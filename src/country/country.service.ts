import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { Country } from './entities/country.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const COUNTRY_PAGINATION_CONFIG: PaginateConfig<Country> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class CountryService {
  constructor(
    @InjectRepository(Country)
    private countryRepository: Repository<Country>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Country>> {
    return paginate(query, this.countryRepository, COUNTRY_PAGINATION_CONFIG);
  }

  async create(createCountryDto: CreateCountryDto) {

    const { ...data } = createCountryDto;

    //check country code exist
    const check = await Country.existsBy({
      code: createCountryDto?.code
    })
    if (check) {
      throw new BadRequestException('country code already.')
    }

    const item = this.countryRepository.create(
      {
        ...data,
      });

    return this.countryRepository.save(item);
  }

  findAll() {
    return this.countryRepository.find();
  }

  async findOne(id: number) {
    const item = await this.countryRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("country not found");

    return item;
  }

  async update(id: number, updateCountryDto: UpdateCountryDto) {
    //check country code exist
    const check = await Country.existsBy({
      code: updateCountryDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Country code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("country not found");

    const { ...data } = updateCountryDto;

    return this.countryRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("country not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.countryRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.countryRepository.findOne({ where: { id } });
  }
}





