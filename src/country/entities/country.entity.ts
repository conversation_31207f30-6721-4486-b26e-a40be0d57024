import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "src/employee/entities/employee.entity";
import { Project } from "src/project/entities/project.entity";

@Entity()
@Unique(['code'])
export class Country extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //Project
    @OneToMany(() => Project, (_) => _.country)
    projects: Array<Project>;

}






