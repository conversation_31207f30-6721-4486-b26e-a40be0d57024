import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Employee, EmployeeActiveEnum, EmployeeStatusEnum } from '../employee/entities/employee.entity';
import { Between, DataSource, In, <PERSON>Than, <PERSON>ThanOrEqual, MoreThanOrEqual, Not } from 'typeorm';
import { EmployeeService } from 'src/employee/employee.service';
import { Leave, LeaveStatusEnum } from 'src/leave/entities/leave.entity';
import { DateTime } from 'luxon';
import { MailerService } from '@nestjs-modules/mailer';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { Ot } from 'src/ot/entities/ot.entity';
import { OtAir } from 'src/ot-air/entities/ot-air.entity';
import { AttandanceConfirmStatusEnum } from 'src/attandances/entities/attandance.entity';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);

  constructor(
    private dataSource: DataSource,
    private readonly employeeService: EmployeeService,
    private readonly mailerService: MailerService,
  ) { }

  // @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  // async increateLeaveQuota() {
  //   const oneYearAgo = new Date();
  //   oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

  //   const employees = await Employee.find({
  //     where: {
  //       registerDate: MoreThan(oneYearAgo),
  //     },
  //   });

  //   console.log(employees);
  // }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async autoGenerateLeave() {
    this.logger.verbose('Start auto Generate Leave');

    const result = await this.employeeService.autoGenerateLeave()

    this.logger.verbose('Auto Generate Leave successfully ' + result.total + ' employees');
  }

  async forceGenerateLeave(date: string) {
    this.logger.verbose('Start Generate Leave' + ' ' + date);

    const result = await this.employeeService.forceGenerateLeave(date)

    this.logger.verbose('Generate Leave successfully ' + result.total + ' employees');
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async resendEmailToRequester() {
    const today = DateTime.local()
    const tomorow = today.plus({ day: 1 });

    const leaves = await Leave.find({
      where: {
        date: Between(tomorow.startOf('day').toJSDate(), tomorow.endOf('day').toJSDate()),
        status: LeaveStatusEnum.OPEN,
      },
      relations: {
        employee: true
      }
    })

    for (const leave of leaves) {
      // Send email in the background
      this.mailerService.sendMail({
        to: leave?.employee?.email,
        subject: 'Leave Application',
        template: 'resend',
        context: {
          fullname: leave?.employee?.fullname,
        },
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async proRate() {
    const now = DateTime.local()
    const oneYearAgo = now.minus({ year: 1 }).toJSDate();

    const employees = await Employee.find({
      where: [
        {
          registerDate: MoreThanOrEqual(oneYearAgo),
          nextQuotaUpdate: LessThanOrEqual(now.toJSDate()),
          employeeStatus: EmployeeStatusEnum.PASSED_PROBATION
        },
        {
          passProbationDate: LessThan(now.toJSDate()),
          nextQuotaUpdate: null,
          employeeStatus: EmployeeStatusEnum.PASSED_PROBATION
        }
      ],
      order: {
        code: 'ASC'
      },
      relations: {
        level: {
          levelType: true
        },
        employeeLeavePermissions: {
          leaveType: true
        }
      }
    })

    const leaveTypes = await LeaveType.find({
      where: {
        proRate: true
      }
    });

    for (const employee of employees) {
      console.log('----------------------------------------------------------------');

      if (employee.totalWorkMonth > 12) {
        console.log('Employee code ' + employee.code + ' worked more 12 month');

        continue;
      }

      if (now.toJSDate() < employee.passProbationDate) {
        console.log('Employee code ' + employee.code + ' not passed probation');

        continue;
      }

      for (const leaveType of leaveTypes) {
        const leavePermission = await LeavePermission.findOne({
          where: {
            ageWork: 1,
            levelType: {
              id: employee.level.levelType.id,
            },
            leaveType: {
              id: leaveType.id
            }
          }
        })

        const totalMonthFromRegister = employee.totalWorkMonth - 2

        let annualLeaveDays = 0;

        if (employee.totalWorkDay >= 90) {
          if (employee.level.levelType.code == 'LVT001') {
            annualLeaveDays = Math.min(leavePermission.qtyDay, 4 + Math.max(0, totalMonthFromRegister - 1));
          } else if (employee.level.levelType.code == 'LVT002') {
            annualLeaveDays = Math.min(leavePermission.qtyDay, 2 + Math.max(0, totalMonthFromRegister - 1));
          }
        }

        await EmployeeLeavePermission.update({
          employee: { id: employee.id },
          leaveType: {
            id: leaveType.id,
          },
          startPeriod: employee.currentStartPeriod,
          endPeriod: employee.currentEndPeriod,
        }, {
          qtyDay: annualLeaveDays,
        })

        console.log('WorkDay ' + employee.totalWorkDay, employee.id, employee.code, employee.firstname, employee.employeeStatus, employee.level.levelType.name, 'totalWorkMonth ' + employee.totalWorkMonth, 'annualLeaveDays ' + annualLeaveDays);
      }

      if (employee.nextQuotaUpdate) {
        await Employee.update(employee.id, {
          nextQuotaUpdate: DateTime.fromSQL(employee.nextQuotaUpdate.toString()).plus({ month: 1 }).toJSDate()
        })
      }

      console.log('----------------------------------------------------------------');
    }
  }

  async gen() {

    //คำนวนวัน next pro rate
    const now = DateTime.local()
    const oneYearAgo = now.minus({ year: 1 }).toJSDate();

    const employees = await Employee.find({
      where: {
        registerDate: MoreThanOrEqual(oneYearAgo),
        // passProbationDate: LessThanOrEqual(now.toJSDate()),
        // employeeStatus: EmployeeStatusEnum.PASSED_PROBATION,
      }
    });

    const query = []
    for (const employee of employees) {
      const passDate = DateTime.fromSQL(employee.registerDate.toString()).plus({ day: 90 }).toLocal().toJSDate();

      const q = Employee.update(employee.id, {
        passProbationDate: passDate,
        nextQuotaUpdate: passDate,
      })

      query.push(q)
    }

    await Promise.all(query)

    // console.log(employees);
    // console.log(employees.length);
  }

  //ot
  @Cron('0 1 9 * *') // ทำงานทุกวันที่ 9 ของทุกเดือน เวลา 01:00 AM
  async sendNotifyRequestOTToEmployee() {

    const employees = await Employee.find({
      where: {
        id: 1161,
        active: EmployeeActiveEnum.PER
      }
    })

    for (const employee of employees) {
      //send email to employee

      this.mailerService.sendMail({
        to: employee?.email,
        subject: 'Submission of OT/OT Air Requests',
        template: 'ot-notify-request-to-employee',
        context: {

        },
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });

    }

  }

  @Cron('0 1 11 * *') // ทำงานทุกวันที่ 11 ของทุกเดือน เวลา 01:00 AM
  async sendNotifyRequestOTToReviewer() {

    let _employeeEmail = []

    //get ot
    const ots = await Ot.find({
      where: {
        status: In(['process']),
      }
    })
    for (const ot of ots) {
      _employeeEmail.push(ot.head?.email)
    }

    // //get ot_air
    // const ot_airs = await OtAir.find({
    //   where: {
    //     status: In(['process']),
    //   }
    // })

    // for (const ot_air of ot_airs) {
    //   _employeeEmail.push(ot_air.head?.email)
    // }

    //send email
    for (const _emEmail of _employeeEmail) {
      //send email to head
      this.mailerService.sendMail({
        to: _emEmail,
        subject: 'Review of OT/OT Air Requests',
        template: 'ot-notify-request-to-review',
        context: {

        },
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });
    }

  }

  @Cron('0 1 12 * *') // ทำงานทุกวันที่ 12 ของทุกเดือน เวลา 01:00 AM
  async sendNotifyRequestOTToApprover() {

    let _employeeEmail = []

    //get ot
    const ots = await Ot.find({
      where: {
        status: In(['head_approved']),
      }
    })
    for (const ot of ots) {
      _employeeEmail.push(ot.approver?.email)
    }

    //get ot_air
    const ot_airs = await OtAir.find({
      where: {
        status: In(['process']),
      }
    })

    for (const ot_air of ot_airs) {
      _employeeEmail.push(ot_air.head?.email)
    }

    //send email
    for (const _emEmail of _employeeEmail) {
      //send email to head
      this.mailerService.sendMail({
        to: _emEmail,
        subject: 'Approve of OT/OT Air Requests',
        template: 'ot-notify-request-to-approver',
        context: {

        },
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });

    }

  }

  @Cron('0 8 1 * *')
  async sendLeaveAtDay1() {
    const today = DateTime.local();

    const emails = await Employee.find()

    if (process.env.NODE_ENV === 'uat') {
      this.mailerService.sendMail({
        to: process.env.SMTP_USERNAME,
        subject: `Your Time Attendance records for [${today.monthLong}, ${today.year}] are now available for review`,
        template: 'leave-noti-1',
        context: {
          month: today.monthLong,
          year: today.year,
        },
        bcc: emails.map((email) => email.email),
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });
    }
  }

  @Cron('0 8 5 * *')
  async sendLeaveAtDay5() {
    const today = DateTime.local();

    const emails = await Employee.find()

    if (process.env.NODE_ENV === 'uat') {
      this.mailerService.sendMail({
        to: process.env.SMTP_USERNAME,
        subject: `Your Time Attendance records for [${today.monthLong}, ${today.year}] are now available for review`,
        template: 'leave-noti-5',
        context: {
          month: today.monthLong,
          year: today.year,
        },
        bcc: emails.map((email) => email.email),
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });
    }

  }

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async generateAttendanceReview() {
    const lastMonth = DateTime.local().minus({ month: 1 });

    console.info('Start generateAttendanceReview : ' + lastMonth.toFormat('yyyy-MM'));
    
    const employees = await Employee.find({
      where: {
        active: Not(EmployeeActiveEnum.RES)
      }
    });

    for (const employee of employees) {
      const attendances = await this.employeeService.getEmployeeAttdance1(employee.id, lastMonth.year, lastMonth.month);

      await this.employeeService.updateEmployeeAttdance(employee.id, {
        attendance: attendances.data,
        year: lastMonth.year,
        month: lastMonth.month,
        confirmStatus: AttandanceConfirmStatusEnum.DRAFT
      });
    }

    console.info('End generateAttendanceReview : ' + lastMonth.toFormat('yyyy-MM'));
  }
}
