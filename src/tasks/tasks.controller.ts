import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { ApiBody } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('task')
// @Auth()
export class TaskController {
  constructor(private readonly taskService: TasksService) { }

  @Post('/attendance')
  @ApiBody({ schema: { properties: { date: { type: 'string', example: '' } } } })
  attendance(@Body('date') date: string) {
    if (!date) {
      date = DateTime.local().toFormat('yyyy-MM-dd');
    }

    return this.taskService.forceGenerateLeave(date);
  }

  @Post('/prorate-cal')
  prorate() {
    return this.taskService.generateAttendanceReview();
  }
}

