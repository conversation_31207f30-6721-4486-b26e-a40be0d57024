
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Choice } from "./choice.entity";
import { ApplicantQuestion } from "../../applicant_question/entities/applicant_question.entity";

@Entity()
export class Question extends CustomBaseEntity {
    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    @OneToMany(() => Choice, (_) => _.question)
    choices: Array<Choice>;

    @OneToMany(() => ApplicantQuestion, (_) => _.question)
    applicantQuestions: ApplicantQuestion[];
}





