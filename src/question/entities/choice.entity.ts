
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Question } from "./question.entity";
import { ApplicantQuestion } from "../../applicant_question/entities/applicant_question.entity";

@Entity()
export class Choice extends CustomBaseEntity {
    @Column()
    name: string;

    @Column({ nullable: true })
    value: string;

    @Column({ default: true })
    active: boolean

    @ManyToOne(() => Question, (_) => _.choices)
    @JoinColumn()
    question: Question;

    @OneToMany(() => ApplicantQuestion, (_) => _.choice)
    applicantQuestions: ApplicantQuestion[];
}





