import { <PERSON>du<PERSON> } from '@nestjs/common';
import { QuestionService } from './question.service';
import { QuestionController } from './question.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Question } from './entities/question.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Question])
],
  controllers: [QuestionController],
  providers: [QuestionService],
})
export class QuestionModule {}
