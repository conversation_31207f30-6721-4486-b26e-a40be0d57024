import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { Question } from './entities/question.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const QUESTION_PAGINATION_CONFIG: PaginateConfig<Question> = {
  relations: [],
  sortableColumns: ['id', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['name']
};
@Injectable()
export class QuestionService {
  constructor(
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Question>> {
    return paginate(query, this.questionRepository, QUESTION_PAGINATION_CONFIG);
  }

  async create(createQuestionDto: CreateQuestionDto) {

    const { ...data } = createQuestionDto;

    const item = this.questionRepository.create(
      {
        ...data,
      });

    return this.questionRepository.save(item);
  }

  findAll(query) {

    return this.questionRepository.find({
      relations: {
        choices: true
      },
      where: {
        active: true,
      },
    });
  }

  async findOne(id: number) {
    const item = await this.questionRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("question not found");

    return item;
  }

  async update(id: number, updateQuestionDto: UpdateQuestionDto) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("question not found");

    const { ...data } = updateQuestionDto;

    return this.questionRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("question not found");

    await this.questionRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.questionRepository.findOne({
      relations: [],
      where: { id }
    });
  }
}


