import { Column, <PERSON><PERSON>ty, Index, <PERSON>inC<PERSON>umn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Group } from "../../group/entities/group.entity";
import { Employee } from "../../employee/entities/employee.entity";
import { PersonalForm } from "../../personal-form/entities/personal-form.entity";
import { OtCondition } from "../../ot-condition/entities/ot-condition.entity";


@Entity()
@Unique(['code'])
export class Department extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //group
    @ManyToOne(() => Group, (_) => _.departments)
    @JoinColumn({ name: 'group_id' })
    group: Group;

    //employee
    @OneToMany(() => Employee, (_) => _.department)
    employees: Array<Employee>;

    @OneToMany(() => PersonalForm, (_) => _.department)
    personalForms: Array<PersonalForm>;

    @OneToMany(() => OtCondition, (_) => _.department)
    otConditions: OtCondition[];
}


