import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { Department } from './entities/department.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const DEPARTMENT_PAGINATION_CONFIG: PaginateConfig<Department> = {
  relations: ['group'],
  sortableColumns: ['name', 'active', 'group.code', 'group.name'],
  select: ['code', 'active', 'name', 'createdAt', 'group.id', 'group.code', 'group.name'],
  searchableColumns: ['code', 'name'],
  filterableColumns: {
    group: [FilterOperator.EQ],
  },

};
@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Department>> {
    return paginate(query, this.departmentRepository, DEPARTMENT_PAGINATION_CONFIG);
  }

  async create(createDepartmentDto: CreateDepartmentDto) {
    const { groupId, ...data } = createDepartmentDto;

    //check department code exist
    const check = await Department.existsBy({
      code: createDepartmentDto?.code
    })
    if (check) {
      throw new BadRequestException('Department code already.')
    }

    const item = this.departmentRepository.create(
      {
        ...data,
        group: { id: groupId }
      });

    return this.departmentRepository.save(item);
  }

  findAll(query) {
    const groupId = query.groupId

    return this.departmentRepository.find({
      relations: ['group'],
      where: {
        group: {
          id: groupId ? groupId : null,
        }
      }
    });
  }

  async findOne(id: number) {
    const item = await this.departmentRepository.findOne({
      relations: ['group'],
      where: {
        id
      }
    });

    if (!item) throw new NotFoundException("department not found");

    return item;
  }

  async update(id: number, updateDepartmentDto: UpdateDepartmentDto) {
    //check department code exist
    const check = await Department.existsBy({
      code: updateDepartmentDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Department code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("department not found");

    const { groupId, ...data } = updateDepartmentDto;

    return this.departmentRepository.update(id, {
      ...data,
      group: {
        id: groupId,
      }
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("department not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.departmentRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.departmentRepository.findOne({ where: { id } });
  }
}
