import * as argon2 from 'argon2';

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { FilterOperator, paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Not } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

export const USER_PAGINATION_CONFIG: PaginateConfig<User> = {
  sortableColumns: ['username', 'firstname', 'lastname', 'roles.name', 'createdAt'],
  select: ['id', 'username', 'firstname', 'lastname', 'active', 'roles.name', 'createdAt'],
  searchableColumns: ['username', 'firstname', 'lastname'],
  filterableColumns: {
    // roles: [FilterOperator.EQ]
  },
  relations: ['roles'],
};

@Injectable()
export class UserService {
  async datatables(query: PaginateQuery): Promise<Paginated<User>> {
    return paginate(query, User.getRepository(), USER_PAGINATION_CONFIG);
  }

  async create(createUserDto: CreateUserDto) {
    const checkUsername = await User.existsBy({ username: createUserDto.username })
    if (checkUsername) {
      throw new BadRequestException('Username already exists');
    }

    const hash = await argon2.hash(createUserDto.password);

    const user = await User.create({
      ...createUserDto,
      passwordHash: hash,
      roles: createUserDto.roleIds.map(roleId => ({ id: roleId }))
    }).save();

    return user;
  }

  findAll() {
    return User.find();
  }

  async findOne(id: number) {
    const user = await User.findOne({
      where: { id },
      relations: {
        roles: {
          permissions: true
        }
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    delete user.passwordHash;

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    const checkUsernameIgnoeSelf = await User.existsBy({ id: Not(id), username: updateUserDto.username });
    if (checkUsernameIgnoeSelf) {
      throw new BadRequestException('Username already exists');
    }

    const user = await User.findOne({
      where: { id },
      relations: {
        roles: {
          permissions: true
        }
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    user.username = updateUserDto.username;
    user.firstname = updateUserDto.firstname;
    user.lastname = updateUserDto.lastname;
    user.active = updateUserDto.active;
    user.roles = updateUserDto.roleIds.map(roleId => ({ id: roleId })) as any;

    await user.save();

    return this.findOne(id);
  }

  async remove(id: number) {
    const user = await User.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.username += randomUUID() + user.username;
    await user.save()
    await user.softRemove();
  }

  async forceChangePassword(userId: number, password: string) {
    const user = await User.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hash = await argon2.hash(password);

    user.passwordHash = hash;

    await user.save()

    delete user.passwordHash;

    return user;
  }
}
