import * as argon2 from 'argon2';

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Not } from 'typeorm';
import { randomUUID } from 'crypto';

export const USER_PAGINATION_CONFIG: PaginateConfig<User> = {
  sortableColumns: ['username', 'firstname', 'lastname', 'roles'],
  select: ['id', 'username', 'firstname', 'lastname', 'active', 'roles','createdAt'],
  searchableColumns: ['username', 'firstname', 'lastname', 'roles'],
  filterableColumns: {},
};

@Injectable()
export class UserService {
  async datatables(query: PaginateQuery): Promise<Paginated<User>> {
    return paginate(query, User.getRepository(), USER_PAGINATION_CONFIG);
  }

  async create(createUserDto: CreateUserDto) {
    const checkUsername = await User.existsBy({ username: createUserDto.username })
    if (checkUsername) {
      throw new BadRequestException('Username already exists');
    }

    const hash = await argon2.hash(createUserDto.password);

    const user = await User.create({
      ...createUserDto,
      passwordHash: hash
    }).save();

    return user;
  }

  findAll() {
    return User.find();
  }

  async findOne(id: number) {
    const user = await User.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    delete user.passwordHash;

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    const checkUsernameIgnoeSelf = await User.existsBy({ id: Not(id), username: updateUserDto.username });
    if (checkUsernameIgnoeSelf) {
      throw new BadRequestException('Username already exists');
    }

    await User.update(id, {
      username: updateUserDto.username,
      firstname: updateUserDto.firstname,
      lastname: updateUserDto.lastname,
      roles: updateUserDto.roles,
    });

    return this.findOne(id);
  }

  async remove(id: number) {
    const user = await User.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.username += randomUUID() + user.username;
    await user.save()
    await user.softRemove();
  }

  async forceChangePassword(userId: number, password: string) {
    const user = await User.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hash = await argon2.hash(password);

    user.passwordHash = hash;

    await user.save()

    delete user.passwordHash;

    return user;
  }
}
