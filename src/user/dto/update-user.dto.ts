import { PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { IsNotEmpty } from 'class-validator';
import { UserRole } from '../entities/user.entity';

export class UpdateUserDto {
    @IsNotEmpty()
    readonly username: string;

    @IsNotEmpty()
    readonly firstname: string;

    @IsNotEmpty()
    readonly lastname: string;

    @IsNotEmpty()
    readonly roles: UserRole[];

    @IsNotEmpty()
    readonly active: boolean;
}
