import { IsNotEmpty } from "class-validator";
import { UserRole } from "../entities/user.entity";

export class CreateUserDto {
    @IsNotEmpty()
    readonly username: string;

    @IsNotEmpty()
    readonly password: string;

    @IsNotEmpty()
    readonly firstname: string;

    @IsNotEmpty()
    readonly lastname: string;

    @IsNotEmpty()
    readonly roles: UserRole[];

    @IsNotEmpty()
    readonly active: boolean;
}
