import { Column, Entity, Index, JoinTable, ManyToMany, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Ot } from "../../ot/entities/ot.entity";
import { OtAir } from "../../ot-air/entities/ot-air.entity";
import { Role } from "../../role/entities/role.entity";

// export enum UserRole {
//     ADMIN = 'ADMIN',
//     ADMIN_HR = 'ADMIN_HR',
//     HR_ASSIT = 'HR_ASSIT',
//     ADMIN_OT = 'ADMIN_OT',
//     OT_RPT = 'OT_RPT',
//     OT_PRJ = 'OT_PRJ',
//     ADMIN_OTA = 'ADMIN_OTA',
//     ADMIN_TA = 'ADMIN_TA',
//     ADMIN_CL = 'ADMIN_CL',
//     USER_CL = 'USER_CL',
// }

@Entity()
export class User extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    username: string;

    @Column()
    @Exclude()
    passwordHash: string;

    @Column()
    firstname: string;

    @Column()
    lastname: string;

    @ManyToMany(() => Role, (_) => _.users)
    @JoinTable()
    roles: Role[];


    @Column({ default: true })
    active: boolean

    @Expose()
    get fullname(): string {
        return `${this.firstname} ${this.lastname}`;
    }

    // @OneToMany(() => PersonalForm, (_) => _.organisedBy)
    // personalFormsOrganise: PersonalForm[];

    @OneToMany(() => Ot, (_) => _.admin)
    ots: Array<Ot>;

    @OneToMany(() => OtAir, (_) => _.admin)
    otAirs: Array<OtAir>;
}
