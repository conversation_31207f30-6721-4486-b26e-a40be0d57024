import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus, Put, Req } from '@nestjs/common';
import { USER_PAGINATION_CONFIG, UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { ForceChangePasswordDto } from './dto/force-change-password.dto';
import { Request } from 'express';

@Controller('user')
@ApiTags('แอดมิน')
@Auth()
export class UserController {
  constructor(private readonly userService: UserService) { }

  @Put('/:id/force-change-password')
  forceChangePassword(@Param('id') id: string, @Body() payload: ForceChangePasswordDto) {
    return this.userService.forceChangePassword(+id, payload.password)
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(USER_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.userService.datatables(query);
  }

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get()
  findAll() {
    return this.userService.findAll();
  }

  @Get('/me')
  myInfo(@Req() req: Request) {
    const currentuserId = req.user['sub'];
    return this.userService.findOne(currentuserId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(+id, updateUserDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id') id: string) {
    return this.userService.remove(+id);
  }
}
