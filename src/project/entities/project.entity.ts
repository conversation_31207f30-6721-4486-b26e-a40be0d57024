import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Ot } from "src/ot/entities/ot.entity";
import { OtAir } from "src/ot-air/entities/ot-air.entity";
import { Employee } from "src/employee/entities/employee.entity";
import { Country } from "src/country/entities/country.entity";

@Entity()
@Unique(['code'])
export class Project extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({
        type: "enum",
        enum: ["process", "end"],
        default: 'process'
    })
    status: string


    @Column({ default: true })
    active: boolean

    //Ot
    @OneToMany(() => Ot, (_) => _.project)
    ots: Array<Ot>;

    @OneToMany(() => OtAir, (_) => _.project)
    otAirs: Array<OtAir>;

    //employee
    @ManyToOne(() => Employee, (_) => _.projects)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;

    //country
    @ManyToOne(() => Country, (_) => _.projects)
    @JoinColumn({ name: 'country_id' })
    country: Country;

}




