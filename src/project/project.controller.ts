import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query } from '@nestjs/common';

import { PROJECT_PAGINATION_CONFIG } from './project.service';
import { ProjectService } from './project.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';


@Controller('project')
@ApiTags('โครงการ')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PROJECT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.projectService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createProjectDto: CreateProjectDto) {
    return this.projectService.create(createProjectDto);
  }

  @Get()
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'countryId', required: false })
  findAll(@Query() query) {
    return this.projectService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.projectService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateProjectDto: UpdateProjectDto) {
    return this.projectService.update(+id, updateProjectDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.projectService.remove(+id);
  }
}


