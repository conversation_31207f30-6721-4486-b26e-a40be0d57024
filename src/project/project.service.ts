import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { Project } from './entities/project.entity';
import { DataSource, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Country } from 'src/country/entities/country.entity';
import { Employee } from 'src/employee/entities/employee.entity';

export const PROJECT_PAGINATION_CONFIG: PaginateConfig<Project> = {
  relations: ['employee', 'country'],
  sortableColumns: ['id', 'code', 'name', 'status', 'active'],
  select: ['id', 'code', 'name', 'status', 'active', 'createdAt'
    , 'country.id', 'country.code', 'country.name'
    , 'employee.id', 'employee.code', 'employee.firstname', 'employee.lastname'
  ],
  searchableColumns: ['code', 'name', 'status']
};
@Injectable()
export class ProjectService {
  constructor(
    @InjectRepository(Project)
    private projectRepository: Repository<Project>,

    @InjectRepository(Country)
    private countryRepository: Repository<Country>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,

    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Project>> {
    return paginate(query, this.projectRepository, PROJECT_PAGINATION_CONFIG);
  }

  async create(createProjectDto: CreateProjectDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { employeeId, countryId } = createProjectDto;

      //check project code exist
      const check = await Project.existsBy({
        code: createProjectDto?.code
      })
      if (check) {
        throw new BadRequestException('project code already.')
      }

      const [country, employee] = await Promise.all([
        this.countryRepository.findOne({ where: { id: countryId } }),
        this.employeeRepository.findOne({ where: { id: employeeId } }),
      ]);

      if (!country) throw new NotFoundException("country not found");
      if (!employee) throw new NotFoundException("employee not found");


      const item = new Project()

      item.code = createProjectDto.code
      item.name = createProjectDto.name
      item.status = 'process'
      item.country = country
      item.employee = employee

      await queryRunner.manager.save(item)

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll(query) {

    const employeeId = query.employeeId;
    const countryId = query.countryId;

    return this.projectRepository.find({
      relations: ['employee', 'country'],
      where: {
        active: true,
        employee: {
          id: employeeId ? employeeId : null,
        },
        country: {
          id: countryId ? countryId : null,
        },
      },
    });
  }

  async findOne(id: number) {
    const item = await this.projectRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("project not found");

    return item;
  }

  async update(id: number, updateProjectDto: UpdateProjectDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { employeeId, countryId } = updateProjectDto;

      //check project code exist
      const check = await Project.existsBy({
        code: updateProjectDto?.code,
        id: Not(id)
      })
      if (check) {
        throw new BadRequestException('project code already.')
      }

      const [country, employee] = await Promise.all([
        this.countryRepository.findOne({ where: { id: countryId } }),
        this.employeeRepository.findOne({ where: { id: employeeId } }),
      ]);

      if (!country) throw new NotFoundException("country not found");
      if (!employee) throw new NotFoundException("employee not found");

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("project not found");

      item.code = updateProjectDto.code
      item.name = updateProjectDto.name
      item.status = updateProjectDto.status
      item.country = country
      item.employee = employee

      await queryRunner.manager.save(item)

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("project not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.projectRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.projectRepository.findOne({
      relations: ['employee', 'country'],
      where: { id }
    });
  }
}


