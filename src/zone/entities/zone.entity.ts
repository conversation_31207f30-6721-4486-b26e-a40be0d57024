import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Ot } from "src/ot/entities/ot.entity";
import { OtAir } from "src/ot-air/entities/ot-air.entity";

@Entity()
@Unique(['code'])
export class Zone extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;


    @Column({ default: true })
    active: boolean

    //
    @OneToMany(() => OtAir, (_) => _.zone)
    otAirs: Array<OtAir>;

}





