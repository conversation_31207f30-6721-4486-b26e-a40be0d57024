
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateZoneDto } from './dto/create-zone.dto';
import { UpdateZoneDto } from './dto/update-zone.dto';
import { Zone } from './entities/zone.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const ZONE_PAGINATION_CONFIG: PaginateConfig<Zone> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class ZoneService {
  constructor(
    @InjectRepository(Zone)
    private zoneRepository: Repository<Zone>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Zone>> {
    return paginate(query, this.zoneRepository, ZONE_PAGINATION_CONFIG);
  }

  async create(createZoneDto: CreateZoneDto) {

    const { ...data } = createZoneDto;

    //check zone code exist
    const check = await Zone.existsBy({
      code: createZoneDto?.code
    })
    if (check) {
      throw new BadRequestException('zone code already.')
    }

    const item = this.zoneRepository.create(
      {
        ...data,
      });

    return this.zoneRepository.save(item);
  }

  findAll() {
    return this.zoneRepository.find();
  }

  async findOne(id: number) {
    const item = await this.zoneRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("zone not found");

    return item;
  }

  async update(id: number, updateZoneDto: UpdateZoneDto) {
    //check zone code exist
    const check = await Zone.existsBy({
      code: updateZoneDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Zone code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("zone not found");

    const { ...data } = updateZoneDto;

    return this.zoneRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("zone not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.zoneRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.zoneRepository.findOne({ where: { id } });
  }
}



