import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantReferenceService } from './applicant_reference.service';

describe('ApplicantReferenceService', () => {
  let service: ApplicantReferenceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicantReferenceService],
    }).compile();

    service = module.get<ApplicantReferenceService>(ApplicantReferenceService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
