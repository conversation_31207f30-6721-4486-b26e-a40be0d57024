import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantReferenceService } from './applicant_reference.service';
import { CreateApplicantReferenceDto } from './dto/create-applicant_reference.dto';
import { UpdateApplicantReferenceDto } from './dto/update-applicant_reference.dto';

@Controller('applicant-reference')
export class ApplicantReferenceController {
  constructor(private readonly applicantReferenceService: ApplicantReferenceService) {}

  @Post()
  create(@Body() createApplicantReferenceDto: CreateApplicantReferenceDto) {
    return this.applicantReferenceService.create(createApplicantReferenceDto);
  }

  @Get()
  findAll() {
    return this.applicantReferenceService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantReferenceService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantReferenceDto: UpdateApplicantReferenceDto) {
    return this.applicantReferenceService.update(+id, updateApplicantReferenceDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantReferenceService.remove(+id);
  }
}
