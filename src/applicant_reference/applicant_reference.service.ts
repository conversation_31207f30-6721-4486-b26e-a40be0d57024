import { Injectable } from '@nestjs/common';
import { CreateApplicantReferenceDto } from './dto/create-applicant_reference.dto';
import { UpdateApplicantReferenceDto } from './dto/update-applicant_reference.dto';

@Injectable()
export class ApplicantReferenceService {
  create(createApplicantReferenceDto: CreateApplicantReferenceDto) {
    return 'This action adds a new applicantReference';
  }

  findAll() {
    return `This action returns all applicantReference`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantReference`;
  }

  update(id: number, updateApplicantReferenceDto: UpdateApplicantReferenceDto) {
    return `This action updates a #${id} applicantReference`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantReference`;
  }
}
