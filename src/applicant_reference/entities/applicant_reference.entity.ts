import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Applicant } from "src/applicant/entities/applicant.entity";

@Entity()
export class ApplicantReference extends CustomBaseEntity {
    @Column("text", { nullable: true })
    name: string;

    @Column("text", { nullable: true })
    companyName: string;

    @Column("text", { nullable: true })
    tel: string;

    @Column({ default: true })
    active: boolean

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantReferences)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;
}

