import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantReferenceController } from './applicant_reference.controller';
import { ApplicantReferenceService } from './applicant_reference.service';

describe('ApplicantReferenceController', () => {
  let controller: ApplicantReferenceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantReferenceController],
      providers: [ApplicantReferenceService],
    }).compile();

    controller = module.get<ApplicantReferenceController>(ApplicantReferenceController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
