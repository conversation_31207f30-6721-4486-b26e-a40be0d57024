import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ContactActivityService } from './contact_activity.service';
import { CreateContactActivityDto } from './dto/create-contact_activity.dto';
import { UpdateContactActivityDto } from './dto/update-contact_activity.dto';

@Controller('contact-activity')
export class ContactActivityController {
  constructor(private readonly contactActivityService: ContactActivityService) {}

  @Post()
  create(@Body() createContactActivityDto: CreateContactActivityDto) {
    return this.contactActivityService.create(createContactActivityDto);
  }

  @Get()
  findAll() {
    return this.contactActivityService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.contactActivityService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateContactActivityDto: UpdateContactActivityDto) {
    return this.contactActivityService.update(+id, updateContactActivityDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.contactActivityService.remove(+id);
  }
}
