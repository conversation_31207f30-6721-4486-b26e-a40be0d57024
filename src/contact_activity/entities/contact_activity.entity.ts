import { Column, <PERSON><PERSON>ty, Index, Jo<PERSON><PERSON><PERSON>umn, <PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Contact } from "src/contact/entities/contact.entity";
import { Activity } from "src/activity/entities/activity.entity";
import { Employee } from "src/employee/entities/employee.entity";

@Entity()
export class ContactActivity extends CustomBaseEntity {

    // Employee
    @ManyToOne(() => Employee, (_) => _.contactActivities)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;

    //Contact
    @ManyToOne(() => Contact, (_) => _.contactActivities)
    @JoinColumn({ name: 'contact_id' })
    contact: Contact;

    //Activity
    @ManyToMany(() => Activity, (_) => _.contactActivities)
    @JoinTable()
    activities: Activity[];

    @Column({ default: true })
    active: boolean
}
