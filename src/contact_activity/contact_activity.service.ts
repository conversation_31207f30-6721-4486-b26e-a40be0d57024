import { Injectable } from '@nestjs/common';
import { CreateContactActivityDto } from './dto/create-contact_activity.dto';
import { UpdateContactActivityDto } from './dto/update-contact_activity.dto';

@Injectable()
export class ContactActivityService {
  create(createContactActivityDto: CreateContactActivityDto) {
    return 'This action adds a new contactActivity';
  }

  findAll() {
    return `This action returns all contactActivity`;
  }

  findOne(id: number) {
    return `This action returns a #${id} contactActivity`;
  }

  update(id: number, updateContactActivityDto: UpdateContactActivityDto) {
    return `This action updates a #${id} contactActivity`;
  }

  remove(id: number) {
    return `This action removes a #${id} contactActivity`;
  }
}
