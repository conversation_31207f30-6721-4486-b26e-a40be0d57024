import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query, Req } from '@nestjs/common';
import { OT_PAGINATION_CONFIG } from './ot.service';
import { OtService } from './ot.service';
import { CreateOtDto } from './dto/create-ot.dto';
import { UpdateOtDto } from './dto/update-ot.dto';

import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags, ApiQuery, ApiBody, ApiProperty } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UpdateStatusOtDto } from './dto/update-status-ot.dto';
import { Request } from 'express';


export class getOtTimeDto {
  @ApiProperty({
    description: 'date',
    required: false,
    type: String,
  })
  date?: string;

  @ApiProperty({
    description: 'employeeId',
    required: false,
    type: String,
  })
  employeeId?: number;
}

@Controller('ot')
@ApiTags('โอที')
@Auth()
export class OtController {
  constructor(private readonly otService: OtService) { }


  // @Get('/wait-approve')
  // @HttpCode(HttpStatus.OK)
  // @ApiPaginationQuery(OT_PAGINATION_CONFIG)
  // waitApprove(@Req() req: Request, @Paginate() query: PaginateQuery) {
  //   const headId = req.user['sub'];

  //   query.filter = {
  //     ...query.filter,
  //     'head.id': headId + ""
  //   }

  //   return this.otService.datatables(query);
  // }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(OT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.otService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Employee, AuthRole.Admin, AuthRole.HrManager)
  create(@Req() req: Request, @Body() createOtDto: CreateOtDto) {
    // const employeeId = req.user['sub'];
    return this.otService.create(createOtDto);
  }

  @Get()
  @ApiQuery({ name: 'projectId', required: false })
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'headId', required: false })
  @ApiQuery({ name: 'year', required: false })
  @ApiQuery({ name: 'month', required: false })
  findAll(@Query() query) {
    return this.otService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.otService.findOne(+id);
  }

  @Put(':id')
  // @Roles(AuthRole.Admin)
  @Roles(AuthRole.Employee, AuthRole.Admin, AuthRole.HrManager)
  update(@Param('id', ParseIntPipe) id: string, @Req() req: Request, @Body() updateOtDto: UpdateOtDto) {
    // const employeeId = req.user['sub'];

    return this.otService.update( +id, updateOtDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Employee, AuthRole.Admin, AuthRole.HrManager)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.otService.remove(+id);
  }

  @Put(':id/approve')
  @Roles(AuthRole.Employee, AuthRole.Admin, AuthRole.HrManager)
  updateStatus(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Body() updateStatusOtDto: UpdateStatusOtDto) {
    const employeeId = req.user['sub'];

    return this.otService.updateStatus(+id, updateStatusOtDto);
  }

  @Post('/get_ot_time')
  @ApiBody({ description: 'get Ot Time request', type: getOtTimeDto })
  getTimeOt(@Body() body: getOtTimeDto, @Req() req: Request) {
    return this.otService.getTimeOt(body);
  }
}



