import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Employee } from "../../employee/entities/employee.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";
import { Project } from "../../project/entities/project.entity";
import { User } from "../../user/entities/user.entity";

export enum OtStatus {
    open = "open",
    process = "process",
    head_approved = "head_approved",
    approved = "approved",
    cancel = "cancel",
    reject = "reject",
}

@Entity()
@Unique(['code'])
export class Ot extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column("date", { nullable: true })
    date: Date

    @Column("time", { nullable: true })
    timeStart: string

    @Column("time", { nullable: true })
    timeEnd: string

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyHour: number;

    @Column("text", { nullable: true })
    detail: string;

    @Column({
        type: "enum",
        enum: OtStatus,
        default: OtStatus.open
    })
    status: OtStatus;

    @Column("timestamp", { nullable: true })
    statusDate: Date;

    @Column('text', { nullable: true })
    statusRemark: string


    @Column("timestamp", { nullable: true })
    statusApproverDate: Date;

    @Column('text', { nullable: true })
    statusApproverRemark: string


    @Column({ default: true })
    active: boolean


    isHoliday: boolean
    isIssue: boolean
    attendance: any

    //employee
    @ManyToOne(() => Employee, (_) => _.leaves)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;


    //project
    @ManyToOne(() => Project, (_) => _.ots)
    @JoinColumn({ name: 'project_id' })
    project: Project;

    //head
    @ManyToOne(() => Employee, (_) => _.ots)
    @JoinColumn({ name: 'head_id' })
    head: Employee;

    //approver
    @ManyToOne(() => Employee, (_) => _.ots)
    @JoinColumn({ name: 'approver_id' })
    approver: Employee;

    @ManyToOne(() => User, (_) => _.ots)
    @JoinColumn({ name: 'admin_id' })
    admin: User;
}




