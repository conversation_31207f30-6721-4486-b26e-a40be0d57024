import { Modu<PERSON> } from '@nestjs/common';
import { OtService } from './ot.service';
import { OtController } from './ot.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Ot } from './entities/ot.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { Project } from 'src/project/entities/project.entity';
import { EmployeeService } from 'src/employee/employee.service';

@Module({
  imports: [TypeOrmModule.forFeature([Ot,Employee,Project])],
  controllers: [OtController],
  providers: [OtService,EmployeeService],
})
export class OtModule {}
