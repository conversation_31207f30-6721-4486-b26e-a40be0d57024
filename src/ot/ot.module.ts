import { Module } from '@nestjs/common';
import { OtService } from './ot.service';
import { OtController } from './ot.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Ot } from './entities/ot.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { Project } from 'src/project/entities/project.entity';
import { EmployeeService } from 'src/employee/employee.service';
import { OtConditionService } from 'src/ot-condition/ot-condition.service';
import { OtCondition } from 'src/ot-condition/entities/ot-condition.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Ot, Employee, Project, OtCondition])],
  controllers: [OtController],
  providers: [OtService, EmployeeService, OtConditionService],
})
export class OtModule {}
