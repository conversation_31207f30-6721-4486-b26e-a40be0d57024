import { <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { OtConditionType } from "../entities/ot-condition.entity";

export class CreateOtConditionDto {
  @ApiProperty({ description: 'รหัสแผนก', example: 1 })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสแผนก' })
  @IsNumber({}, { message: 'รหัสแผนกต้องเป็นตัวเลข' })
  @Type(() => Number)
  readonly departmentId: number;

  @ApiProperty({ description: 'รหัสประเภทพนักงาน', example: 1 })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสประเภทพนักงาน' })
  @IsNumber({}, { message: 'รหัสประเภทพนักงานต้องเป็นตัวเลข' })
  @Type(() => Number)
  readonly employeeTypeId: number;

  @ApiProperty({
    description: 'ประเภทของเงื่อนไข OT',
    enum: OtConditionType,
    example: OtConditionType.WORK
  })
  @IsNotEmpty({ message: 'กรุณาระบุประเภทของเงื่อนไข OT' })
  @IsEnum(OtConditionType, { message: 'ประเภทของเงื่อนไข OT ไม่ถูกต้อง' })
  readonly type: OtConditionType;

  @ApiProperty({ description: 'จำนวนนาทีขั้นต่ำสำหรับ OT', example: 30 })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวนนาทีขั้นต่ำสำหรับ OT' })
  @IsNumber({}, { message: 'จำนวนนาทีขั้นต่ำต้องเป็นตัวเลข' })
  @Min(0, { message: 'จำนวนนาทีขั้นต่ำต้องมากกว่าหรือเท่ากับ 0' })
  @Type(() => Number)
  readonly qtyOtMin: number;

  @ApiProperty({ description: 'หักเวลาพักหรือไม่', example: false })
  @IsNotEmpty({ message: 'กรุณาระบุว่าต้องหักเวลาพักหรือไม่' })
  @IsBoolean({ message: 'ค่าการหักเวลาพักต้องเป็น true หรือ false' })
  @Type(() => Boolean)
  readonly deduct: boolean;

  @ApiProperty({ description: 'สถานะการใช้งาน', example: true })
  @IsOptional()
  @IsBoolean({ message: 'สถานะการใช้งานต้องเป็น true หรือ false' })
  @Type(() => Boolean)
  readonly active?: boolean;
}
