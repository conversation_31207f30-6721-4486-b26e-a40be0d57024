import { IsNotEmpty } from "class-validator";
import { OtConditionType } from "../entities/ot-condition.entity";

export class CreateOtConditionDto {
  @IsNotEmpty()
  readonly departmentId: number;

  @IsNotEmpty()
  readonly employeeTypeId: number;

  @IsNotEmpty()
  readonly type: OtConditionType;
  
  @IsNotEmpty()
  readonly qtyOtMin: number;
  
  @IsNotEmpty()
  readonly deduct: boolean;
  
  @IsNotEmpty()
  readonly active: boolean;
}
