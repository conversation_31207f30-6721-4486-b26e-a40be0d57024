import { Test, TestingModule } from '@nestjs/testing';
import { OtConditionController } from './ot-condition.controller';
import { OtConditionService } from './ot-condition.service';
import { OtConditionType } from './entities/ot-condition.entity';

describe('OtConditionController', () => {
  let controller: OtConditionController;
  let service: OtConditionService;

  const mockOtConditionService = {
    datatables: jest.fn(),
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OtConditionController],
      providers: [
        {
          provide: OtConditionService,
          useValue: mockOtConditionService,
        },
      ],
    }).compile();

    controller = module.get<OtConditionController>(OtConditionController);
    service = module.get<OtConditionService>(OtConditionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('datatables', () => {
    it('should return paginated ot conditions', async () => {
      const query = { page: 1, limit: 10 };
      const expectedResult = {
        data: [{ id: 1, type: OtConditionType.WORK }],
        meta: { totalItems: 1, itemCount: 1, itemsPerPage: 10, totalPages: 1, currentPage: 1 }
      };

      mockOtConditionService.datatables.mockResolvedValue(expectedResult);

      const result = await controller.datatables(query as any);

      expect(service.datatables).toHaveBeenCalledWith(query);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should create a new ot condition', async () => {
      const createDto = {
        departmentId: 1,
        employeeTypeId: 1,
        type: OtConditionType.WORK,
        qtyOtMin: 30,
        deduct: false,
        active: true,
      };
      const expectedResult = { id: 1, ...createDto };

      mockOtConditionService.create.mockResolvedValue(expectedResult);

      const result = await controller.create(createDto);

      expect(service.create).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findAll', () => {
    it('should return all ot conditions', async () => {
      const query = { departmentId: 1 };
      const expectedResult = [
        { id: 1, type: OtConditionType.WORK },
        { id: 2, type: OtConditionType.OFF },
      ];

      mockOtConditionService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(expectedResult);
    });

    it('should return all ot conditions without query', async () => {
      const expectedResult = [
        { id: 1, type: OtConditionType.WORK },
        { id: 2, type: OtConditionType.OFF },
      ];

      mockOtConditionService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll({});

      expect(service.findAll).toHaveBeenCalledWith({});
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOne', () => {
    it('should return ot condition by id', async () => {
      const expectedResult = { id: 1, type: OtConditionType.WORK };

      mockOtConditionService.findOne.mockResolvedValue(expectedResult);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should update ot condition', async () => {
      const updateDto = {
        departmentId: 1,
        employeeTypeId: 1,
        type: OtConditionType.WORK,
        qtyOtMin: 45,
        deduct: true,
        active: true,
      };
      const expectedResult = { id: 1, ...updateDto };

      mockOtConditionService.update.mockResolvedValue(expectedResult);

      const result = await controller.update('1', updateDto);

      expect(service.update).toHaveBeenCalledWith(1, updateDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('remove', () => {
    it('should remove ot condition', async () => {
      const expectedResult = { message: 'OT condition deleted successfully' };

      mockOtConditionService.remove.mockResolvedValue(expectedResult);

      const result = await controller.remove('1');

      expect(service.remove).toHaveBeenCalledWith(1);
      expect(result).toEqual(expectedResult);
    });
  });
});
