import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { OtConditionService } from './ot-condition.service';
import { OtCondition, OtConditionType } from './entities/ot-condition.entity';

describe('OtConditionService', () => {
  let service: OtConditionService;
  let repository: Repository<OtCondition>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    softDelete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OtConditionService,
        {
          provide: getRepositoryToken(OtCondition),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<OtConditionService>(OtConditionService);
    repository = module.get<Repository<OtCondition>>(getRepositoryToken(OtCondition));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    const createDto = {
      departmentId: 1,
      employeeTypeId: 1,
      type: OtConditionType.WORK,
      qtyOtMin: 30,
      deduct: false,
      active: true,
    };

    it('should create a new ot condition successfully', async () => {
      const expectedResult = { id: 1, ...createDto };
      
      mockRepository.findOne.mockResolvedValue(null); // ไม่มีข้อมูลซ้ำ
      mockRepository.create.mockReturnValue(expectedResult);
      mockRepository.save.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          department: { id: createDto.departmentId },
          employeeType: { id: createDto.employeeTypeId },
          type: createDto.type
        }
      });
      expect(mockRepository.create).toHaveBeenCalledWith({
        type: createDto.type,
        qtyOtMin: createDto.qtyOtMin,
        deduct: createDto.deduct,
        active: createDto.active,
        department: { id: createDto.departmentId },
        employeeType: { id: createDto.employeeTypeId }
      });
      expect(result).toEqual(expectedResult);
    });

    it('should throw BadRequestException when condition already exists', async () => {
      const existingCondition = { id: 1, ...createDto };
      mockRepository.findOne.mockResolvedValue(existingCondition);

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
      expect(mockRepository.create).not.toHaveBeenCalled();
      expect(mockRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return all ot conditions', async () => {
      const expectedResult = [
        { id: 1, type: OtConditionType.WORK, qtyOtMin: 30 },
        { id: 2, type: OtConditionType.OFF, qtyOtMin: 60 },
      ];
      
      mockRepository.find.mockResolvedValue(expectedResult);

      const result = await service.findAll();

      expect(mockRepository.find).toHaveBeenCalledWith({
        relations: ['department', 'employeeType'],
        where: {},
        order: { createdAt: 'DESC' }
      });
      expect(result).toEqual(expectedResult);
    });

    it('should filter by query parameters', async () => {
      const query = { departmentId: 1, employeeTypeId: 2, type: OtConditionType.WORK, active: 'true' };
      const expectedResult = [{ id: 1, type: OtConditionType.WORK }];
      
      mockRepository.find.mockResolvedValue(expectedResult);

      const result = await service.findAll(query);

      expect(mockRepository.find).toHaveBeenCalledWith({
        relations: ['department', 'employeeType'],
        where: {
          department: { id: 1 },
          employeeType: { id: 2 },
          type: OtConditionType.WORK,
          active: true
        },
        order: { createdAt: 'DESC' }
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOne', () => {
    it('should return ot condition by id', async () => {
      const expectedResult = { id: 1, type: OtConditionType.WORK };
      mockRepository.findOne.mockResolvedValue(expectedResult);

      const result = await service.findOne(1);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        relations: ['department', 'employeeType'],
        where: { id: 1 }
      });
      expect(result).toEqual(expectedResult);
    });

    it('should throw NotFoundException when ot condition not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    const updateDto = {
      departmentId: 1,
      employeeTypeId: 1,
      type: OtConditionType.WORK,
      qtyOtMin: 45,
      deduct: true,
      active: true,
    };

    it('should update ot condition successfully', async () => {
      const existingCondition = { id: 1, type: OtConditionType.OFF };
      const updatedCondition = { id: 1, ...updateDto };
      
      mockRepository.findOne
        .mockResolvedValueOnce(existingCondition) // findOneById
        .mockResolvedValueOnce(null) // check for duplicate
        .mockResolvedValueOnce(updatedCondition); // findOne after update
      
      mockRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(1, updateDto);

      expect(mockRepository.update).toHaveBeenCalledWith(1, {
        type: updateDto.type,
        qtyOtMin: updateDto.qtyOtMin,
        deduct: updateDto.deduct,
        active: updateDto.active,
        department: { id: updateDto.departmentId },
        employeeType: { id: updateDto.employeeTypeId }
      });
      expect(result).toEqual(updatedCondition);
    });

    it('should throw NotFoundException when ot condition not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(999, updateDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove ot condition successfully', async () => {
      const existingCondition = { id: 1, type: OtConditionType.WORK };
      mockRepository.findOne.mockResolvedValue(existingCondition);
      mockRepository.softDelete.mockResolvedValue({ affected: 1 });

      const result = await service.remove(1);

      expect(mockRepository.softDelete).toHaveBeenCalledWith(1);
      expect(result).toEqual({ message: 'OT condition deleted successfully' });
    });

    it('should throw NotFoundException when ot condition not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findConditionByParams', () => {
    it('should find condition by department, employee type and type', async () => {
      const expectedResult = { 
        id: 1, 
        type: OtConditionType.WORK, 
        qtyOtMin: 30,
        active: true 
      };
      
      mockRepository.findOne.mockResolvedValue(expectedResult);

      const result = await service.findConditionByParams(1, 1, OtConditionType.WORK);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          department: { id: 1 },
          employeeType: { id: 1 },
          type: OtConditionType.WORK,
          active: true
        },
        relations: ['department', 'employeeType']
      });
      expect(result).toEqual(expectedResult);
    });
  });
});
