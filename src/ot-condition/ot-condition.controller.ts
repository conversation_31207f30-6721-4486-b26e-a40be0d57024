import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  Query
} from '@nestjs/common';
import { ApiTags, ApiQuery } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { OtConditionService, OT_CONDITION_PAGINATION_CONFIG } from './ot-condition.service';
import { CreateOtConditionDto } from './dto/create-ot-condition.dto';
import { UpdateOtConditionDto } from './dto/update-ot-condition.dto';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('ot-condition')
@ApiTags('เงื่อนไข OT')
@Auth()
export class OtConditionController {
  constructor(private readonly otConditionService: OtConditionService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(OT_CONDITION_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.otConditionService.datatables(query);
  }

  @Post()
  create(@Body() createOtConditionDto: CreateOtConditionDto) {
    return this.otConditionService.create(createOtConditionDto);
  }

  @Get()
  @ApiQuery({ name: 'departmentId', required: false })
  @ApiQuery({ name: 'employeeTypeId', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'active', required: false })
  findAll(@Query() query: any) {
    return this.otConditionService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.otConditionService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id', ParseIntPipe) id: string, @Body() updateOtConditionDto: UpdateOtConditionDto) {
    return this.otConditionService.update(+id, updateOtConditionDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.otConditionService.remove(+id);
  }
}
