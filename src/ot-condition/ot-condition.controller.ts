import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { OtConditionService } from './ot-condition.service';
import { CreateOtConditionDto } from './dto/create-ot-condition.dto';
import { UpdateOtConditionDto } from './dto/update-ot-condition.dto';

@Controller('ot-condition')
export class OtConditionController {
  constructor(private readonly otConditionService: OtConditionService) {}

  @Post()
  create(@Body() createOtConditionDto: CreateOtConditionDto) {
    return this.otConditionService.create(createOtConditionDto);
  }

  @Get()
  findAll() {
    return this.otConditionService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.otConditionService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateOtConditionDto: UpdateOtConditionDto) {
    return this.otConditionService.update(+id, updateOtConditionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.otConditionService.remove(+id);
  }
}
