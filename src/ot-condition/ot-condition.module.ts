import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OtConditionService } from './ot-condition.service';
import { OtConditionController } from './ot-condition.controller';
import { OtCondition } from './entities/ot-condition.entity';

@Module({
  imports: [TypeOrmModule.forFeature([OtCondition])],
  controllers: [OtConditionController],
  providers: [OtConditionService],
  exports: [OtConditionService],
})
export class OtConditionModule {}
