import { Injectable } from '@nestjs/common';
import { CreateOtConditionDto } from './dto/create-ot-condition.dto';
import { UpdateOtConditionDto } from './dto/update-ot-condition.dto';

@Injectable()
export class OtConditionService {
  create(createOtConditionDto: CreateOtConditionDto) {
    return 'This action adds a new otCondition';
  }

  findAll() {
    return `This action returns all otCondition`;
  }

  findOne(id: number) {
    return `This action returns a #${id} otCondition`;
  }

  update(id: number, updateOtConditionDto: UpdateOtConditionDto) {
    return `This action updates a #${id} otCondition`;
  }

  remove(id: number) {
    return `This action removes a #${id} otCondition`;
  }
}
