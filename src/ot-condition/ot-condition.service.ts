import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { CreateOtConditionDto } from './dto/create-ot-condition.dto';
import { UpdateOtConditionDto } from './dto/update-ot-condition.dto';
import { OtCondition, OtConditionType } from './entities/ot-condition.entity';

export const OT_CONDITION_PAGINATION_CONFIG: PaginateConfig<OtCondition> = {
  relations: ['department', 'employeeType'],
  sortableColumns: ['id', 'type', 'qtyOtMin', 'deduct', 'active', 'createdAt'],
  select: [
    'id', 'type', 'qtyOtMin', 'deduct', 'active', 'createdAt',
    'department.id', 'department.code', 'department.name',
    'employeeType.id', 'employeeType.code', 'employeeType.name'
  ],
  searchableColumns: ['department.name', 'employeeType.name'],
  filterableColumns: {
    department: [FilterOperator.EQ],
    employeeType: [FilterOperator.EQ],
    type: [FilterOperator.EQ],
    active: [FilterOperator.EQ],
  },
};

@Injectable()
export class OtConditionService {
  constructor(
    @InjectRepository(OtCondition)
    private otConditionRepository: Repository<OtCondition>,
  ) {}

  async datatables(query: PaginateQuery): Promise<Paginated<OtCondition>> {
    return paginate(query, this.otConditionRepository, OT_CONDITION_PAGINATION_CONFIG);
  }

  async create(createOtConditionDto: CreateOtConditionDto) {
    const { departmentId, employeeTypeId, ...data } = createOtConditionDto;

    // ตรวจสอบว่ามีการตั้งค่าเงื่อนไข OT สำหรับแผนกและประเภทพนักงานนี้แล้วหรือไม่
    const existingCondition = await this.otConditionRepository.findOne({
      where: {
        department: { id: departmentId },
        employeeType: { id: employeeTypeId },
        type: data.type
      }
    });

    if (existingCondition) {
      throw new BadRequestException('OT condition for this department, employee type and type already exists.');
    }

    const item = this.otConditionRepository.create({
      ...data,
      department: { id: departmentId },
      employeeType: { id: employeeTypeId }
    });

    return this.otConditionRepository.save(item);
  }

  findAll(query?: any) {
    const { departmentId, employeeTypeId, type, active } = query || {};

    const whereCondition: any = {};

    if (departmentId) {
      whereCondition.department = { id: departmentId };
    }

    if (employeeTypeId) {
      whereCondition.employeeType = { id: employeeTypeId };
    }

    if (type) {
      whereCondition.type = type;
    }

    if (active !== undefined) {
      whereCondition.active = active === 'true';
    }

    return this.otConditionRepository.find({
      relations: ['department', 'employeeType'],
      where: whereCondition,
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const item = await this.otConditionRepository.findOne({
      relations: ['department', 'employeeType'],
      where: { id }
    });

    if (!item) {
      throw new NotFoundException('OT condition not found');
    }

    return item;
  }

  async update(id: number, updateOtConditionDto: UpdateOtConditionDto) {
    const item = await this.findOneById(id);
    if (!item) {
      throw new NotFoundException('OT condition not found');
    }

    const { departmentId, employeeTypeId, ...data } = updateOtConditionDto;

    // ตรวจสอบว่ามีการตั้งค่าเงื่อนไข OT สำหรับแผนกและประเภทพนักงานนี้แล้วหรือไม่ (ยกเว้นรายการปัจจุบัน)
    if (departmentId && employeeTypeId && data.type) {
      const existingCondition = await this.otConditionRepository.findOne({
        where: {
          department: { id: departmentId },
          employeeType: { id: employeeTypeId },
          type: data.type,
          id: Not(id)
        }
      });

      if (existingCondition) {
        throw new BadRequestException('OT condition for this department, employee type and type already exists.');
      }
    }

    const updateData: any = { ...data };

    if (departmentId) {
      updateData.department = { id: departmentId };
    }

    if (employeeTypeId) {
      updateData.employeeType = { id: employeeTypeId };
    }

    await this.otConditionRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) {
      throw new NotFoundException('OT condition not found');
    }

    await this.otConditionRepository.softDelete(id);
    return { message: 'OT condition deleted successfully' };
  }

  findOneById(id: number) {
    return this.otConditionRepository.findOne({ where: { id } });
  }

  // ฟังก์ชันสำหรับค้นหาเงื่อนไข OT ตามแผนก, ประเภทพนักงาน และประเภทวัน
  async findConditionByParams(departmentId: number, employeeTypeId: number, type: OtConditionType) {
    return this.otConditionRepository.findOne({
      where: {
        department: { id: departmentId },
        employeeType: { id: employeeTypeId },
        type: type,
        active: true
      },
      relations: ['department', 'employeeType']
    });
  }
}
