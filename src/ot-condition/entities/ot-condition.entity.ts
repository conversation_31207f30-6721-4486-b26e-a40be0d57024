import { Department } from "../../department/entities/department.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne, JoinColumn } from "typeorm";
import { EmployeeType } from "../../employee-type/entities/employee-type.entity";

export enum OtConditionType {
  WORK = 'work',
  OFF = 'off',
  HOLIDAY = 'holiday',
}

@Entity()
export class OtCondition extends CustomBaseEntity {
  @ManyToOne(() => Department, (_) => _.otConditions)
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @ManyToOne(() => EmployeeType, (_) => _.otConditions)
  @JoinColumn({ name: 'employee_type_id' })
  employeeType: EmployeeType;

  @Column({
    type: "enum",
    enum: OtConditionType,
    default: OtConditionType.WORK,
    comment: 'ประเภทของเงื่อนไข OT (work=วันทำงาน, off=วันหยุด, holiday=วันหยุดนักขัตฤกษ์)'
  })
  type: OtConditionType;

  @Column({
    default: 0,
    comment: 'จำนวนนาทีขั้นต่ำสำหรับ OT'
  })
  qtyOtMin: number;

  @Column({
    default: false,
    comment: 'หักเวลาพักหรือไม่'
  })
  deduct: boolean;

  @Column({
    default: true,
    comment: 'สถานะการใช้งาน'
  })
  active: boolean
}
