import { Department } from "../../department/entities/department.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne } from "typeorm";
import { EmployeeType } from "../../employee-type/entities/employee-type.entity";

export enum OtConditionType {
  WORK = 'work',
  OFF = 'off',
  HOLIDAY = 'holiday',
}

@Entity()
export class OtCondition extends CustomBaseEntity {
  @ManyToOne(() => Department, (_) => _.otConditions)
  department: Department;

  @ManyToOne(() => EmployeeType, (_) => _.otConditions)
  employeeType: EmployeeType;

  @Column({
    type: "enum",
    enum: OtConditionType,
    default: OtConditionType.WORK
  })
  type: OtConditionType;

  @Column({ default: 0 })
  qtyOtMin: number;

  @Column({ default: false })
  deduct: boolean;

  @Column({ default: true })
  active: boolean
}
