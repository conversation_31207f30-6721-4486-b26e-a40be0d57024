import { Module } from '@nestjs/common';
import { ApplicantService } from './applicant.service';
import { ApplicantController } from './applicant.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Applicant } from './entities/applicant.entity';
import { ApplicantOtp } from 'src/applicant_otp/entities/applicant_otp.entity';
import { CommandModule } from 'nestjs-command';
import { JwtModule } from '@nestjs/jwt';
import { AccessTokenStrategy, RefreshTokenStrategy } from 'src/auth/strategies';
import { AuthCommand } from 'src/auth/auth.command';
import { AuthService } from 'src/auth/auth.service';
import { ApplicantFamily } from 'src/applicant_family/entities/applicant_family.entity';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantWork } from 'src/applicant_work/entities/applicant_work.entity';
import { ApplicantTrain } from 'src/applicant_train/entities/applicant_train.entity';
import { ApplicantReference } from 'src/applicant_reference/entities/applicant_reference.entity';
import { ApplicantContact } from 'src/applicant_contact/entities/applicant_contact.entity';
import { ApplicantQuestion } from 'src/applicant_question/entities/applicant_question.entity';

@Module({
  imports: [JwtModule.register({}), CommandModule, TypeOrmModule.forFeature([
    Applicant, 
    ApplicantOtp,
    ApplicantFamily,
    ApplicantEducation,
    ApplicantWork,
    ApplicantTrain,
    ApplicantReference,
    ApplicantContact,
    ApplicantQuestion,
  ])],
  controllers: [ApplicantController],
  providers: [ApplicantService, AccessTokenStrategy, RefreshTokenStrategy, AuthCommand, AuthService],
})
export class ApplicantModule { }
