import { Controller, Get, Post, Body, Patch, Param, Delete, Put, ParseIntPipe, HttpCode, HttpStatus, Query, Res, Redirect } from '@nestjs/common';
import { APPLICANT_PAGINATION_CONFIG, ApplicantService } from './applicant.service';
import { CreateApplicantDto } from './dto/create-applicant.dto';
import { UpdateApplicantDto } from './dto/update-applicant.dto';
import { RegisterEmailAuthDto } from './dto/register-email-auth.dto';
import { ApiTags } from '@nestjs/swagger';
import { OtpEmailAuthDto } from './dto/otp-email-auth.dto';
import { CreatePasswordAuthDto } from './dto/create-password-auth.dto';
import { LoginEmailDto } from './dto/login-email.dto';
import { ConFirmOtpForgotPasswordDto } from './dto/confirm-otp-forgot-password.dto';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UpdateApplicantInformationDto } from './dto/update-applicant-information.dto';
import { UpdateFamilyDto } from './dto/update-family.dto';
import { UpdateEducationDto } from './dto/update-education.dto';
import { UpdateWorkDto } from './dto/update-work.dto';
import { UpdateTrainDto } from './dto/update-train.dto';
import { UpdateSkillDto } from './dto/update-skill.dto';
import { UpdateReferenceDto } from './dto/update-reference.dto';
import { UpdateQuestion1Dto } from './dto/update-question.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { UpdateDeclarationDto } from './dto/update-declaration.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Response } from 'express';
import { UpdateFileDto, UpdateFilesDto } from './dto/update-file.dto';


@Controller('applicant')
@ApiTags('ผู้สมัคร')
export class ApplicantController {
  constructor(private readonly applicantService: ApplicantService) { }


  @Post('confirm_email')
  confirmEmail(@Body() registerEmailAuthDto: RegisterEmailAuthDto) {
    return this.applicantService.confirmEmail(registerEmailAuthDto);
  }

  @Post('confirm_otp_email')
  confirmOtpEmail(@Body() otpEmailAuthDto: OtpEmailAuthDto) {
    return this.applicantService.confirmOtpEmail(otpEmailAuthDto);
  }

  @Post('create_password')
  createPassword(@Body() createPasswordAuthDto: CreatePasswordAuthDto) {
    return this.applicantService.createPassword(createPasswordAuthDto);
  }

  @Post('login_applicant')
  login(@Body() loginEmailDto: LoginEmailDto) {
    return this.applicantService.login(loginEmailDto);
  }

  @Post('forgot_password_applicant')
  forgotPassword(@Body() registerEmailAuthDto: RegisterEmailAuthDto) {
    return this.applicantService.forgotPassword(registerEmailAuthDto);
  }

  @Post('confirm_forgot_password_applicant')
  confirmOtpForgotPassword(@Body() conFirmOtpForgotPasswordDto: ConFirmOtpForgotPasswordDto) {
    return this.applicantService.confirmOtpForgotPassword(conFirmOtpForgotPasswordDto);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(APPLICANT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.applicantService.datatables(query);
  }

  @Get()
  findAll(@Query() query) {
    return this.applicantService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.applicantService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Applicant)
  updateInformation(@Param('id', ParseIntPipe) id: string, @Body() updateApplicantInformationDto: UpdateApplicantInformationDto) {
    return this.applicantService.updateInformation(+id, updateApplicantInformationDto);
  }

  @Put('applicant_family/:id')
  @Roles(AuthRole.Applicant)
  updateFamily(@Param('id', ParseIntPipe) id: string, @Body() updateFamilyDto: UpdateFamilyDto) {
    return this.applicantService.updateFamily(+id, updateFamilyDto);
  }

  @Put('applicant_education/:id')
  @Roles(AuthRole.Applicant)
  updateEducation(@Param('id', ParseIntPipe) id: string, @Body() updateEducationDto: UpdateEducationDto) {
    return this.applicantService.updateEducation(+id, updateEducationDto);
  }

  @Put('applicant_work/:id')
  @Roles(AuthRole.Applicant)
  updateWork(@Param('id', ParseIntPipe) id: string, @Body() updateWorkDto: UpdateWorkDto) {
    return this.applicantService.updateWork(+id, updateWorkDto);
  }

  @Put('applicant_train/:id')
  @Roles(AuthRole.Applicant)
  updateTrain(@Param('id', ParseIntPipe) id: string, @Body() updateTrainDto: UpdateTrainDto) {
    return this.applicantService.updateTrain(+id, updateTrainDto);
  }

  @Put('applicant_skill/:id')
  @Roles(AuthRole.Applicant)
  updateSkill(@Param('id', ParseIntPipe) id: string, @Body() updateSkillDto: UpdateSkillDto) {
    return this.applicantService.updateSkill(+id, updateSkillDto);
  }

  @Put('applicant_reference/:id')
  @Roles(AuthRole.Applicant)
  updateReference(@Param('id', ParseIntPipe) id: string, @Body() updateReferenceDto: UpdateReferenceDto) {
    return this.applicantService.updateReference(+id, updateReferenceDto);
  }

  @Put('applicant_question/:id')
  @Roles(AuthRole.Applicant)
  updateQuestion(@Param('id', ParseIntPipe) id: string, @Body() updateQuestionDto: UpdateQuestion1Dto) {
    return this.applicantService.updateQuestion(+id, updateQuestionDto);
  }

  @Put('applicant_contact/:id')
  @Roles(AuthRole.Applicant)
  updateContact(@Param('id', ParseIntPipe) id: string, @Body() updateContactDto: UpdateContactDto) {
    return this.applicantService.updateContact(+id, updateContactDto);
  }

  @Put('applicant_declaration/:id')
  @Roles(AuthRole.Applicant)
  updateDeclaration(@Param('id', ParseIntPipe) id: string, @Body() updateDeclarationDto: UpdateDeclarationDto) {
    return this.applicantService.updateDeclaration(+id, updateDeclarationDto);
  }

  @Get('generate-pdf/:id')
  async generatePdf(@Param('id') id: string, @Res() res: Response) {
    if (!id) {
      return res.status(400).json({ message: 'ID is required' });
    }

    try {
      const pdfBuffer = await this.applicantService.generatePdfFromHtml(id);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="generated-${id}.pdf"`);
      res.send(pdfBuffer);
    } catch (error) {
      res.status(500).json({ message: 'Failed to generate PDF', error: error.message });
    }
  }




  @Put('applicant_files/:id')
  // @Roles(AuthRole.Applicant)
  updateFiles(@Param('id', ParseIntPipe) id: string, @Body() payload: UpdateFilesDto) {
    return this.applicantService.updateFiles(+id, payload);
  }

  // updateInformation
  // @Post()
  // create(@Body() createApplicantDto: CreateApplicantDto) {
  //   return this.applicantService.create(createApplicantDto);
  // }

  // @Get()
  // findAll() {
  //   return this.applicantService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.applicantService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateApplicantDto: UpdateApplicantDto) {
  //   return this.applicantService.update(+id, updateApplicantDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.applicantService.remove(+id);
  // }
}
