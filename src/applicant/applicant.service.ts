import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateApplicantDto } from './dto/create-applicant.dto';
import { UpdateApplicantDto } from './dto/update-applicant.dto';
import { RegisterEmailAuthDto } from './dto/register-email-auth.dto';
import { DataSource, Repository } from 'typeorm';
import { MailerService } from '@nestjs-modules/mailer';
import { InjectRepository } from '@nestjs/typeorm';
import { Applicant } from './entities/applicant.entity';
import { Helper } from 'src/common/utils/helper';
import { ApplicantOtp } from 'src/applicant_otp/entities/applicant_otp.entity';
import { OtpEmailAuthDto } from './dto/otp-email-auth.dto';
import { CreatePasswordAuthDto } from './dto/create-password-auth.dto';
import * as argon2 from 'argon2';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { LoginEmailDto } from './dto/login-email.dto';
import { AuthRole } from 'src/auth/auth.interface';
import { ConFirmOtpForgotPasswordDto } from './dto/confirm-otp-forgot-password.dto';
import { UpdateApplicantInformationDto } from './dto/update-applicant-information.dto';
import { ApplicantFamily } from 'src/applicant_family/entities/applicant_family.entity';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantContact } from 'src/applicant_contact/entities/applicant_contact.entity';
import { ApplicantReference } from 'src/applicant_reference/entities/applicant_reference.entity';
import { ApplicantWork } from 'src/applicant_work/entities/applicant_work.entity';
import { ApplicantTrain } from 'src/applicant_train/entities/applicant_train.entity';
import { ApplicantQuestion } from 'src/applicant_question/entities/applicant_question.entity';
import { UpdateFamilyDto } from './dto/update-family.dto';
import { UpdateEducationDto } from './dto/update-education.dto';
import { UpdateWorkDto } from './dto/update-work.dto';
import { UpdateTrainDto } from './dto/update-train.dto';
import { UpdateSkillDto } from './dto/update-skill.dto';
import { UpdateReferenceDto } from './dto/update-reference.dto';
import { UpdateQuestion1Dto } from './dto/update-question.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { UpdateDeclarationDto } from './dto/update-declaration.dto';
import { FilterOperator, paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import * as puppeteer from 'puppeteer';
import * as path from 'path';
import { UpdateFileDto, UpdateFilesDto } from './dto/update-file.dto';
import { ApplicantFile } from './entities/applicant_file.entity';
import { DateTime } from 'luxon';

export const APPLICANT_PAGINATION_CONFIG: PaginateConfig<Applicant> = {
  relations: ['applicantFamilies', 'applicantEducations', 'applicantWorks', 'applicantTrains', 'applicantReferences', 'applicantContacts', 'applicantQuestions'],
  sortableColumns: [
    'id', 'code', 'positionAppliedFor', 'expectedSalary', 'YouKnowFrom', 'startDate', 'startDate'
    , 'prefixEn', 'fullnameEn', 'nicknameEn', 'prefixTh', 'fullnameTh', 'nicknameTh', 'permanentAddress', 'presentAddress'
    , 'homePhone', 'mobile', 'birthDate', 'age', 'height', 'weight', 'nationality', 'idCardNo', 'passportNo'
  ],
  // select: [
  //   'id', 'code', 'positionAppliedFor', 'expectedSalary', 'YouKnowFrom', 'startDate', 'startDate'
  //   , 'prefixEn', 'fullnameEn', 'nicknameEn', 'prefixTh', 'fullnameTh', 'nicknameTh', 'permanentAddress', 'presentAddress'
  //   , 'homePhone', 'mobile', 'birthDate', 'age', 'height', 'weight', 'nationality', 'idCardNo', 'passportNo'
  // ],
  searchableColumns: ['id', 'code', 'positionAppliedFor', 'expectedSalary', 'YouKnowFrom', 'startDate', 'startDate'
    , 'prefixEn', 'fullnameEn', 'nicknameEn', 'prefixTh', 'fullnameTh', 'nicknameTh', 'permanentAddress', 'presentAddress'
    , 'homePhone', 'mobile', 'birthDate', 'age', 'height', 'weight', 'nationality', 'idCardNo', 'passportNo'
  ],
  filterableColumns: {
    code: [FilterOperator.EQ],
    fullnameTh: [FilterOperator.EQ],
    fullnameEn: [FilterOperator.EQ],
  },
};
@Injectable()
export class ApplicantService {
  constructor(
    @InjectRepository(Applicant)
    private applicantRepository: Repository<Applicant>,

    @InjectRepository(ApplicantOtp)
    private applicantOtpRepository: Repository<ApplicantOtp>,

    @InjectRepository(ApplicantFamily)
    private applicantFamilyRepository: Repository<ApplicantFamily>,

    @InjectRepository(ApplicantEducation)
    private applicantEducationRepository: Repository<ApplicantEducation>,

    @InjectRepository(ApplicantWork)
    private applicantWorkRepository: Repository<ApplicantWork>,


    @InjectRepository(ApplicantContact)
    private applicantContactRepository: Repository<ApplicantContact>,

    @InjectRepository(ApplicantReference)
    private applicantReferenceRepository: Repository<ApplicantReference>,

    @InjectRepository(ApplicantWork)
    private applicantWorkReferenceRepository: Repository<ApplicantWork>,


    @InjectRepository(ApplicantTrain)
    private applicantTrainRepository: Repository<ApplicantTrain>,


    @InjectRepository(ApplicantQuestion)
    private applicantQuestionRepository: Repository<ApplicantQuestion>,




    private jwtService: JwtService,
    private configService: ConfigService,
    private dataSource: DataSource,
    private readonly mailerService: MailerService,

  ) { }

  private async getTokens(userId: number, username: string, role: string) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync({ sub: userId, username, role }, { secret: this.configService.get('JWT_ACCESS_SECRET'), expiresIn: '1d' }),
      this.jwtService.signAsync({ sub: userId, username, role }, { secret: this.configService.get('JWT_REFRESH_SECRET'), expiresIn: '7d' })
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async confirmEmail(registerEmailAuthDto: RegisterEmailAuthDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { email } = registerEmailAuthDto

      //check email uniq
      const emailIsExist = await this.applicantRepository.findOneBy({ email: email });
      if (emailIsExist) {
        throw new BadRequestException('email is already.')
      }

      //random otp
      const otpKey = Helper.randomOtp()

      //add otp
      let { otpCode, otpRef, otpExp } = otpKey

      //send otp
      this.mailerService.sendMail({
        to: email,
        subject: 'Meinhardt: Employment application Register',
        template: 'otp-employment-application-register',
        context: {
          otpCode: otpCode,
          otpRef: otpRef,
        }
      })
      //

      const otp = new ApplicantOtp();
      otp.email = email
      otp.otpCode = otpCode;
      otp.otpRef = otpRef;
      otp.otpExp = otpExp;
      otp.token = 'none';
      otp.otpType = 'email';

      await queryRunner.manager.save(otp)

      await queryRunner.commitTransaction();

      return { email: email, otp: otp }

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  async confirmOtpEmail(otpEmailAuthDto: OtpEmailAuthDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();


    try {

      const { email, otpCode, otpRef } = otpEmailAuthDto

      // check otp
      const otpIsExist = await this.applicantOtpRepository.findOne({
        where: {
          email: email,
          otpCode: otpCode,
          otpRef: otpRef,
          otpType: 'email'
        },
      })

      if (!otpIsExist) {
        throw new BadRequestException('otp is invalid')
      }

      //check otp time
      const timeDiff = await Helper.checkOtpExpire(otpIsExist.otpExp)

      if (timeDiff > 0) {
        throw new BadRequestException('otp is expired')
      }

      //update status
      otpIsExist.active = true
      await queryRunner.manager.save(otpIsExist)

      await queryRunner.commitTransaction();



      return { email: email }

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  async createPassword(createPasswordAuthDto: CreatePasswordAuthDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { email, password } = createPasswordAuthDto

      //check email uniq
      const applicantIsExist = await this.applicantRepository.findOneBy({ email: email });

      if (applicantIsExist) {
        throw new BadRequestException('email is already.')
      }


      const hash = await argon2.hash(password);

      //get last id
      const last = await Applicant.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = last.length;
      const Code = Helper.generateNo('AP', lastId).toString();
      //


      const applicant = new Applicant();
      applicant.code = Code;
      applicant.email = email
      applicant.passwordHash = hash;

      await queryRunner.manager.save(applicant)


      await queryRunner.commitTransaction();


      const tokens = await this.getTokens(applicant.id, applicant.email, AuthRole.Applicant);

      return {
        ...tokens,
        applicant,
        role: AuthRole.Applicant
      };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }


  async login(loginEmailDto: LoginEmailDto) {
    let { email } = loginEmailDto

    const applicant = await Applicant.findOne({
      where: {
        email: email,
      },
      select: ['id', 'email', 'passwordHash', 'fullnameTh', 'fullnameEn']
    })

    if (!applicant) {
      throw new BadRequestException('username or password is not correct');
    }

    const passwordMatches = await argon2.verify(applicant.passwordHash, loginEmailDto.password);
    if (!passwordMatches) {
      throw new BadRequestException('username or password is not correct');
    }

    const tokens = await this.getTokens(applicant.id, applicant.email, AuthRole.Applicant);

    delete applicant.passwordHash;

    return {
      ...tokens,
      applicant,
      role: AuthRole.Applicant
    };
  }

  async forgotPassword(registerEmailAuthDto: RegisterEmailAuthDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { email } = registerEmailAuthDto

      //check email uniq
      const applicantIsExist = await this.applicantRepository.findOneBy({ email: email });
      if (!applicantIsExist) {
        throw new BadRequestException('email not found')
      }

      //random otp
      const otpKey = Helper.randomOtp()

      //add otp
      let { otpCode, otpRef, otpExp } = otpKey


      //send otp
      this.mailerService.sendMail({
        to: email,
        subject: 'Meinhardt: Employment application Register',
        template: 'otp-employment-application-register',
        context: {
          otpCode: otpCode,
          otpRef: otpRef,
        }
      })
      //

      const otp = new ApplicantOtp();
      otp.email = email
      otp.otpCode = otpCode;
      otp.otpRef = otpRef;
      otp.otpExp = otpExp;
      otp.token = 'none';
      otp.otpType = 'email';

      await queryRunner.manager.save(otp)

      await queryRunner.commitTransaction();

      return { email: email, otp: otp }

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async confirmOtpForgotPassword(conFirmOtpForgotPasswordDto: ConFirmOtpForgotPasswordDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();


    try {

      const { email, otpCode, otpRef, password } = conFirmOtpForgotPasswordDto

      //check user
      //check email uniq
      const applicant = await this.applicantRepository.findOneBy({ email: email });
      if (!applicant) {
        throw new BadRequestException('email not found')
      }
      //

      // check otp
      const otpIsExist = await this.applicantOtpRepository.findOne({
        where: {
          email: email,
          otpCode: otpCode,
          otpRef: otpRef,
          otpType: 'email'
        },
      })

      if (!otpIsExist) {
        throw new BadRequestException('otp is invalid')
      }

      //check otp time
      const timeDiff = await Helper.checkOtpExpire(otpIsExist.otpExp)

      if (timeDiff > 0) {
        throw new BadRequestException('otp is expired')
      }

      //update status
      otpIsExist.active = true
      await queryRunner.manager.save(otpIsExist)


      //update applicant password
      const hash = await argon2.hash(password);
      applicant.passwordHash = hash
      await queryRunner.manager.save(applicant)

      await queryRunner.commitTransaction();

      const tokens = await this.getTokens(applicant.id, applicant.email, AuthRole.Applicant);

      return {
        ...tokens,
        applicant,
        role: AuthRole.Applicant
      };

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }



  }


  async datatables(query: PaginateQuery): Promise<Paginated<Applicant>> {
    return paginate(query, this.applicantRepository, APPLICANT_PAGINATION_CONFIG);
  }

  async create(createApplicantDto: CreateApplicantDto) {
    //
  }


  findAll(query) {
    return this.applicantRepository.find({
      relations: ['applicantFamilies', 'applicantEducations', 'applicantWorks', 'applicantTrains', 'applicantReferences', 'applicantContacts', 'applicantQuestions'],

    });
  }

  async findOne(id: number) {
    const item = await this.applicantRepository.findOne({
      relations: [
        'applicantFamilies',
        'applicantEducations',
        'applicantWorks',
        'applicantTrains',
        'applicantReferences',
        'applicantContacts',
        'applicantQuestions.question',
        'applicantQuestions.choice',
        'applicantFiles',
        'candidates',
      ],
      where: { id }
    });

    if (!item) throw new NotFoundException("applicant not found");

    return item;
  }


  findOneById(id: number) {
    return this.applicantRepository.findOne({
      relations: ['applicantFamilies', 'applicantEducations', 'applicantWorks', 'applicantTrains', 'applicantReferences', 'applicantContacts', 'applicantQuestions'],
      where: { id }
    });
  }

  remove(id: number) {
    return `This action removes a #${id} applicant`;
  }

  async updateInformation(id: number, updateApplicantInformationDto: UpdateApplicantInformationDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("contact not found");

      item.image = updateApplicantInformationDto.image
      item.positionAppliedFor = updateApplicantInformationDto.positionAppliedFor
      item.expectedSalary = updateApplicantInformationDto.expectedSalary
      item.YouKnowFrom = updateApplicantInformationDto.YouKnowFrom
      item.startDate = updateApplicantInformationDto.startDate

      item.prefixEn = updateApplicantInformationDto.prefixEn
      item.fullnameEn = updateApplicantInformationDto.fullnameEn
      item.nicknameEn = updateApplicantInformationDto.nicknameEn
      item.prefixTh = updateApplicantInformationDto.prefixTh
      item.fullnameTh = updateApplicantInformationDto.fullnameTh
      item.nicknameTh = updateApplicantInformationDto.nicknameTh
      item.permanentAddress = updateApplicantInformationDto.permanentAddress
      item.presentAddress = updateApplicantInformationDto.presentAddress
      item.homePhone = updateApplicantInformationDto.homePhone
      item.mobile = updateApplicantInformationDto.mobile
      item.birthDate = updateApplicantInformationDto.birthDate
      item.age = updateApplicantInformationDto.age
      item.placeOfBirth = updateApplicantInformationDto.placeOfBirth
      item.height = updateApplicantInformationDto.height
      item.weight = updateApplicantInformationDto.weight
      item.nationality = updateApplicantInformationDto.nationality
      item.idCardNo = updateApplicantInformationDto.idCardNo
      item.idCardIssuedBy = updateApplicantInformationDto.idCardIssuedBy
      item.idCardExpiryDate = updateApplicantInformationDto.idCardExpiryDate
      item.passportNo = updateApplicantInformationDto.passportNo
      item.passportIssuedBy = updateApplicantInformationDto.passportIssuedBy
      item.passportExpiryDate = updateApplicantInformationDto.passportExpiryDate

      await queryRunner.manager.save(item)

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateFamily(id: number, updateFamilyDto: UpdateFamilyDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      item.maritalStatus = updateFamilyDto.maritalStatus
      item.marriedName = updateFamilyDto.marriedName
      item.marriedAge = updateFamilyDto.marriedAge
      item.marriedOccupation = updateFamilyDto.marriedOccupation
      item.childrenQty = updateFamilyDto.childrenQty
      item.childrenName1 = updateFamilyDto.childrenName1
      item.childrenAge1 = updateFamilyDto.childrenAge1
      item.childrenName2 = updateFamilyDto.childrenName2
      item.childrenAge2 = updateFamilyDto.childrenAge2
      item.childrenName3 = updateFamilyDto.childrenName3
      item.childrenAge3 = updateFamilyDto.childrenAge3
      item.childrenName4 = updateFamilyDto.childrenName4
      item.childrenAge4 = updateFamilyDto.childrenAge4
      item.childrenName5 = updateFamilyDto.childrenName5
      item.childrenAge5 = updateFamilyDto.childrenAge5
      item.childrenName6 = updateFamilyDto.childrenName6
      item.childrenAge6 = updateFamilyDto.childrenAge6

      await queryRunner.manager.save(item)

      // add line

      //del
      const getApplicantFamily = await this.applicantFamilyRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantFamily) {
        getApplicantFamily.forEach(async (item) => {
          const deleteResponse = await this.applicantFamilyRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateFamilyDto.applicantFamily.length; i++) {

        const applicantFamily = new ApplicantFamily();
        applicantFamily.name = updateFamilyDto.applicantFamily[i].name
        applicantFamily.relationship = updateFamilyDto.applicantFamily[i].relationship
        applicantFamily.age = updateFamilyDto.applicantFamily[i].age
        applicantFamily.occupation = updateFamilyDto.applicantFamily[i].occupation
        applicantFamily.placeOfWork = updateFamilyDto.applicantFamily[i].placeOfWork

        applicantFamily.applicant = item //applicant

        await queryRunner.manager.save(applicantFamily)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateEducation(id: number, updateEducationDto: UpdateEducationDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");


      // add line

      //del
      const getApplicantEducation = await this.applicantEducationRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantEducation) {
        getApplicantEducation.forEach(async (item) => {
          const deleteResponse = await this.applicantEducationRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateEducationDto.applicantEducation.length; i++) {

        const applicantEducation = new ApplicantEducation();
        applicantEducation.name = updateEducationDto.applicantEducation[i].name
        applicantEducation.level = updateEducationDto.applicantEducation[i].level
        applicantEducation.from = updateEducationDto.applicantEducation[i].from
        applicantEducation.to = updateEducationDto.applicantEducation[i].to
        applicantEducation.major = updateEducationDto.applicantEducation[i].major
        applicantEducation.degree = updateEducationDto.applicantEducation[i].degree

        applicantEducation.applicant = item //applicant

        await queryRunner.manager.save(applicantEducation)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }


  async updateWork(id: number, updateWorkDto: UpdateWorkDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");


      // add line

      //del
      const getApplicantWork = await this.applicantWorkRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantWork) {
        getApplicantWork.forEach(async (item) => {
          const deleteResponse = await this.applicantWorkRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateWorkDto.applicantWork.length; i++) {

        const applicantWork = new ApplicantWork();
        applicantWork.name = updateWorkDto.applicantWork[i].name
        applicantWork.position = updateWorkDto.applicantWork[i].position
        applicantWork.from = updateWorkDto.applicantWork[i].from
        applicantWork.to = updateWorkDto.applicantWork[i].to
        applicantWork.salary = updateWorkDto.applicantWork[i].salary
        applicantWork.reasonLeaving = updateWorkDto.applicantWork[i].reasonLeaving

        applicantWork.applicant = item //applicant

        await queryRunner.manager.save(applicantWork)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateTrain(id: number, updateTrainDto: UpdateTrainDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");


      // add line

      //del
      const getApplicantTrain = await this.applicantTrainRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantTrain) {
        getApplicantTrain.forEach(async (item) => {
          const deleteResponse = await this.applicantTrainRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateTrainDto.applicantTrain.length; i++) {

        const applicantTrain = new ApplicantTrain();
        applicantTrain.name = updateTrainDto.applicantTrain[i].name
        applicantTrain.detail = updateTrainDto.applicantTrain[i].detail
        applicantTrain.monthYear = updateTrainDto.applicantTrain[i].monthYear
        applicantTrain.certificate = updateTrainDto.applicantTrain[i].certificate

        applicantTrain.applicant = item //applicant

        await queryRunner.manager.save(applicantTrain)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateSkill(id: number, updateSkillDto: UpdateSkillDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      item.skillLanguage1 = updateSkillDto.skillLanguage1
      item.skillLevel1 = updateSkillDto.skillLevel1
      item.skillLanguage2 = updateSkillDto.skillLanguage2
      item.skillLevel2 = updateSkillDto.skillLevel2

      item.typingEnSpeed = updateSkillDto.typingEnSpeed
      item.typingThSpeed = updateSkillDto.typingThSpeed
      item.computerSkill = updateSkillDto.computerSkill
      item.otherSkill = updateSkillDto.otherSkill

      await queryRunner.manager.save(item)


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateReference(id: number, updateReferenceDto: UpdateReferenceDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");


      // add line

      //del
      const getApplicantReference = await this.applicantReferenceRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantReference) {
        getApplicantReference.forEach(async (item) => {
          const deleteResponse = await this.applicantReferenceRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateReferenceDto.applicantReference.length; i++) {

        const applicantTrain = new ApplicantReference();
        applicantTrain.name = updateReferenceDto.applicantReference[i].name
        applicantTrain.companyName = updateReferenceDto.applicantReference[i].companyName
        applicantTrain.tel = updateReferenceDto.applicantReference[i].tel

        applicantTrain.applicant = item //applicant

        await queryRunner.manager.save(applicantTrain)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateFiles(id: number, payload: UpdateFilesDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      //del
      await ApplicantFile.delete({ applicant: { id: id } });

      //add
      const newFiles = [];
      for (let i = 0; i < payload.applicantFiles.length; i++) {
        const fileData = payload.applicantFiles[i];

        const file = ApplicantFile.create({
          name: fileData.name,
          file: fileData.file,
          applicant: item,
        });

        newFiles.push(file);
      }

      await queryRunner.manager.save(ApplicantFile, newFiles);

      await queryRunner.commitTransaction();

      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateQuestion(id: number, updateQuestionDto: UpdateQuestion1Dto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      //del
      await this.applicantQuestionRepository.softDelete({
        applicant: {
          id: id,
        }
      });

      //add
      const newApplicantQuestion = [];
      for (let i = 0; i < updateQuestionDto.applicantQuestion.length; i++) {
        const item = updateQuestionDto.applicantQuestion[i];

        const applicantQuestion = ApplicantQuestion.create({
          question: { id: item.questionId },
          choice: { id: item.choiceId },
          remark: item?.remark,
          remark2: item?.remark2,
          applicant: { id: id }
        });

        newApplicantQuestion.push(applicantQuestion);
      }

      await queryRunner.manager.save(newApplicantQuestion)

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateContact(id: number, updateContactDto: UpdateContactDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      item.nameOfYouFriend = updateContactDto.nameOfYouFriend
      item.anyAdditional = updateContactDto.anyAdditional

      await queryRunner.manager.save(item)
      // add line

      //del
      const getApplicantapplicantContact = await this.applicantContactRepository.find({
        where: {
          applicant: {
            id: id
          }
        }
      });
      if (getApplicantapplicantContact) {
        getApplicantapplicantContact.forEach(async (item) => {
          const deleteResponse = await this.applicantContactRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateContactDto.applicantContact.length; i++) {

        const applicantQuestion = new ApplicantContact();
        applicantQuestion.name = updateContactDto.applicantContact[i].name
        applicantQuestion.relationship = updateContactDto.applicantContact[i].relationship
        applicantQuestion.telHome = updateContactDto.applicantContact[i].telHome
        applicantQuestion.tel = updateContactDto.applicantContact[i].tel

        applicantQuestion.applicant = item //applicant

        await queryRunner.manager.save(applicantQuestion)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  async updateDeclaration(id: number, updateDeclarationDto: UpdateDeclarationDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("applicant not found");

      item.signatureApplicant = updateDeclarationDto.signatureApplicant
      item.signatureApplicantDate = updateDeclarationDto.signatureApplicantDate

      await queryRunner.manager.save(item)

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  async generatePdfFromHtml(id: string): Promise<Buffer> {


    const base64 = 'data:image/png;base64,...';


    const imagePath = path.resolve(__dirname, '..', 'public', 'uploads', 'logo.jpg'); //

    console.log(imagePath);



    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Sample PDF</title>
      <style>
        body {
          font-family: Arial, sans-serif;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        td {
          padding: 8px;
        }
      </style>
    </head>
    <body>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="width: 30%; border: none; text-align: left; font-size: 22px; font-weight: bold; color: #b4b5c1;">
            Meinhardt (Thailand) Ltd.
          </td>
          <td style="width: 70%; border: none; text-align: right;">
            <img src="/uploads/logo.jpg" alt="Logo" style="width: 130px; height: 50px;">
            <p style="text-align: right; padding-right: 20px; font-size: 16px; color: #b4b5c1;">12 - FORM MTRC-02</p>
          </td>
        </tr>
      </table>

       <table style="width: 100%; border-collapse: collapse;margin-top: 10px;">
                <tr>
                    <td style="width: 85%; border: none;text-align: center;padding-left:120px;">
                        <span style="font-weight: bold;color:#0009b1;">EMPLOYMENT APPLICATION FORM</span>
                        <p style="margin: 5px 0 0;color:#0009b1;font-weight: bold;">ใบสมัครเข้าทำงาน</p>
                    </td>
                    <td rowspan="2" style="width: 15%; border: none;text-align: center;border: 1px solid black;">
                        <p style="font-size:14px;">Recent</p>
                        <p style="font-size:14px;">Photograph</p>
                        <p style="font-size:14px;">(6 months)</p>
                        <p style="font-size:14px;">&nbsp;</p>
                        <p style="font-size:14px;">รูปถ่ายปัจจุบัน</p>
                        <p style="font-size:14px;">(ถ่ายไว้ไม่เกิน 6 เดือน)</p>
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%; border: none;text-align: left;">
                        <p style="font-size:14px;">Position Applied For.................................................................................................................................Expected Salary.............................................................</p>
                        <p style="font-size:14px;">(ตำแหน่งที่ต้องการ)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(เงินเดือนที่คาดหวัง)</p>
                        <p style="font-size:14px;">You know Meinhardt from.....................................................................................................................Start date.........................................................................</p>
                        <p style="font-size:14px;">(ท่านรู้จัก บริษัท ไมนฮาร์ท ประเทศไทย จำกัด จากที่ไหน)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(เริ่มงานได้เมื่อไหร่)</p>
                    </td>
                </tr>
            </table>
             <div style="position absolute;padding-top:10px;margin-top:15px;font-size: 18px;padding: 10px 10px 10px; width: 100%; text-align: left;line-height: 0.3;background-color: #0009b1;color: white;">
                <span>PERSONAL DATA (ประวัติส่วนตัว)</span>
            </div>
            <div style="position absolute;padding-top:10px;margin-top:10px;font-size: 14px; width: 100%; text-align: left;line-height: 0.1;">
                <p>Full Name:Mr/Mrs./Miss......................................................................................................................................................................................Nicename.......................................................................</p>
                <p>&nbsp;</p>
                <p>ชื่อ-นามสกุล: นาย/นาง/นางสาว..............................................................................................................................................................................ชื่อเล่น..............................................................................</p>
                <p>&nbsp;</p>
                <p>Permanent Address:.......................................................................................................................................................................................................................................................................................</p>
                <p>(ที่อยู่ถาวร)</p>
                <p>Present Address:............................................................................................................................................................................................................................................................................................</p>
                <p>(ที่อยู่ปัจจุบัน)</p>
                <p>Home Phone:..............................................................Mobile:.........................................................&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;E-mail Address:...........................................................................................................................</p>
                <p>(โทรศัพท์บ้าน)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(โทรศัพท์มือถือ)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(อีเมล)</p>
                <p>Date of Birth:..............................................................Age:.................................................................&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Place of Birth:...........................................................................................................................</p>
                <p>(วัน เดือน ปีเกิด)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(อายุ)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(สถานที่เกิด)</p>
                <p>Height:..........................................................................Weight:...........................................................&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nationality:.................................................................................................................................</p>
                <p>(ความสูง)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(น้ำหนัก)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(สัญชาติ)</p>
                <p>I.D. Card No.:.............................................................................................&nbsp;&nbsp;&nbsp;&nbsp;Issued by:..................................................................................&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Expiry Date:...............................................................</p>
                <p>(บัตรประจำตัวประชาชนเลขที่)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(ออกให้โดย)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(วันหมดอายุ)</p>
                <p>Passport No.:.............................................................................................&nbsp;&nbsp;&nbsp;&nbsp;Issued by:..................................................................................&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Expiry Date:...............................................................</p>
                <p>(หนังสือเดินทางเลขที่)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(ออกให้โดย)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(วันหมดอายุ)</p>
            </div>
            <div style="position absolute;padding-top:5px;margin-top:12px;font-size: 18px;padding: 10px 10px 10px; width: 100%; text-align: left;line-height: 0.3;background-color: #0009b1;color: white;">
                <span>FAMILY DETAILS (รายละเอียดครอบครัว)</span>
            </div>
             <table style="width: 100%; border-collapse: collapse;padding-top: 5px;font-size: 14px;">
                <tr>
                    <td style="width: 20%; border: none;text-align: left;">
                        <p>Marital Status:</p>
                        <p>(สถานะภาพสมรส)
                    </td>
                    <td style="width: 15%; border: none;text-align: center;">
                        <span style="font-family: Arial, sans-serif; font-size: 16px; display: inline-block;">' .($maritalstatusChecked['Single'] ? '&#9745;' : '&#9744;') . '</span>
                        Single
                        <p>(โสด)</p>

                    </td>
                    <td style="width: 15%; border: none;text-align: center;">
                        <span style="font-family: Arial, sans-serif; font-size: 16px; display: inline-block;">' .($maritalstatusChecked['Married'] ? '&#9745;' : '&#9744;') . '</span>
                        Married
                        <p>(แต่งงาน)</p>

                    </td>
                    <td style="width: 15%; border: none;text-align: center;">
                        <span style="font-family: Arial, sans-serif; font-size: 16px; display: inline-block;">' .($maritalstatusChecked['Divorced'] ? '&#9745;' : '&#9744;') . '</span>
                        Divorced
                        <p>(หย่า)</p>
                    </td>
                    <td style="width: 15%; border: none;text-align: center;">
                        <span style="font-family: Arial, sans-serif; font-size: 16px; display: inline-block;">' .($maritalstatusChecked['Separated'] ? '&#9745;' : '&#9744;') . '</span>
                        Separated
                        <p>(แยกกันอยู่)</p>
                    </td>
                </tr>
            </table>
             <div style="position absolute;padding-top:10px;font-size: 14px; width: 100%; text-align: left;line-height: 0.1;">
                <p>Spouse'."'".'s Name (if married):..........................................................................................................................Age:..........................................Occupation:.................................................</p>
                <p>(ชื่่อ-นามสกุล คู่สมรส ในกรณีแต่งงาน)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(อายุ)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(อาชีพ)</p>
                <p>Number of Children, if any (จำนวนบุตร) ...................... (คน) Please specify children'."'".'s name and age below (ระบุชื่อ-นามสกุล บุตร ด้านล่าง)</p>
                <p>1. .........................................................................................Age (อายุ)....................................... 2. .........................................................................................Age (อายุ) ........................................</p>
                <p>3. .........................................................................................Age (อายุ)....................................... 4. .........................................................................................Age (อายุ) ........................................</p>
                <p>5. .........................................................................................Age (อายุ)....................................... 6. .........................................................................................Age (อายุ) ........................................</p>
            </div>
    </body>
    </html>
    `;


    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    // Set the HTML content
    await page.setContent(htmlContent, { waitUntil: 'networkidle2' });

    // Generate PDF with custom options
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20px', bottom: '20px', left: '20px', right: '20px' },
    });

    await browser.close();
    return Buffer.from(pdfBuffer); // Return PDF as a Buffer
  }

}
