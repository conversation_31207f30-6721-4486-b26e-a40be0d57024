
import { Column, <PERSON>tity, Index, OneToMany, OneToOne, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";
import { ApplicantFamily } from "../../applicant_family/entities/applicant_family.entity";
import { ApplicantEducation } from "../../applicant_education/entities/applicant_education.entity";
import { ApplicantWork } from "../../applicant_work/entities/applicant_work.entity";
import { ApplicantTrain } from "../../applicant_train/entities/applicant_train.entity";
import { ApplicantReference } from "../../applicant_reference/entities/applicant_reference.entity";
import { ApplicantContact } from "../../applicant_contact/entities/applicant_contact.entity";
import { ApplicantQuestion } from "../../applicant_question/entities/applicant_question.entity";
import { Candidate } from "../../candidate/entities/candidate.entity";
import { ApplicantFile } from "./applicant_file.entity";

@Entity()
@Unique(['code'])
export class Applicant extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Index({ unique: true })
    @Column()
    email: string;

    @Column()
    @Exclude()
    passwordHash: string;


    @Column("text", { nullable: true })
    image: string;

    @Column("text", { nullable: true })
    positionAppliedFor: string;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    expectedSalary: number;

    @Column("text", { nullable: true })
    YouKnowFrom: string;

    @Column("date", { nullable: true })
    startDate: Date

    @Column({ nullable: true })
    prefixEn: string

    @Column("text", { nullable: true })
    fullnameEn: string;

    @Column("text", { nullable: true })
    nicknameEn: string;

    @Column({ nullable: true })
    prefixTh: string

    @Column("text", { nullable: true })
    fullnameTh: string;

    @Column("text", { nullable: true })
    nicknameTh: string;

    @Column("text", { nullable: true })
    permanentAddress: string;

    @Column("text", { nullable: true })
    presentAddress: string;

    @Column("text", { nullable: true })
    homePhone: string;

    @Column("text", { nullable: true })
    mobile: string;

    @Column("date", { nullable: true })
    birthDate: Date

    @Column('int', { nullable: true })
    age: number

    @Column("text", { nullable: true })
    placeOfBirth: string;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    height: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    weight: number;

    @Column("text", { nullable: true })
    nationality: string;

    @Column("text", { nullable: true })
    idCardNo: string;

    @Column("text", { nullable: true })
    idCardIssuedBy: string;

    @Column("date", { nullable: true })
    idCardExpiryDate: Date

    @Column("text", { nullable: true })
    passportNo: string

    @Column("text", { nullable: true })
    passportIssuedBy: string;

    @Column("date", { nullable: true })
    passportExpiryDate: Date

    @Column({
        type: "enum",
        enum: ["single", "married", "divorced", "separated"],
        default: 'single'
    })
    maritalStatus: string

    @Column("text", { nullable: true })
    marriedName: string

    @Column('int', { nullable: true })
    marriedAge: number

    @Column("text", { nullable: true })
    marriedOccupation: string

    @Column('int', { nullable: true })
    childrenQty: number


    @Column("text", { nullable: true })
    childrenName1: string

    @Column('int', { nullable: true })
    childrenAge1: number

    @Column("text", { nullable: true })
    childrenName2: string

    @Column('int', { nullable: true })
    childrenAge2: number

    @Column("text", { nullable: true })
    childrenName3: string

    @Column('int', { nullable: true })
    childrenAge3: number

    @Column("text", { nullable: true })
    childrenName4: string

    @Column('int', { nullable: true })
    childrenAge4: number

    @Column("text", { nullable: true })
    childrenName5: string

    @Column('int', { nullable: true })
    childrenAge5: number

    @Column("text", { nullable: true })
    childrenName6: string

    @Column('int', { nullable: true })
    childrenAge6: number


    @Column({ default: true })
    active: boolean


    @Column("text", { nullable: true })
    skillLanguage1: string

    @Column({
        type: "enum",
        enum: ["excellent", "veryGood", "good", "fair", "poor"],
        nullable: true,
    })
    skillLevel1: string

    @Column("text", { nullable: true })
    skillLanguage2: string

    @Column({
        type: "enum",
        enum: ["excellent", "veryGood", "good", "fair", "poor"],
        nullable: true  
    })
    skillLevel2: string

    @Column('int', { nullable: true })
    typingEnSpeed: number

    @Column('int', { nullable: true })
    typingThSpeed: number

    @Column("text", { nullable: true })
    computerSkill: string

    @Column("text", { nullable: true })
    otherSkill: string

    @Column("text", { nullable: true })
    nameOfYouFriend: string

    @Column("text", { nullable: true })
    anyAdditional: string


    @Column("text", { nullable: true })
    signatureApplicant: string

    @Column("date", { nullable: true })
    signatureApplicantDate: Date

    // @Expose()
    // get fullname(): string {
    //     return `${this.firstname} ${this.lastname}`;
    // }

    //ApplicantFamily
    @OneToMany(() => ApplicantFamily, (_) => _.applicant)
    applicantFamilies: Array<ApplicantFamily>;

    //ApplicantEducation
    @OneToMany(() => ApplicantEducation, (_) => _.applicant)
    applicantEducations: Array<ApplicantEducation>;

    //ApplicantWork
    @OneToMany(() => ApplicantWork, (_) => _.applicant)
    applicantWorks: Array<ApplicantWork>;

    //ApplicantTrain
    @OneToMany(() => ApplicantTrain, (_) => _.applicant)
    applicantTrains: Array<ApplicantTrain>;

    //ApplicantTrain
    @OneToMany(() => ApplicantReference, (_) => _.applicant)
    applicantReferences: Array<ApplicantReference>;

    //ApplicantContact
    @OneToMany(() => ApplicantContact, (_) => _.applicant)
    applicantContacts: Array<ApplicantContact>;

    //ApplicantQuestion
    @OneToMany(() => ApplicantQuestion, (_) => _.applicant)
    applicantQuestions: Array<ApplicantQuestion>;

    @OneToMany(() => Candidate, (_) => _.applicant)
    candidates: Candidate[];

    @OneToMany(() => ApplicantFile, (_) => _.applicant)
    applicantFiles: ApplicantFile[];
}
