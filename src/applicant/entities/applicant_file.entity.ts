import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Applicant } from "../../applicant/entities/applicant.entity";

@Entity()
export class ApplicantFile extends CustomBaseEntity {
    @Column({ nullable: true })
    name: string;

    @Column("text", { nullable: true })
    file: string;

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantFiles)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;
}

