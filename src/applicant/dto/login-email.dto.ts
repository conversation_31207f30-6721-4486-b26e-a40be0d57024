import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class LoginEmailDto {

    @IsNotEmpty({ message: 'please input email' })
    @MaxLength(255)
    email: string;

    @IsNotEmpty({ message: 'please input password' })
    @IsString()
    @MinLength(8,{ message: 'Please enter a password of at least 8 characters.' })
    //@Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, { message: 'password too weak' })
    password: string;

}
