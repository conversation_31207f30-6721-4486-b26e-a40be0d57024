import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';

export class UpdateSkillDto {

    @ApiProperty()
    skillLanguage1: string
    @ApiProperty()
    skillLevel1: string
    @ApiProperty()
    skillLanguage2: string
    @ApiProperty()
    skillLevel2: string
    @ApiProperty()
    typingEnSpeed: number
    @ApiProperty()
    typingThSpeed: number
    @ApiProperty()
    computerSkill: string
    @ApiProperty()
    otherSkill: string
}
