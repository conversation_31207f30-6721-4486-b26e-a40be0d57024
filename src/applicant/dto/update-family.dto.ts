import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { ApplicantFamily } from 'src/applicant_family/entities/applicant_family.entity';

export class UpdateFamilyDto {

    @ApiProperty()
    maritalStatus: string;

    @ApiProperty()
    marriedName: string;

    @ApiProperty()
    marriedAge: number;

    @ApiProperty()
    marriedOccupation: string;

    @ApiProperty()
    childrenQty: number;

    @ApiProperty()
    childrenName1: string

    @ApiProperty()
    childrenAge1: number;

    @ApiProperty()
    childrenName2: string;

    @ApiProperty()
    childrenAge2: number;

    @ApiProperty()
    childrenName3: string;

    @ApiProperty()
    childrenAge3: number;

    @ApiProperty()
    childrenName4: string;

    @ApiProperty()
    childrenAge4: number;

    @ApiProperty()
    childrenName5: string;

    @ApiProperty()
    childrenAge5: number;

    @ApiProperty()
    childrenName6: string;

    @ApiProperty()
    childrenAge6: number;

    @ApiProperty()
    readonly applicantFamily: ApplicantFamily[]

}
