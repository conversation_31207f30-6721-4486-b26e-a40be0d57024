import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantReference } from 'src/applicant_reference/entities/applicant_reference.entity';
import { ApplicantQuestion } from 'src/applicant_question/entities/applicant_question.entity';
import { ApplicantContact } from 'src/applicant_contact/entities/applicant_contact.entity';

export class UpdateContactDto {

    @ApiProperty()
    readonly applicantContact: ApplicantContact[];

    @ApiProperty()
    nameOfYouFriend: string

    @ApiProperty()
    anyAdditional: string
}
