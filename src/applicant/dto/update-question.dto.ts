import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantReference } from 'src/applicant_reference/entities/applicant_reference.entity';
import { ApplicantQuestion } from 'src/applicant_question/entities/applicant_question.entity';

export class ApplicantQuestionDto {
    @ApiProperty()
    readonly questionId: number;

    @ApiProperty()
    readonly choiceId: number;
    
    @ApiProperty()
    readonly remark: string;
    
    @ApiProperty()
    readonly remark2: string;
}

export class UpdateQuestion1Dto {
    @ApiProperty()
    readonly applicantQuestion: ApplicantQuestionDto[];
}
