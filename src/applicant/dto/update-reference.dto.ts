import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantReference } from 'src/applicant_reference/entities/applicant_reference.entity';

export class UpdateReferenceDto {
    @ApiProperty()
    readonly applicantReference: ApplicantReference[];
}
