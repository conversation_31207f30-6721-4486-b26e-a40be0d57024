import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class OtpEmailAuthDto {


    @IsNotEmpty({ message: 'please input email' })
    @IsString({ message: 'input type text' })
    @MaxLength(255)
    email: string;

    @IsNotEmpty({ message: 'please input OTP' })
    @IsString({ message: 'input type text' })
    @MinLength(6)
    @MaxLength(6)
    otpCode: string;

    
    @IsNotEmpty({ message: 'please input OTP Ref' })
    @IsString({ message: 'input type text' })
    @MinLength(4)
    @MaxLength(6)
    otpRef: string;
}
