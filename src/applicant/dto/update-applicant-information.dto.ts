import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';

export class UpdateApplicantInformationDto {

    // @IsNotEmpty()
    @ApiProperty()
    image: string;

    @ApiProperty()
    positionAppliedFor: string

    @ApiProperty()
    expectedSalary: number

    @ApiProperty()
    YouKnowFrom: string

    @ApiProperty()
    startDate: Date


    @ApiProperty()
    prefixEn: string

    @ApiProperty()
    fullnameEn: string

    @ApiProperty()
    nicknameEn: string

    @ApiProperty()
    prefixTh: string

    @ApiProperty()
    fullnameTh: string

    @ApiProperty()
    nicknameTh: string

    @ApiProperty()
    permanentAddress: string

    @ApiProperty()
    presentAddress: string

    @ApiProperty()
    homePhone: string

    @ApiProperty()
    mobile: string

    @ApiProperty()
    birthDate: Date

    @ApiProperty()
    age: number

    @ApiProperty()
    placeOfBirth: string

    @ApiProperty()
    height: number

    @ApiProperty()
    weight: number

    @ApiProperty()
    nationality: string

    @ApiProperty()
    idCardNo: string

    @ApiProperty()
    idCardIssuedBy: string

    @ApiProperty()
    idCardExpiryDate: Date

    @ApiProperty()
    passportNo: string

    @ApiProperty()
    passportIssuedBy: string
    
    @ApiProperty()
    passportExpiryDate: Date
    
    // maritalStatus: string
    // marriedName: string
    // marriedAge: number
    // marriedOccupation: string
    // childrenQty: number
    // childrenName1: string
    // childrenAge1: number
    // childrenName2: string
    // childrenAge2: number
    // childrenName3: string
    // childrenAge3: number
    // childrenName4: string
    // childrenAge4: number
    // childrenName5: string
    // childrenAge5: number
    // childrenName6: string
    // childrenAge6: number
 
    // skillLanguage1: string
    // skillLevel1: string
    // skillLanguage2: string
    // skillLevel2: string
    // typingEnSpeed: number
    // typingThSpeed: number
    // computerSkill: string
    // otherSkill: string
    
    // nameOfYouFriend: string
    // anyAdditional: string
    // signatureApplicant: string
    // signatureApplicantDate: Date
}
