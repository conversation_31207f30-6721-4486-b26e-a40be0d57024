import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class ConFirmOtpForgotPasswordDto {

    @IsNotEmpty({ message: 'please input email' })
    @IsString({ message: 'input type text' })
    @MaxLength(255)
    email: string;

    @IsNotEmpty({ message: 'please input OTP' })
    @IsString({ message: 'input type text' })
    @MinLength(6)
    @MaxLength(6)
    otpCode: string;


    @IsNotEmpty({ message: 'please input OTP Ref' })
    @IsString({ message: 'input type text' })
    @MinLength(4)
    @MaxLength(6)
    otpRef: string;

    @IsNotEmpty({ message: 'please input password' })
    @IsString({ message: 'input type text' })
    @MinLength(8,{ message: 'Please enter a password of at least 8 characters.' })
    //@Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, { message: 'password too weak' })
    password: string;

}
