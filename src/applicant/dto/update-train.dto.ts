import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateApplicantDto } from './create-applicant.dto';
import { IsNotEmpty } from 'class-validator';
import { ApplicantEducation } from 'src/applicant_education/entities/applicant_education.entity';
import { ApplicantTrain } from 'src/applicant_train/entities/applicant_train.entity';

export class UpdateTrainDto {

    @ApiProperty()
    readonly applicantTrain: ApplicantTrain[];

}
