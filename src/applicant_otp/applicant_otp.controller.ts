import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantOtpService } from './applicant_otp.service';
import { CreateApplicantOtpDto } from './dto/create-applicant_otp.dto';
import { UpdateApplicantOtpDto } from './dto/update-applicant_otp.dto';

@Controller('applicant-otp')
export class ApplicantOtpController {
  constructor(private readonly applicantOtpService: ApplicantOtpService) {}

  @Post()
  create(@Body() createApplicantOtpDto: CreateApplicantOtpDto) {
    return this.applicantOtpService.create(createApplicantOtpDto);
  }

  @Get()
  findAll() {
    return this.applicantOtpService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantOtpService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantOtpDto: UpdateApplicantOtpDto) {
    return this.applicantOtpService.update(+id, updateApplicantOtpDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantOtpService.remove(+id);
  }
}
