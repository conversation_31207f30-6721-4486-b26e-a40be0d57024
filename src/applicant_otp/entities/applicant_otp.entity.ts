import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";


@Entity()
export class ApplicantOtp extends CustomBaseEntity {

    @Column()
    email: string

    @Column()
    otpRef: string

    @Column({ nullable: true })
    otpCode: string

    @Column({ nullable: true })
    otpExp: string

    @Column({ nullable: true })
    token: string

    @Column({ nullable: true })
    otpType: string

    @Column({ default: false })
    active: boolean
}





