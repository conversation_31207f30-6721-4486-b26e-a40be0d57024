import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantOtpController } from './applicant_otp.controller';
import { ApplicantOtpService } from './applicant_otp.service';

describe('ApplicantOtpController', () => {
  let controller: ApplicantOtpController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantOtpController],
      providers: [ApplicantOtpService],
    }).compile();

    controller = module.get<ApplicantOtpController>(ApplicantOtpController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
