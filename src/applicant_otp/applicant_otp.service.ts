import { Injectable } from '@nestjs/common';
import { CreateApplicantOtpDto } from './dto/create-applicant_otp.dto';
import { UpdateApplicantOtpDto } from './dto/update-applicant_otp.dto';

@Injectable()
export class ApplicantOtpService {
  create(createApplicantOtpDto: CreateApplicantOtpDto) {
    return 'This action adds a new applicantOtp';
  }

  findAll() {
    return `This action returns all applicantOtp`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantOtp`;
  }

  update(id: number, updateApplicantOtpDto: UpdateApplicantOtpDto) {
    return `This action updates a #${id} applicantOtp`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantOtp`;
  }
}
