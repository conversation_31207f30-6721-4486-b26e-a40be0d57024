import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { DateTime } from 'luxon';
import { DataSource } from 'typeorm';
import { CheckInDto } from './check-in.dto';
import { Employee } from './employee/entities/employee.entity';
import { CheckInArea } from './check-in-area/entities/check-in-area.entity';
import { checkLocation } from './common/utils/distance';

@Injectable()
export class AppService {

  constructor(private readonly dataSource: DataSource) { }
  getHello(): string {
    return 'HRM Asah Tech V ' + process.env.npm_package_version;
  }

  async checkIn(payload: CheckInDto) {
    const employeeExist = await Employee.exists({
      where: {
        code: payload.employeeCode,
      }
    });

    if (!employeeExist) {
      throw new NotFoundException('Employee not found');
    }

    //check location
    const locations = await CheckInArea.find({
      where: {
        active: true
      }
    });
    if (locations.length > 0) {
      const found = await checkLocation(payload.latitude, payload.longitude, locations);
      if (!found) {
        throw new BadRequestException('No locations found');
      }
    }

    const today = DateTime.local();

    this.dataSource
      .createQueryBuilder()
      .insert()
      .into("TimeIn_TimeOut")
      .values({
        ID: payload.employeeCode,
        Access_Date_Time: today.toFormat('yyyy-MM-dd HH:mm:ss'), //2025-03-19 16:09:41
        Date: today.toFormat('yyyy-MM-dd'), //2025-03-19
        Time: today.toFormat('HH:mm:ss'), //16:09:41
        latitude: payload.latitude,
        longitude: payload.longitude,
        image: payload.image,
        source: "web"
      })
      .execute();

    return { message: 'Check-in successful' };
  }
}
