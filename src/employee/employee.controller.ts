import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query, UseInterceptors, UploadedFile, ParseFilePipeBuilder, BadRequestException, Req } from '@nestjs/common';
import { EMPLOYEE_PAGINATION_CONFIG, EmployeeService, HISTORY_PAGINATION_CONFIG } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';

import { ApiPaginationQuery, Paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { ApiTags, ApiQuery, ApiConsumes, ApiBody, ApiOperation } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request } from 'express';
import { CreateEmployeeLeavePermissionDto } from 'src/employee-leave-permission/dto/create-employee-leave-permission.dto';
import { LEAVE_PAGINATION_CONFIG, LeaveService } from 'src/leave/leave.service';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { Employee } from './entities/employee.entity';

@Controller('employee')
@ApiTags('พนักงาน')
@Auth()
export class EmployeeController {
  constructor(
    private readonly employeeService: EmployeeService,
    private readonly leaveService: LeaveService
  ) { }

  // @Get('/test')
  // test() {
  //   return this.employeeService.autoGenerateLeave();
  // }

  @Get('/:id/leave-request')
  @Roles(AuthRole.Employee)
  @ApiOperation({ summary: 'รายการที่ขอลา [EMP]' })
  @ApiPaginationQuery(LEAVE_PAGINATION_CONFIG)
  leaveRequestList(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const employeeId = req.user['sub'];

    query.filter = {
      ...query.filter,
      'employee.id': employeeId + ""
    }

    return this.leaveService.datatables(query);
  }

  @Get('/leave-permission')
  @Roles(AuthRole.Employee)
  @ApiOperation({ summary: 'วันลาของพนักงาน [EMP]' })
  getEmployeeLeavePermission(@Req() req: Request) {
    const userId = req.user['sub'];

    return this.employeeService.getEmployeeLeavePermission(userId);
  }

  @Get('/:id/attendances')
  // @Roles(AuthRole.Employee)
  @ApiOperation({ summary: 'วันมาทำงานของพนักงาน [EMP]' })
  getEmployeeAttdance(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Query('year') year: number, @Query('month') month: number) {
    const employeeId = req.user['sub'];

    return this.employeeService.getEmployeeAttdance1(+id, +year, +month);
  }

  @Post('/:id/attendances')
  @ApiOperation({ summary: 'หัวหน้ายืนยันการมาทำงานของพนักงาน' })
  updateEmployeeAttdance(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Body() payload: UpdateAttendanceDto) {
    const employeeId = req.user['sub'];

    return this.employeeService.updateEmployeeAttdance(+id, payload);
  }

  @Post('/force-generate')
  // @Roles(AuthRole.Admin, AuthRole.Hr)
  @ApiOperation({ summary: 'วันลาของพนักงาน [ADMIN,HR]' })
  forceGenerate() {
    return this.employeeService.forceGenerate();
  }

  @Get('/:id/leave-permission')
  // @Roles(AuthRole.Admin, AuthRole.Hr)
  @ApiOperation({ summary: 'วันลาของพนักงาน [ADMIN,HR]' })
  getEmployeeLeavePermissionByid(@Param('id', ParseIntPipe) id: string) {

    return this.employeeService.getEmployeeLeavePermission(id);
  }

  @Get('/:id/leave-history')
  @ApiOperation({ summary: 'ประวัติการลาของพนักงานรายคน [EMPLOYEE]' })
  getEmployeeLeaveHistory(@Param('id', ParseIntPipe) employeeId: string) {
    return this.employeeService.getEmployeeLeaveHistory(employeeId);
  }

  @Get('/:id/leave-permission/initial')
  // @Roles(AuthRole.Admin, AuthRole.Hr)
  @ApiOperation({ summary: 'ดูวันลาที่จะได้ของพนักงาน [ADMIN,HR]' })
  initialEmployeeLeavePermissionByid(@Param('id', ParseIntPipe) id: string) {

    return this.employeeService.initialEmployeeLeavePermissionByid(id);
  }

  // @Post('/:id/leave-permission/create')
  // @Roles(AuthRole.Admin, AuthRole.Hr)
  // @ApiOperation({ summary: 'สร้างวันลาของพนักงาน [ADMIN,HR]'})
  // createEmployeeLeavePermissionByid(@Param('id', ParseIntPipe) id: string) {

  //   return this.employeeService.createEmployeeLeavePermission(id);
  // }

  @Put('/:id/leave-permission/update')
  // @Roles(AuthRole.Admin, AuthRole.Hr)
  @ApiOperation({ summary: 'แก้ไขวันลาของพนักงาน [ADMIN,HR]' })
  updateEmployeeLeavePermissionByid(@Param('id', ParseIntPipe) id: string, @Body() payload: CreateEmployeeLeavePermissionDto) {

    return this.employeeService.updateEmployeeLeavePermission(+id, payload);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '[ADMIN,HR]' })
  @ApiPaginationQuery(EMPLOYEE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.employeeService.datatables(query);
  }

  @Get('history-datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Time Attendance History' })
  @ApiPaginationQuery(HISTORY_PAGINATION_CONFIG)
  historyDatatables(@Paginate() query: PaginateQuery) {
    return this.employeeService.historyDatatables(query);
  }

  @Get('attendance-history-check-datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '[ADMIN,HR]' })
  @ApiQuery({ name: 'year', required: false })
  @ApiQuery({ name: 'month', required: false })
  @ApiPaginationQuery(EMPLOYEE_PAGINATION_CONFIG)
  getEmployeeAttendanceHistoryCheckDatatables(@Paginate() query: PaginateQuery, @Query() filter) {
    return this.employeeService.getEmployeeAttendanceHistoryCheckDatatables(query, filter.year, filter.month);
  }

  @Get('/auto-code')
  autoCode(@Query('t') t: string) {
    return this.employeeService.autoCode(t)
  }

  @Post()
  // @Roles(AuthRole.Admin, AuthRole.Hr, AuthRole.AdminHr)
  create(@Body() createEmployeeDto: CreateEmployeeDto) {
    return this.employeeService.create(createEmployeeDto);
  }

  @Get()
  @ApiQuery({ name: 'titleId', required: false })
  @ApiQuery({ name: 'levelId', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  @ApiQuery({ name: 'employeeTypeId', required: false })
  @ApiQuery({ name: 'workShiftId', required: false })
  @ApiQuery({ name: 'headId', required: false })
  findAll(@Query() query) {
    return this.employeeService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.employeeService.findOne(+id);
  }

  @Put(':id')
  // @Roles(AuthRole.Admin, AuthRole.Hr, AuthRole.AdminHr)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateEmployeeDto: UpdateEmployeeDto) {
    return this.employeeService.update(+id, updateEmployeeDto);
  }

  @Delete(':id')
  // @Roles(AuthRole.Admin, AuthRole.AdminHr)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.employeeService.remove(+id);
  }

  @Post('import-excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async importEmployee(@UploadedFile(
    new ParseFilePipeBuilder()
      .addFileTypeValidator({ fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }) // Adjusted regex to accept .txt as well
      .addMaxSizeValidator({ maxSize: 1000 * 1000 * 10 }) // 10 MB
      .build({
        errorHttpStatusCode: HttpStatus.BAD_REQUEST

      })
  ) file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.employeeService.import(file);
  }

}


