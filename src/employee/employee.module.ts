import { Module } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkShift } from 'src/work-shift/entities/work-shift.entity';
import { Employee } from './entities/employee.entity';
import { Title } from 'src/title/entities/title.entity';
import { Level } from 'src/level/entities/level.entity';
import { Group } from 'src/group/entities/group.entity';
import { EmployeeType } from 'src/employee-type/entities/employee-type.entity';
import { Department } from 'src/department/entities/department.entity';
import { ReportService } from 'src/report/report.service';
import { LeaveModule } from 'src/leave/leave.module';

@Module({
  imports: [TypeOrmModule.forFeature([Employee, Title, Level, Group, EmployeeType, Department, WorkShift]), LeaveModule],
  controllers: [EmployeeController],
  providers: [EmployeeService, ReportService],
  exports: [EmployeeService]
})
export class EmployeeModule { }
