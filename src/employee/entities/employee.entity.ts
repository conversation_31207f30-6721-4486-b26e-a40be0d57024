import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude } from "class-transformer";
import { Title } from "../../title/entities/title.entity";
import { Level } from "../../level/entities/level.entity";
import { Department } from "../../department/entities/department.entity";
import { WorkShift } from "../../work-shift/entities/work-shift.entity";
import { EmployeeType } from "../../employee-type/entities/employee-type.entity";
import { Leave } from "../../leave/entities/leave.entity";
import { EmployeeLeavePermission } from "../../employee-leave-permission/entities/employee-leave-permission.entity";
import { DateTime } from "luxon";
import { Attandance } from "../../attandances/entities/attandance.entity";
import { CheckInCheckOut } from "../../attandances/entities/check-in-check-out.entity";
import { ViewAttendance } from "../../attandances/entities/vw-attendance.entity";
import { HistoryAttandance } from "../../attandances/entities/history-attandance.entity";
import { Project } from "../../project/entities/project.entity";
import { Ot } from "../../ot/entities/ot.entity";
import { OtAir } from "../../ot-air/entities/ot-air.entity";
import { PersonalForm } from "../../personal-form/entities/personal-form.entity";
import { Contact } from "../../contact/entities/contact.entity";
import { ContactActivity } from "src/contact_activity/entities/contact_activity.entity";
import { Candidate } from "src/candidate/entities/candidate.entity";

export enum GenderEnum {
    MALE = 'male',
    FEMALE = 'female'
}

export enum EmployeeActiveEnum {
    /**Permanent */
    PER = 'PER',
    /**Fixed-term Contract */
    FTC = 'FTC',
    /**Resigned */
    RES = 'RES',
}

export enum EmployeeStatusEnum {
    UNDER_PROBATION = 'under_probation',
    PASSED_PROBATION = 'passed_probation',
    DIS_NOT_PASS_PROBATION = 'dis_not_pass_probation'
}

@Entity()
@Unique(['code', 'username'])
export class Employee extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Index({ unique: true })
    @Column({ nullable: true })
    username: string;

    @Column({ nullable: true, select: false })
    @Exclude()
    password: string

    @Column()
    firstname: string;

    @Column()
    lastname: string;

    @Column({ nullable: true })
    initial: string

    @Column({ nullable: true })
    email: string

    @Column("date", { nullable: true })
    birthDate: Date

    @Column("date", { nullable: true })
    registerDate: Date

    @Column("date", { nullable: true })
    currentStartPeriod: Date

    @Column("date", { nullable: true })
    currentEndPeriod: Date

    @Column("date", { nullable: true })
    passProbationDate: Date

    @Column("date", { nullable: true })
    nextQuotaUpdate: Date

    @Column({
        type: "enum",
        enum: GenderEnum,
        nullable: true
    })
    sex: GenderEnum;

    @Column({ type: 'enum', enum: EmployeeActiveEnum, nullable: true })
    active: EmployeeActiveEnum

    @Column({ default: false })
    isApprover: boolean

    @Column({ type: 'enum', enum: EmployeeStatusEnum, nullable: true })
    employeeStatus: EmployeeStatusEnum;

    total_case1: number
    total_case2: number
    total_case3: number
    total_case4: number
    total_deduct_min: number
    create_history_status: boolean
    create_history_attendance_detail: any

    //title
    @ManyToOne(() => Title, (_) => _.employees)
    @JoinColumn({ name: 'title_id' })
    title: Title;

    //level
    @ManyToOne(() => Level, (_) => _.employees)
    @JoinColumn({ name: 'level_id' })
    level: Level;

    //department
    @ManyToOne(() => Department, (_) => _.employees)
    @JoinColumn({ name: 'department_id' })
    department: Department;

    //employeeType
    @ManyToOne(() => EmployeeType, (_) => _.employees)
    @JoinColumn({ name: 'employee_type_id' })
    employeeType: EmployeeType;

    //WorkShift
    @ManyToOne(() => WorkShift, (_) => _.employees)
    @JoinColumn({ name: 'work_shift_id' })
    workShift: WorkShift;

    //head
    @ManyToOne(() => Employee, (_) => _.employees)
    @JoinColumn({ name: 'head_id' })
    head: Employee;

    @OneToMany(() => Employee, (_) => _.head)
    employees: Array<Employee>;
    //

    //Contact
    // @OneToMany(() => Contact, (_) => _.employee)
    // contacts: Array<Contact>;

    //Leave
    @OneToMany(() => Leave, (_) => _.employee)
    leaves: Array<Leave>;

    //ot
    @OneToMany(() => Ot, (_) => _.employee)
    ots: Array<Ot>;

    //OtAir
    @OneToMany(() => OtAir, (_) => _.employee)
    otAirs: Array<OtAir>;

    //Project
    @OneToMany(() => Project, (_) => _.employee)
    projects: Array<Project>;


    @OneToMany(() => EmployeeLeavePermission, (_) => _.employee)
    employeeLeavePermissions: EmployeeLeavePermission[];

    @OneToMany(() => Attandance, (_) => _.employee)
    attandances: Array<Attandance>;

    @OneToMany(() => HistoryAttandance, (_) => _.employee)
    historyAttandances: HistoryAttandance[];

    @OneToMany(() => ViewAttendance, (_) => _.employee)
    vwAttendance: ViewAttendance[];

    @OneToMany(() => CheckInCheckOut, (_) => _.employee)
    checkInCheckOuts: Array<CheckInCheckOut>;

    @OneToMany(() => PersonalForm, (_) => _.requestBy)
    personalFormsRequest: PersonalForm[];

    @OneToMany(() => PersonalForm, (_) => _.approveBy)
    personalFormsApprove: PersonalForm[];

    // @OneToMany(() => PersonalForm, (_) => _.organisedBy)
    // personalFormsOrganise: PersonalForm[];

    @OneToMany(() => ContactActivity, (_) => _.employee)
    contactActivities: Array<ContactActivity>;

    @OneToMany(() => Candidate, (_) => _.interviewer1)
    candidates1: Candidate[];

    @OneToMany(() => Candidate, (_) => _.interviewer2)
    candidates2: Candidate[];

    @OneToMany(() => Candidate, (_) => _.interviewer3)
    candidates3: Candidate[];

    get fullname(): string {
        return this.firstname + ' ' + this.lastname
    }

    get totalWorkYear(): number {
        const now = DateTime.now();
        const hireDate = DateTime.fromISO(this.registerDate.toString());

        const totalYears = now.diff(hireDate, 'years').years;

        return Math.floor(totalYears);
    }

    get totalWorkMonth(): number {
        const now = DateTime.local();
        const hireDate = DateTime.fromISO(this.registerDate.toString());

        const totalMonth = now.diff(hireDate, 'months').months;

        return Math.floor(totalMonth);
    }

    get totalWorkDay(): number {
        const now = DateTime.local();
        const hireDate = DateTime.fromISO(this.registerDate.toString());

        const total = now.diff(hireDate, 'days').days;

        return Math.floor(total);
    }
}



