import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { Employee, EmployeeActiveEnum, EmployeeStatusEnum } from './entities/employee.entity';
import {
  Between,
  DataSource,
  In,
  IsNull,
  LessThanOrEqual,
  Like,
  MoreThan,
  MoreThanOrEqual,
  Not,
  Raw,
  Repository,
} from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import * as xlsx from 'xlsx';
import { Helper } from 'src/common/utils/helper';
import { Title } from 'src/title/entities/title.entity';
import { Level } from 'src/level/entities/level.entity';
import { EmployeeType } from 'src/employee-type/entities/employee-type.entity';
import { WorkShift } from 'src/work-shift/entities/work-shift.entity';
import { Department } from 'src/department/entities/department.entity';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { DateTime } from 'luxon';
import { groupBy } from 'lodash';
import { Leave, LeaveStatusEnum } from 'src/leave/entities/leave.entity';
import { Attandance, AttandanceConfirmStatusEnum } from 'src/attandances/entities/attandance.entity';
import { LeaveDate } from 'src/leave/entities/leave-date.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { Holiday } from 'src/holiday/entities/holiday.entity';
import { IAttandance } from 'src/attandances/attandance.interface';
import { HistoryAttandance, HistoryAttandanceStatusEnum } from 'src/attandances/entities/history-attandance.entity';
import { AuthRole } from 'src/auth/auth.interface';
import { CreateEmployeeLeavePermissionDto, EmployeeLeavePermissionDto } from 'src/employee-leave-permission/dto/create-employee-leave-permission.dto';
import { calculateNextPeriod } from 'src/common/utils/DatetimeUtil';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';
import { Ot, OtStatus } from 'src/ot/entities/ot.entity';

export const EMPLOYEE_PAGINATION_CONFIG: PaginateConfig<Employee> = {
  relations: [
    'title',
    'level',
    'department.group',
    'employeeType',
    'workShift',
    'head',
  ],
  sortableColumns: [
    'id',
    'code',
    'firstname',
    'lastname',
    'email',
    'sex',
    'birthDate',
    'registerDate',
    'passProbationDate',
    'active',
    'employeeStatus',
    'title.id',
    'title.code',
    'title.name',
    'level.id',
    'level.code',
    'level.name',
    'department.id',
    'department.code',
    'department.name',
    'department.group.id',
    'department.group.code',
    'department.group.name',
    'employeeType.id',
    'employeeType.code',
    'employeeType.name',
    'workShift.id',
    'workShift.code',
    'workShift.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
  ],
  select: [
    'id',
    'code',
    'firstname',
    'lastname',
    'email',
    'sex',
    'birthDate',
    'registerDate',
    'passProbationDate',
    'active',
    'employeeStatus',
    'createdAt',
    'title.id',
    'title.code',
    'title.name',
    'level.id',
    'level.code',
    'level.name',
    'department.id',
    'department.code',
    'department.name',
    'department.group.id',
    'department.group.code',
    'department.group.name',
    'employeeType.id',
    'employeeType.code',
    'employeeType.name',
    'workShift.id',
    'workShift.code',
    'workShift.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
  ],
  searchableColumns: ['code', 'firstname', 'lastname', 'email', 'employeeStatus'],
  filterableColumns: {
    id: [FilterOperator.EQ],
    title: [FilterOperator.EQ],
    level: [FilterOperator.EQ],
    department: [FilterOperator.EQ],
    employeeType: [FilterOperator.EQ],
    head: [FilterOperator.EQ],
    isApprover: [FilterOperator.EQ],
    employeeStatus: [FilterOperator.EQ, FilterOperator.IN],
  },
};

export const HISTORY_PAGINATION_CONFIG: PaginateConfig<HistoryAttandance> = {
  relations: {
    employee: {
      head: true
    }
  },
  sortableColumns: ['year', 'month', 'case1', 'case2', 'case3', 'case4', 'confirmStatus', 'headConfirmStatus'],
  searchableColumns: ['year', 'month'],
  filterableColumns: {
    year: [FilterOperator.EQ],
    month: [FilterOperator.EQ],
    'employee.id': [FilterOperator.EQ],
    confirmStatus: [FilterOperator.EQ],
    headConfirmStatus: [FilterOperator.EQ],
    'employee.head.id': [FilterOperator.EQ],
  },
};

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    private dataSource: DataSource,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
  ) { }

  // async test() {

  // }

  async datatables(query: PaginateQuery): Promise<Paginated<Employee>> {
    return paginate(query, this.employeeRepository, EMPLOYEE_PAGINATION_CONFIG);
  }

  async historyDatatables(query: PaginateQuery): Promise<Paginated<HistoryAttandance>> {
    return paginate(query, HistoryAttandance.getRepository(), HISTORY_PAGINATION_CONFIG);
  }

  async create(createEmployeeDto: CreateEmployeeDto) {
    const {
      titleId,
      levelId,
      departmentId,
      employeeTypeId,
      workShiftId,
      headId,
      // registerDate,
      ...data
    } = createEmployeeDto;

    // const { startPeriod, endPeriod } = calculateNextPeriod(new Date(registerDate));

    //check employee code exist
    const check = await Employee.existsBy({
      code: createEmployeeDto?.code,
    });
    if (check) {
      throw new BadRequestException('employee code already.');
    }

    // const hash = await argon2.hash(createEmployeeDto.password);

    const item = this.employeeRepository.create({
      ...data,
      // password: hash,
      title: { id: titleId },
      level: { id: levelId },
      department: { id: departmentId },
      employeeType: { id: employeeTypeId },
      workShift: { id: workShiftId },
      head: { id: headId },
      nextQuotaUpdate: data.passProbationDate,
      // currentStartPeriod: startPeriod,
      // currentEndPeriod: endPeriod,
    });

    return this.employeeRepository.save(item);
  }

  findAll(query) {
    const titleId = query.titleId;
    const levelId = query.levelId;
    const departmentId = query.departmentId;
    const employeeTypeId = query.employeeTypeId;
    const workShiftId = query.workShiftId;
    const headId = query.headId;

    return this.employeeRepository.find({
      relations: ['title', 'level', 'department.group', 'employeeType', 'head'],
      where: {
        title: {
          id: titleId ? titleId : null,
        },
        level: {
          id: levelId ? levelId : null,
        },
        department: {
          id: departmentId ? departmentId : null,
        },
        employeeType: {
          id: employeeTypeId ? employeeTypeId : null,
        },
        workShift: {
          id: workShiftId ? workShiftId : null,
        },
        head: {
          id: headId ? headId : null,
        },
      },
    });
  }

  async findOne(id: number) {
    const item = await this.employeeRepository.findOne({
      relations: [
        'title',
        'level',
        'department.group',
        'employeeType',
        'head',
        'workShift',
      ],
      where: { id },
    });

    if (!item) throw new NotFoundException('employee not found');

    return item;
  }

  async update(id: number, updateEmployeeDto: UpdateEmployeeDto) {
    //check employee code exist
    const check = await Employee.existsBy({
      code: updateEmployeeDto?.code,
      id: Not(id),
    });
    if (check) {
      throw new BadRequestException('Employee code already.');
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException('employee not found');

    const {
      titleId,
      levelId,
      departmentId,
      employeeTypeId,
      workShiftId,
      headId,
      ...data
    } = updateEmployeeDto;

    const emp = Employee.create({
      ...data,
      title: { id: titleId },
      level: { id: levelId },
      department: { id: departmentId },
      employeeType: { id: employeeTypeId },
      workShift: { id: workShiftId },
      head: { id: headId },
    })

    if (item.passProbationDate != emp.passProbationDate) {
      emp.nextQuotaUpdate = emp.passProbationDate
    }

    return this.employeeRepository.update(id, {
      ...emp,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException('employee not found');

    item.code = `${Date.now()}-${item.code}`;
    await item.save();

    await this.employeeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.employeeRepository.findOne({ where: { id } });
  }

  async newEmployee(
    data: any,
  ): Promise<{ status: 'ok' | 'error'; message?: string }> {
    try {
      // const requiredFields = ['firstname', 'lastname', 'username', 'sex', 'title', 'level', 'employeeType', 'work_shift_id'];
      // for (const field of requiredFields) {
      //   if (!data[field]) {
      //     console.log(field);
      //     return { status: 'error', message: `${field} is required.` };

      //   }
      // }
      const titleName = data['title'].trim();
      const title = await Title.findOne({ where: { name: titleName } });
      // if (!title) {
      //   throw new BadRequestException('Title not found.');
      // }
      const levelName = data['level'].trim();
      const level = await Level.findOne({ where: { name: levelName } });
      if (!level) {
        throw new BadRequestException('Level not found.');
      }

      const employeeType = await EmployeeType.findOne({
        where: { name: 'Office' },
      });
      if (!employeeType) {
        throw new BadRequestException('Employee Type not found.');
      }

      const workShift = await WorkShift.findOne({
        where: { name: data['work_shift'] },
      });
      if (!workShift) {
        throw new BadRequestException('Work Shift not found.');
      }

      const department = await Department.findOne({
        where: { name: data['department'] },
      });
      // if (!department) {
      //   throw new BadRequestException('Department not found.');
      // }
      // const existingEmployee = await Employee.findOne({
      //   where: { code: data.code },
      // });
      // if (existingEmployee) {
      //   return { status: 'error', message: 'Employee with this code already exists.' };
      // }

      const newEmployee = Employee.create({
        code: data.code,
        firstname: data.firstname,
        lastname: data.lastname,
        username: data.username,
        initial: data.initial,
        sex: data.gender,
        email: data.email,
        birthDate: data.birth_date,
        registerDate: data.register_date,
        title: title,
        level: level,
        employeeType: employeeType,
        workShift: workShift,
        department: department,
      });

      await newEmployee.save();

      return { status: 'ok' };
    } catch (error) {
      console.log(error);
      return { status: 'error', message: error.message };
    }
  }

  private async updateEmployee(
    data: any,
  ): Promise<{ status: 'ok' | 'error'; message?: string }> {
    try {
      const requiredFields = [
        'firstname',
        'lastname',
        'username',
        'sex',
        'title',
        'level',
        'employeeType',
        'work_shift_id',
      ];
      for (const field of requiredFields) {
        if (!data[field]) {
          console.log(`${field} There is a vacancy`);
          return { status: 'error', message: `${field} is required.` };
        }
      }
      const title = await Title.findOne({ where: { name: data['title'] } });
      const level = await Level.findOne({ where: { name: data['level'] } });
      const employeeType = await EmployeeType.findOne({
        where: { name: data['employeeType'] },
      });
      const workShift = await WorkShift.findOne({
        where: { name: data['workShift'] },
      });
      const result = await Employee.update(
        { code: data.code },
        {
          firstname: data.firstname,
          lastname: data.lastname,
          username: data.username,
          sex: data.sex,
          active: data.active,
          title: title,
          level: level,
          employeeType: employeeType,
          workShift: workShift,
          employeeStatus: data.employeeStatus,
        },
      );
      return { status: 'ok' };
    } catch (error) {
      console.log(error);
      return { status: 'error', message: error.message };
    }
  }
  async import(file: Express.Multer.File) {
    const head = [
      'code',
      'username',
      'firstname',
      'lastname',
      'initial',
      'email',
      'level',
      'title',
      'department',
      'birth_date',
      'register_date',
      'gender',
      'work_shift',
    ];
    const workbook = xlsx.read(file.buffer, {
      type: 'buffer',
      cellDates: true,
    });

    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1 });
    const actualHeaders: string[] = jsonData[0] as Array<string>;

    const isValid = head.every((header) => actualHeaders.includes(header));
    if (!isValid) {
      throw new BadRequestException('Header validation failed.');
    }
    const jsonDataWithHeaders: Employee[] = xlsx.utils.sheet_to_json(sheet);

    const employees = Helper.duplicateArrayValue(
      jsonDataWithHeaders.map((e) => `${e['code']}`),
    );
    if (employees.length) {
      throw new BadRequestException(
        'Duplicate employee code found: ' + employees,
      );
    }

    const result = {
      create: 0,
      update: 0,
      ok: 0,
      error: 0,
    };

    const createEmployeePromises = [];
    const updateEmployeePromises = [];
    const transformEmployeeData = (data) => {
      return {
        ...data,
        gender: data.gender.toLowerCase(),
      };
    };
    for (const employeeData of jsonDataWithHeaders) {
      const transformedEmployeeData = transformEmployeeData(employeeData);

      const existingEmployee = await this.employeeRepository.findOne({
        where: { code: transformedEmployeeData['code'] },
      });
      if (existingEmployee) {
        // ถ้ามีพนักงานอยู่แล้ว ทำการอัปเดต
        // updateEmployeePromises.push(this.updateEmployee(transformedEmployeeData));
      } else {
        // ถ้าไม่มีพนักงาน สร้างใหม่
        createEmployeePromises.push(this.newEmployee(transformedEmployeeData));
      }
    }

    const resultCreate = await Promise.all(createEmployeePromises);
    const resultUpdate = await Promise.all(updateEmployeePromises);

    result.create = resultCreate.filter((e) => e.status === 'ok').length;
    result.update = resultUpdate.filter((e) => e.status === 'ok').length;
    result.ok = result.create + result.update;
    result.error =
      resultCreate.filter((e) => e.status === 'error').length +
      resultUpdate.filter((e) => e.status === 'error').length;

    return result;
  }

  async getEmployeeAttdance1(employeeId: number, year: number, month: number) {
    const start = DateTime.local(year, month).startOf('month')
    const end = DateTime.local(year, month).endOf('month')

    const employee = await Employee.findOne({
      where: {
        id: employeeId,
        vwAttendance: {
          date: Between(start.toFormat('yyyy-MM-dd'), end.toFormat('yyyy-MM-dd'))
        }
      },
      relations: {
        employeeType: true,
        vwAttendance: true,
        workShift: {
          workShiftTimes: true
        },
        leaves: true,
      }
    })

    if (!employee) {
      return { message: 'ok', data: [] }
    }

    const otsQuery = Ot.find({
      where: {
        employee: {
          id: employeeId
        },
        status: OtStatus.approved,
        date: Between(start.toJSDate(), end.toJSDate())
      }
    })

    const [leaveDates, leaveTypes, holidays, oldAttandances, ots] = await Promise.all([
      LeaveDate.find({
        where: {
          leave: {
            employee: {
              id: employeeId
            },
            status: LeaveStatusEnum.APPROVED
          },
          date: Between(start.toJSDate(), end.toJSDate())
        },
        relations: {
          leave: {
            leaveType: true
          }
        }
      }),
      LeaveType.find({ order: { code: 'ASC' } }),
      this.dataSource
        .createQueryBuilder(Holiday, 'holiday')
        .leftJoinAndSelect('holiday.employeeType', 'employeeType')
        .where("DATE_PART('Year', date) = :year", { year: year })
        .getMany(),
      Attandance.find({
        where: {
          workDate: Between(start.toJSDate(), end.toJSDate()),
          employee: {
            id: employeeId
          }
        }
      }),
      otsQuery
    ])

    const oldAttandancesGroup = groupBy(oldAttandances, 'workDate')

    const leaveDateGroups = groupBy(leaveDates, 'date')

    const holidayGroupDate = groupBy(holidays, (e) => {
      return e.date + "#" + e.employeeType.id
    })

    const otGroups = groupBy(ots, 'date')

    const attandanceForCreate: IAttandance[] = [];

    const dayOfMonth = DateTime.local(year, month).daysInMonth

    const attandances: { [key: string]: IAttandance } = {};

    // สร้างวันมารอให้ครบ 1 เดือน
    for (let index = 1; index <= dayOfMonth; index++) {
      const date = DateTime.local(year, month, index)
      const dateStr = date.toFormat('yyyy-MM-dd')

      const leave = leaveDateGroups[dateStr]

      const IAtten: IAttandance = {
        id: null,
        employeeId: employee.id,
        date: dateStr,
        workStart: null,
        workEnd: null,
        timeIn: null,
        timeOut: null,
        day: date.weekdayShort,
        case1: 0,
        case2: 0,
        case3: 0,
        case4: 0,
        status: null,
        reason: null,
        explanation: null,
        workHrs: 0,
        otHrs: 0,
        calcHrs: 0,
        isWorkday: false,
        isHoliday: false,
        confirmStatus: AttandanceConfirmStatusEnum.DRAFT,
        leaveCode: leave?.length > 0 ? leave[0].leave.leaveType.code : null,
        leaveName: leave?.length > 0 ? leave[0].leave.leaveType.name : null,
        leaveQty: leave?.length > 0 ? leave[0].qty : 0,
        color: '#E6F7FF',
        deduct: false,
        leaveType: []
      }

      attandances[dateStr] = IAtten
    }

    //เริ่มนำข้อมูลมาใส่ในแต่ละวัน
    const { vwAttendance, workShift } = employee

    const workDayGroup = groupBy(workShift.workShiftTimes, 'day')

    const vwAttendanceDateTime = groupBy(vwAttendance, function (data) {
      return DateTime.fromJSDate(new Date(data.date)).toLocal().toFormat('yyyy-MM-dd');
    })

    for (const [key, value] of Object.entries(attandances)) {
      const atten = oldAttandancesGroup[key];

      // work day
      const dayOfWeek = DateTime.fromSQL(key).weekday

      attandances[key].isWorkday = workDayGroup[dayOfWeek][0].active
      attandances[key].workStart = workDayGroup[dayOfWeek][0].time_in
      attandances[key].workEnd = workDayGroup[dayOfWeek][0].time_out

      //existing attendance
      if (atten != undefined && atten.length) {
        attandances[key].id = atten[0].id;
        attandances[key].timeIn = atten[0].checkIn;
        attandances[key].timeOut = atten[0].checkOut;
        attandances[key].calcHrs = atten[0].expectedHours;
        attandances[key].workHrs = atten[0].workedHours;
        attandances[key].otHrs = atten[0].overtimeHours;
        attandances[key].confirmStatus = atten[0].confirmStatus;
        attandances[key].reason = atten[0].reason;
        attandances[key].deduct = atten[0].deduct;
        attandances[key].explanation = atten[0].explanation;
      } else if (vwAttendanceDateTime[key] != undefined) {
        attandances[key].timeIn = vwAttendanceDateTime[key][0].min;
        attandances[key].timeOut = vwAttendanceDateTime[key][0].max;

        const date1 = DateTime.fromFormat(attandances[key].timeIn.toString(), 'HH:mm:ss').set({ second: 0 });
        const date2 = DateTime.fromFormat(attandances[key].timeOut.toString(), 'HH:mm:ss').set({ second: 0 });

        if (date2 > date1) {
          const { minutes } = date2.diff(date1, 'minutes').toObject();
          attandances[key].calcHrs = minutes;

          // ลบเวลาพักออก (สมมติพัก 60 นาที)
          attandances[key].workHrs = Math.max(0, minutes - 60);
        } else {
          attandances[key].calcHrs = 0;
          attandances[key].workHrs = 0;
        }
      }

      const isOt = otGroups[key] ?? false;

      // ตรวจสอบให้แน่ใจว่ามี timeIn,workStar และเป็นวันทำงาน และไม่มีใบลา และไม่มีใบขอ OT อยู่ก่อนคำนวณเวลาล่าช้า
      if (attandances[key].timeIn && attandances[key].workStart && attandances[key].isWorkday && !isOt) {
        const timeIn = DateTime.fromFormat(attandances[key].timeIn.toString(), 'HH:mm:ss').set({ second: 0 });
        const workStart = DateTime.fromFormat(attandances[key].workStart.toString(), 'HH:mm:ss').set({ second: 0 });
        if (timeIn > workStart) {
          const { minutes: lateMinutes } = timeIn.diff(workStart, 'minutes').toObject();
          if (lateMinutes > 150) {
            attandances[key].case4 = 1;
          } else if (lateMinutes > 90) {
            attandances[key].case3 = 1;
          } else if (lateMinutes > 60) {
            attandances[key].case2 = 1;
          } else if (lateMinutes > 30) {
            attandances[key].case1 = 1;
          }
        }
      }

      //holiday
      const holiday4emp = key + '#' + employee.employeeType.id
      attandances[key].isHoliday = holidayGroupDate[holiday4emp] != undefined

      //leave day
      attandances[key].leaveType = leaveTypes.map(e => {
        let useDay = 0
        if (e.name == attandances[key].leaveName) {
          useDay = attandances[key].leaveQty
        }

        return ({
          name: e.name,
          useDay: useDay
        });
      })

      // color (ควรวางไว้หลังสุดใน loop)
      if (
        attandances[key].isWorkday &&
        attandances[key].leaveQty == 0 &&
        attandances[key].timeIn == null &&
        attandances[key].timeOut == null
      ) {
        attandances[key].case4 = 1;
        attandances[key].color = '#F7C6AC'; // ขาดงานไม่มีลาและไม่มีเวลามาทำงาน
      }

      if (attandances[key].case1 > 0 ||
        attandances[key].case2 > 0 ||
        attandances[key].case3 > 0 ||
        attandances[key].case4 > 0
      ) {
        attandances[key].color = '#F7C6AC'; // มาสาย/ลา/ทำงานไม่ครบ
      }

      if (attandances[key].leaveQty > 0) {
        attandances[key].color = '#A6C9EC'; // มาสาย/ลา/ทำงานไม่ครบ
      }

      //วันหยุดนักขัตฤกษ์
      if (attandances[key].isHoliday) {
        attandances[key].color = '#D0D0D0'; // วันหยุด
        attandances[key].case1 = 0;
        attandances[key].case2 = 0;
        attandances[key].case3 = 0;
        attandances[key].case4 = 0;
      }

      //วันหยุด Day Off
      if (attandances[key].isWorkday == false) {
        attandances[key].color = '#ADADAD'; // วันหยุด
        attandances[key].case1 = 0;
        attandances[key].case2 = 0;
        attandances[key].case3 = 0;
        attandances[key].case4 = 0;
      }

      const leave = attandances[key].leaveType.reduce((acc, e) => {
        acc[e.name.slice(0, 3)] = e.useDay;
        return acc;
      }, {});

      Object.assign(attandances[key], leave)
    }

    attandanceForCreate.push(...Object.values(attandances))


    //get history
    const historyAttandance = await HistoryAttandance.findOne({
      relations: ['employee'],
      where: {
        employee: {
          id: employeeId,
        },
        year: year,
        month: month,
      },
    });
    //

    return { message: 'ok', attandanceHistory: historyAttandance, data: attandanceForCreate }
  }

  async getEmployeeAttdance(employeeId: any, year: number, month: number) {
    const start = DateTime.local(+year, +month).startOf('month');
    const end = DateTime.local(+year, +month).endOf('month');

    const attandances = await this.dataSource
      .createQueryBuilder(Attandance, 'attendance')
      .select([
        'attendance.id',
        'attendance.employee_id',
        'attendance.work_date',
        'attendance.work_start',
        'attendance.work_end',
        'attendance.check_in',
        'attendance.check_out',
        'attendance.worked_hours',
        'attendance.overtime_hours',
        'attendance.expected_hours',
        'attendance.is_workday',
        'attendance.is_holiday',
        'attendance.confirm_status',
        'attendance.reason',
        'attendance.explanation',
        'attendance.status',
        'attendance.deduct',
        'l.leave_code',
        'l.leave_name',
        'l.leave_qty'
      ])
      .leftJoin(
        qb => {
          return qb
            .select([
              'leave.employee_id',
              'leave_date.date AS leave_date',
              'leave_date.qty AS leave_qty',
              'leave_type.code AS leave_code',
              'leave_type.name AS leave_name'
            ])
            .from(LeaveDate, 'leave_date')
            .innerJoin(Leave, 'leave', 'leave_date.leave_id = leave.id')
            .innerJoin(LeaveType, 'leave_type', 'leave.leave_type_id = leave_type.id')
            .where('leave.status = :status', { status: LeaveStatusEnum.APPROVED })
            .andWhere('leave.employee_id = :employeeId', { employeeId: employeeId });
        },
        'l',
        'attendance.work_date = l.leave_date AND attendance.employee_id = l.employee_id'
      )
      .where('attendance.employee_id = :employeeId', { employeeId: employeeId })
      .andWhere('attendance.workDate BETWEEN :start AND :end', { start: start.toJSDate(), end: end.toJSDate() })
      .getRawMany()

    const result = []

    if (attandances.length == 0) {
      return result
    }

    const leaveTypes = await LeaveType.find({ order: { code: 'ASC' } });

    for (const attandance of attandances) {
      const dict: IAttandance = {
        id: attandance['attendance_id'],
        employeeId: attandance['employee_id'],
        date: attandance['work_date'],
        workStart: attandance['work_start'],
        workEnd: attandance['work_end'],
        timeIn: attandance['check_in'],
        timeOut: attandance['check_out'],
        day: DateTime.fromJSDate(attandance['work_date']).weekdayShort,
        status: attandance['attendance_status'],
        reason: attandance['attendance_reason'],
        explanation: attandance['attendance_explanation'],
        workHrs: attandance['worked_hours'],
        otHrs: attandance['overtime_hours'],
        calcHrs: attandance['expected_hours'],
        isWorkday: false,
        isHoliday: false,
        confirmStatus: attandance['confirm_status'],
        leaveCode: attandance['leave_code'],
        leaveName: attandance['leave_name'],
        leaveQty: attandance['leave_qty'],
        case1: 0,
        case2: 0,
        case3: 0,
        case4: 0,
        leaveType: [],
        color: '#E7E9FD',
        deduct: attandance['attendance_deduct']
      };

      //late
      if (dict.timeIn && dict.workStart) {
        const checkIn = DateTime.fromFormat(dict.timeIn, 'HH:mm:ss')
        const workStart = DateTime.fromFormat(dict.workStart, 'HH:mm:ss')

        const { minutes } = checkIn.diff(workStart, 'minutes').toObject()

        if (minutes > 150) {
          dict.case4 = 1
        } else if (minutes > 90) {
          dict.case3 = 1
        } else if (minutes > 60) {
          dict.case2 = 1
        } else if (minutes > 30) {
          dict.case1 = 1
        }
      }

      //leave day
      dict.leaveType = leaveTypes.map(e => {
        let useDay = 0
        if (e.name == dict.leaveName) {
          useDay = dict.leaveQty
        }

        return ({
          name: e.name,
          useDay: useDay
        });
      })

      //color
      if (dict.isHoliday || !dict.isWorkday) {
        dict.color = '#bfbfbf';
      }

      if (
        dict.leaveQty > 0 ||
        dict.case1 > 0 ||
        dict.case2 > 0 ||
        dict.case3 > 0 ||
        dict.case4 > 0 ||
        (dict.calcHrs > 0 && dict.calcHrs < 480)
      ) {
        dict.color = '#FFDCB2';
      }

      result.push(dict)
    }

    return result;
  }

  async updateEmployeeAttdance(employeeId: number, payload: UpdateAttendanceDto) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const { year, month, attendance, confirmStatus } = payload

      const queryCreate: Attandance[] = []
      const queryUpdate: Attandance[] = []

      let [sumCase1, sumCase2, sumCase3, sumCase4] = [0, 0, 0, 0];

      for (const item of attendance) {
        const attandance = Attandance.create({
          workDate: item.date,
          workStart: item.workStart,
          workEnd: item.workEnd,
          checkIn: item.timeIn,
          checkOut: item.timeOut,
          isWorkday: item.isWorkday,
          isHoliday: item.isHoliday,
          workedHours: item.workHrs,
          overtimeHours: item.otHrs,
          expectedHours: item.calcHrs,
          confirmStatus: confirmStatus,
          deduct: item.deduct,
          employee: Employee.create({ id: employeeId }),
          reason: item.reason,
          explanation: item.explanation,
          case1: item.case1,
          case2: item.case2,
          case3: item.case3,
          case4: item.case4,
        })

        if (item.id == null) {
          queryCreate.push(attandance);
        } else {

          attandance.id = item.id;

          queryUpdate.push(attandance)
        }

        sumCase1 += item.case1
        sumCase2 += item.case2
        sumCase3 += item.case3
        sumCase4 += item.case4
      }

      if (queryCreate.length > 0) {
        await queryRunner.manager.save(queryCreate);
      }

      if (queryUpdate.length > 0) {
        await queryRunner.manager.save(queryUpdate);
      }

      //update history
      const historyRepository = queryRunner.manager.getRepository(HistoryAttandance);

      let history = await historyRepository.findOne({
        where: { year, month, employee: { id: employeeId } },
      });

      if (!history) {
        history = HistoryAttandance.create({
          year,
          month,
          case1: sumCase1,
          case2: sumCase2,
          case3: sumCase3,
          case4: sumCase4,
          employee: { id: employeeId },
        });
      } else {
        history.case1 = sumCase1;
        history.case2 = sumCase2;
        history.case3 = sumCase3;
        history.case4 = sumCase4;
      }

      if (confirmStatus === AttandanceConfirmStatusEnum.CONFIRM) {
        history.confirmStatus = HistoryAttandanceStatusEnum.APPROVE;
        history.headConfirmStatus = HistoryAttandanceStatusEnum.PENDING;
      } else if (confirmStatus === AttandanceConfirmStatusEnum.APPROVE) {
        history.headConfirmStatus = HistoryAttandanceStatusEnum.APPROVE;
      } else if (confirmStatus === AttandanceConfirmStatusEnum.DRAFT) {
        history.confirmStatus = HistoryAttandanceStatusEnum.PENDING;
        history.headConfirmStatus = HistoryAttandanceStatusEnum.PENDING;
      }

      await queryRunner.manager.save(history);

      await queryRunner.commitTransaction();

      return { message: 'ok' }
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async getEmployeeLeaveHistory(employeeId: any) {
    const employee = await Employee.findOneBy({ id: employeeId })

    const result = await Leave.find({
      where: {
        employee: {
          id: employeeId,
        },
        year: employee.totalWorkYear,
      },
      relations: {
        leaveType: true,
        employee: true
      },
      order: {
        leaveType: {
          code: 'DESC'
        }
      }
    });

    return result;
  }

  async forceGenerate() {
    const employees = await Employee.find({
      where: {
        level: {
          levelType: Not(IsNull())
        }
      },
      relations: {
        employeeLeavePermissions: {
          leaveType: true
        }
      }
    })

    return this.forceGenerateEmps(employees)
  }

  async forceGenerateEmps(employees: Employee[]) {
    let total = 0;
    for (const employee of employees) {
      this.logger.verbose(employee.code + ' ' + 'start new periode generate');
      // const employeeLeavePermissions = employee.employeeLeavePermissions.filter(e => e.year == employee.totalWorkYear)

      const leaveQuota = await this.initialEmployeeLeavePermissionByid(employee.id)

      // const newLeavePermissions = leaveQuota.employeeLeavePermissions
      //   .filter(e => !employeeLeavePermissions.map(e => e.leaveType.id).includes(e['leaveTypeId']))

      const newEmployeeLeavePermissions: EmployeeLeavePermissionDto[] = leaveQuota.employeeLeavePermissions.map(e => ({
        id: null,
        leaveTypeId: e.leaveTypeId,
        qtyDay: e.qtyDay,
        usedDay: e.usedDay,
        excessDay: e.excessDay,
        expireDate: leaveQuota.expireDate,
        toNextPeriod: false,
      }));

      const data: CreateEmployeeLeavePermissionDto = {
        startPeriod: leaveQuota.startPeriod,
        endPeriod: leaveQuota.endPeriod,
        expireDate: leaveQuota.expireDate,
        employeeLeavePermissions: newEmployeeLeavePermissions
      }

      await this.updateEmployeeLeavePermission(employee.id, data)

      await Employee.update(employee.id, {
        currentStartPeriod: leaveQuota.startPeriod,
        currentEndPeriod: leaveQuota.endPeriod
      })

      this.logger.verbose(employee.code + ' ' + 'end new periode generate');
      total += 1;
    }

    return { total: total, message: 'ok' }
  }

  async updateEmployeeLeavePermission(
    employeeId: number,
    payload: CreateEmployeeLeavePermissionDto,
  ) {
    const employee = await Employee.findOne({
      where: {
        id: employeeId,
      },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found.');
    }

    const createData = payload.employeeLeavePermissions.filter(
      (e) => e.id == null,
    );
    const updateData = payload.employeeLeavePermissions.filter(
      (e) => e.id != null,
    );

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const queryInsert = [];
      for (const item of createData) {
        //check duplicates
        const check = await EmployeeLeavePermission.findOne({
          where: {
            employee: {
              id: employeeId,
            },
            leaveType: {
              id: item.leaveTypeId,
            },
            // year: employee.totalWorkYear,
            startPeriod: DateTime.fromSQL(payload.startPeriod).toLocal().toJSDate(),
            endPeriod: DateTime.fromSQL(payload.endPeriod).toLocal().toJSDate(),
            // expireDate: DateTime.fromSQL(payload.expireDate).toLocal().toJSDate(),
          },
        });

        if (check) {
          throw new BadRequestException(
            'Employee leave permission already exist.',
          );
        }

        const query = queryRunner.manager.save(EmployeeLeavePermission, {
          employee: { id: employeeId },
          leaveType: { id: item.leaveTypeId },
          startPeriod: new Date(payload.startPeriod),
          endPeriod: new Date(payload.endPeriod),
          expireDate: new Date(item.expireDate),
          qtyDay: item.qtyDay,
          usedDay: 0,
          excessDay: 0,
          year: employee.totalWorkYear,
          toNextPeriod: false,
        });

        queryInsert.push(query);
      }

      const queryUpdate = [];
      for (const item of updateData) {
        //check employee leave permission exists
        const check = await EmployeeLeavePermission.findOne({
          where: {
            id: item.id,
          },
        });
        if (!check) {
          throw new BadRequestException('Employee leave permission not found.');
        }

        const query = queryRunner.manager.update(
          EmployeeLeavePermission,
          item.id,
          {
            qtyDay: item.qtyDay,
            excessDay: item.excessDay,
            usedDay: item.usedDay,
            expireDate: item.expireDate,
            toNextPeriod: item?.toNextPeriod ?? false
          },
        );

        queryUpdate.push(query);
      }

      await Promise.all(queryInsert);
      await Promise.all(queryUpdate);

      await queryRunner.manager.update(Employee, employee.id, {
        currentStartPeriod: DateTime.fromSQL(payload.startPeriod).toLocal().toFormat('yyyy-MM-dd'),
        currentEndPeriod: DateTime.fromSQL(payload.endPeriod).toLocal().toFormat('yyyy-MM-dd'),
      })

      await queryRunner.commitTransaction();

      return { message: 'Update employee leave permission success.' };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message);
      throw new BadRequestException(err?.message);
    } finally {
      await queryRunner.release();
    }
  }

  async initialEmployeeLeavePermissionByid(employeeId: any) {
    //check employee exists
    const employee = await Employee.findOne({
      // select: ['id', 'code', 'firstname', 'lastname', 'registerDate', 'sex', 'level'],
      where: {
        id: employeeId,
      },
      relations: {
        level: {
          levelType: true
        }
      }
    });

    if (!employee) {
      throw new NotFoundException('Employee not found.');
    }

    const leavePermissions = await LeavePermission.find({
      where: {
        ageWork: LessThanOrEqual(employee.totalWorkYear),
        leaveType: {
          sex: In(['all', employee.sex]),
        },
        levelType: {
          id: employee.level.levelType.id
        }
      },
      relations: {
        leaveType: true,
        levelType: true,
      },
      order: {
        leaveType: {
          code: 'ASC',
        },
        ageWork: 'ASC',
      },
    });

    const groupLeavePermissions = groupBy(
      leavePermissions,
      (lp) => `${lp.leaveType.id}-${lp.levelType.id}`,
    );

    for (const key of Object.keys(groupLeavePermissions)) {
      if (groupLeavePermissions[key].length > 1) {
        groupLeavePermissions[key] = [
          groupLeavePermissions[key].reverse().shift(),
        ];
      }
    }

    const flattenedLeavePermissions = Object.values(
      groupLeavePermissions,
    ).flat();

    const registerDate = new Date(employee.registerDate);

    // Use Luxon to construct a DateTime for better readability and correct method usage
    let period = employee?.currentEndPeriod
      ? DateTime.fromSQL(employee.currentEndPeriod.toString())
      : DateTime.fromObject({
        month: registerDate.getMonth() + 1, // Adjust for zero-based month
        day: registerDate.getDate()         // Use getDate() for day of the month
      });

    const now = DateTime.local();

    if (period > now) {
      period = period.minus({ year: 1 })
    }

    const { startPeriod, endPeriod } = calculateNextPeriod(new Date(period.toJSDate()));

    // ค้นหาวันลาเดิมที่ถูกเลือก toNextPeriod = true
    const employeeLeavePermissions = await EmployeeLeavePermission.find({
      where: {
        employee: {
          id: employeeId,
        },
        toNextPeriod: true,
      },
      order: {
        startPeriod: 'DESC',
      },
      take: 1,
      relations: {
        leaveType: true,
      },
    });

    const newEmployeeLeavePermissions = [];
    for (const leavePermission of flattenedLeavePermissions) {
      if (employeeLeavePermissions.length > 0) {
        const _employeeLeavePermission = employeeLeavePermissions.find(
          (elp) => elp.leaveType.id === leavePermission.leaveType.id,
        );

        if (_employeeLeavePermission) {
          const employeeLeavePermission = {
            qtyDay: _employeeLeavePermission.qtyDay,
            usedDay: 0,
            excessDay: 0,
            leaveTypeId: leavePermission.leaveType.id,
            leaveTypeName: leavePermission.leaveType.name,
            leaveTypecode: leavePermission.leaveType.code,
            expireDate: DateTime.fromJSDate(endPeriod).toLocal().toFormat('yyyy-MM-dd'),
          };

          newEmployeeLeavePermissions.push(employeeLeavePermission);
        }
      }

      const employeeLeavePermission = {
        qtyDay: leavePermission.qtyDay,
        usedDay: 0,
        excessDay: 0,
        leaveTypeId: leavePermission.leaveType.id,
        leaveTypeName: leavePermission.leaveType.name,
        leaveTypecode: leavePermission.leaveType.code,
        expireDate: DateTime.fromJSDate(endPeriod).toLocal().toFormat('yyyy-MM-dd'),
      };

      //ถ้ามี leaveTypeId อยู๋แล้วจะข้ามไป
      if (newEmployeeLeavePermissions.find((elp) => elp.leaveTypeId === leavePermission.leaveType.id)) {
        continue;
      }

      newEmployeeLeavePermissions.push(employeeLeavePermission);
    }

    // await Employee.update(employee.id, {
    //   currentStartPeriod: DateTime.fromJSDate(startPeriod).toLocal().toFormat('yyyy-MM-dd'),
    //   currentEndPeriod: DateTime.fromJSDate(endPeriod).toLocal().toFormat('yyyy-MM-dd'),
    // })

    return {
      employee,
      startPeriod: DateTime.fromJSDate(startPeriod).toLocal().toFormat('yyyy-MM-dd'),
      endPeriod: DateTime.fromJSDate(endPeriod).toLocal().toFormat('yyyy-MM-dd'),
      expireDate: DateTime.fromJSDate(endPeriod).toLocal().toFormat('yyyy-MM-dd'),
      employeeLeavePermissions: newEmployeeLeavePermissions,
    };
  }

  async autoGenerateLeave() {
    const yesterday = DateTime.local().minus({ days: 1 }).toFormat('yyyy-MM-dd');

    const employees = await this.dataSource
      .createQueryBuilder(Employee, 'employee')
      .leftJoinAndSelect("employee.employeeLeavePermissions", "employeeLeavePermissions")
      .leftJoinAndSelect("employeeLeavePermissions.leaveType", "leaveType")
      .where('(employee.current_end_period <= :yesterday OR employee.current_end_period IS NULL)', { yesterday })
      // .andWhere("employee.active <> :active", { active: EmployeeActiveEnum.RES })
      .getMany();

    const result = await this.forceGenerateEmps(employees)

    return result;
  }

  async forceGenerateLeave(date: string) {
    const yesterday = DateTime.fromSQL(date).toLocal().minus({ days: 1 }).toFormat('yyyy-MM-dd');

    const employees = await this.dataSource
      .createQueryBuilder(Employee, 'employee')
      // .leftJoinAndSelect("employee.employeeLeavePermissions", "employeeLeavePermissions")
      // .leftJoinAndSelect("employeeLeavePermissions.leaveType", "leaveType")
      .where('(employee.current_end_period <= :yesterday OR employee.current_end_period IS NULL)', { yesterday })
      // .andWhere("employee.active <> :active", { active: EmployeeActiveEnum.RES })
      .getMany();

    const result = await this.forceGenerateEmps(employees)

    return result;
  }

  async getEmployeeLeavePermission(userId: any) {
    const employee = await Employee.findOneBy({ id: userId });

    const result = await EmployeeLeavePermission.find({
      where: {
        employee: {
          id: userId,
        },
        startPeriod: employee.currentStartPeriod,
        endPeriod: employee.currentEndPeriod
      },
      relations: {
        leaveType: true,
      },
      order: {
        leaveType: {
          code: 'ASC'
        }
      }
    });

    return result;
  }

  async autoCode(t: string) {
    const prefix = t

    const lastEmployee = await Employee.find({
      where: {
        code: Like(prefix + '%')
      },
      order: {
        code: 'DESC'
      },
      take: 1
    });

    if (lastEmployee.length == 0) {
      return {
        data: prefix + '0001'
      }
    } else {

      const num = Number.parseInt(lastEmployee[0].code.replace(prefix, ''))
      const newNumberString = prefix + (num + 1).toString().padStart(4, '0')

      return {
        data: newNumberString
      };
    }
  }

  async getWorkShiftEmployee(employeeId: number, date: string) {

    let is_holiday = false
    let workShift = null

    //employee in dept
    const employee = await Employee.findOne({
      relations: ['workShift'],
      where: {
        id: employeeId,
      },
    });

    if (employee) {
      if (employee.workShift) {

        const dateOfWeek: string = String(Helper.getDayOfWeek(date));
        const work_shift_id = employee.workShift?.id

        const workShiftTime = await WorkShiftTime.findOne({
          relations: ['workShift'],
          where: {
            day: dateOfWeek,
            workShift: {
              id: work_shift_id,
            }
          },
        });

        if (workShiftTime) {
          workShift = workShiftTime

          //check holiday
          const Date = DateTime.fromSQL(date).toLocal();  // ตัวอย่างวันที่เริ่มต้น

          // ค้นหาข้อมูล holiday โดยตรง
          const holidays = await Holiday.findOne({
            where: {
              date: Date.toJSDate(),
              employeeType: {
                id: employee.employeeType?.id,
              }
            },
          });

          //
          if (holidays || workShiftTime.active == false) {
            is_holiday = true
          }
        }

      }

    }

    let data = {
      is_holiday: is_holiday,
      workShift: workShift
    }

    return data

  }

  // async getEmployeeAttendanceHistoryCheck(year: string, month: string) {

  //   let dateStart = null
  //   let dateEnd = null

  //   if (!year) {
  //     throw new BadRequestException("please input year")
  //   }

  //   if (!month) {
  //     throw new BadRequestException("please input month")
  //   }

  //   if (year && !month) {
  //     dateStart = `${year}-01-01`;
  //     dateEnd = `${year}-12-31`;

  //   } else if (year && month) {
  //     const paddedMonth = month.padStart(2, '0');

  //     dateStart = `${year}-${paddedMonth}-01`;

  //     const endDate = new Date(Number(year), Number(month), 0);
  //     dateEnd = `${year}-${paddedMonth}-${endDate
  //       .getDate()
  //       .toString()
  //       .padStart(2, '0')}`;
  //   }
  //   //employee
  //   const employee = await Employee.find({
  //     relations: [],
  //     // where: {
  //     //   department: {
  //     //     id: dep.id,
  //     //   },
  //     // },
  //   });

  //   for (const em of employee) {

  //     let create_history_status = false
  //     //check create history
  //     const historyAttandance = await HistoryAttandance.find({
  //       relations: ['employee'],
  //       where: {
  //         employee: {
  //           id: em.id
  //         },
  //         year: parseInt(year, 10),
  //         month: parseInt(month, 10),
  //       }
  //     });

  //     if (historyAttandance) {
  //       create_history_status = true
  //     }
  //     //

  //     //attendance
  //     let whereConditions: any = {
  //       employee: {
  //         id: em.id
  //       }
  //     };

  //     if (dateStart && dateEnd) {

  //       const dateStartFormat = Helper.formatDate(`${dateStart}`);
  //       const dateEndFormat = Helper.formatDate(`${dateEnd}`);

  //       whereConditions.workDate = Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart: dateStartFormat, dateEnd: dateEndFormat });
  //     }

  //     const result = await Attandance.find({
  //       relations: ['employee'],
  //       where: whereConditions
  //     });

  //     // case1
  //     const case1 = result.filter(row => row.case1 > 0);
  //     const sumCase1 = case1.reduce((sum, row) => sum + row.case1, 0);

  //     // case2
  //     const case2 = result.filter(row => row.case2 > 0);
  //     const sumCase2 = case2.reduce((sum, row) => sum + row.case2, 0);

  //     // case3
  //     const case3 = result.filter(row => row.case3 > 0);
  //     const sumCase3 = case3.reduce((sum, row) => sum + row.case3, 0);

  //     // case4
  //     const case4 = result.filter(row => row.case4 > 0);
  //     const sumCase4 = case4.reduce((sum, row) => sum + row.case4, 0);

  //     //total deduct
  //     //case 1 = Late > 30 Mins , case 2 = Late > 60 Mins
  //     //case 3 = Late > 90 Mins , case 4 = Late > 150 Mins

  //     const total_deduct_min = (sumCase1 * 30) + (sumCase2 * 60) + (sumCase3 * 90) + (sumCase4 * 150)

  //     em.total_case1 = sumCase1
  //     em.total_case2 = sumCase2
  //     em.total_case3 = sumCase3
  //     em.total_case4 = sumCase4
  //     em.total_deduct_min = total_deduct_min

  //     em.create_history_status = create_history_status
  //   }
  // }

  async getEmployeeAttendanceHistoryCheckDatatables(query: PaginateQuery, year: string, month: string): Promise<Paginated<Employee>> {

    if (!year) {
      throw new BadRequestException('Please input year');
    }

    if (!month) {
      throw new BadRequestException('Please input month');
    }

    // if (!headId) {
    //   throw new BadRequestException('Please input headId');
    // }


    // กำหนดช่วงวันที่
    let dateStart = `${year}-${month.padStart(2, '0')}-01`;
    let dateEnd = `${year}-${month.padStart(2, '0')}-${new Date(Number(year), Number(month), 0).getDate()}`;

    if (!month) {
      dateStart = `${year}-01-01`;
      dateEnd = `${year}-12-31`;
    }

    // Query ข้อมูล employee พร้อม paginate
    const employees = await paginate(query, this.employeeRepository, EMPLOYEE_PAGINATION_CONFIG);

    for (const em of employees.data) {
      let create_history_status = false;
      let history_attandane = null;

      let whereEmployeeConditions: any = {
        employee: {
          id: em.id
        },
        year: year,
        month: month,
      };

      // ตรวจสอบประวัติการสร้าง Attendance
      const historyAttandance = await HistoryAttandance.findOne({
        relations: ['employee'],
        where: whereEmployeeConditions
      });

      if (historyAttandance) {
        create_history_status = true;
        history_attandane = historyAttandance
      }

      // Attendance query
      const whereConditions: any = {
        employee: {
          id: em.id,
        },
        workDate: Raw(
          (alias) => `${alias} BETWEEN :dateStart AND :dateEnd`,
          { dateStart, dateEnd },
        ),
      };

      const result = await Attandance.find({
        relations: ['employee'],
        where: whereConditions,
      });

      // สรุป case ทั้งหมด
      const sumCase1 = result.reduce((sum, row) => sum + (row.case1 || 0), 0);
      const sumCase2 = result.reduce((sum, row) => sum + (row.case2 || 0), 0);
      const sumCase3 = result.reduce((sum, row) => sum + (row.case3 || 0), 0);
      const sumCase4 = result.reduce((sum, row) => sum + (row.case4 || 0), 0);

      // คำนวณการหัก
      const total_deduct_min =
        sumCase1 * 30 + sumCase2 * 60 + sumCase3 * 90 + sumCase4 * 150;

      // เพิ่มข้อมูลเพิ่มเติมให้กับ employee
      em['total_case1'] = sumCase1;
      em['total_case2'] = sumCase2;
      em['total_case3'] = sumCase3;
      em['total_case4'] = sumCase4;
      em['total_deduct_min'] = total_deduct_min;
      em['create_history_status'] = create_history_status;
      em['create_history_attendance_detail'] = history_attandane;
    }
    return employees;
  }
}
