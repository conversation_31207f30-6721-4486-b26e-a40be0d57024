import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { UpdateEmpAttendanceDto } from "src/attandances/dto/update-attendance.dto";
import { AttandanceConfirmStatusEnum } from "src/attandances/entities/attandance.entity";

export class UpdateAttendanceDto {
  @IsNotEmpty()
  @ApiProperty({ example: 2024})
  year: number;

  @IsNotEmpty()
  @Min(1)
  @Max(12)
  month: number;

  @IsNotEmpty()
  readonly attendance: UpdateEmpAttendanceDto[]

  @IsNotEmpty()
  confirmStatus: AttandanceConfirmStatusEnum
}

// export class UpdateAttendanceItemDto {
//   @IsNotEmpty()
//   readonly id: number;

//   readonly reason: string | null;

//   readonly explanation: string | null;

//   readonly deduct: boolean;
// }