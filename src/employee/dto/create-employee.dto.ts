import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { EmployeeActiveEnum, EmployeeStatusEnum, GenderEnum } from "../entities/employee.entity";
export class CreateEmployeeDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly username: string;

    // @IsNotEmpty()
    // @ApiProperty()
    // readonly password: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly firstname: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly lastname: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly sex: GenderEnum;

    @ApiProperty()
    readonly birthDate: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly registerDate: string;

    @ApiProperty()
    readonly passProbationDate: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly titleId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly levelId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly departmentId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly employeeTypeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly workShiftId: number;

    @ApiProperty()
    readonly headId: number;

    @IsNotEmpty()
    @ApiProperty({ example: false })
    readonly isApprover: boolean;

    @IsNotEmpty()
    readonly employeeStatus: EmployeeStatusEnum;

    @IsNotEmpty()
    @ApiProperty({description: 'PER = Permanent, FTC = Fixed-term Contract, RES = Resigned'})
    readonly active: EmployeeActiveEnum
}
