import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const CATEGORY_PAGINATION_CONFIG: PaginateConfig<Category> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Category>> {
    return paginate(query, this.categoryRepository, CATEGORY_PAGINATION_CONFIG);
  }

  async create(createCategoryDto: CreateCategoryDto) {

    const { ...data } = createCategoryDto;

    //check category code exist
    const check = await Category.existsBy({
      code: createCategoryDto?.code
    })
    if (check) {
      throw new BadRequestException('category code already.')
    }

    const item = this.categoryRepository.create(
      {
        ...data,
      });

    return this.categoryRepository.save(item);
  }

  findAll() {
    return this.categoryRepository.find();
  }

  async findOne(id: number) {
    const item = await this.categoryRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("category not found");

    return item;
  }

  async update(id: number, updateCategoryDto: UpdateCategoryDto) {
    //check category code exist
    const check = await Category.existsBy({
      code: updateCategoryDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Category code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("category not found");

    const { ...data } = updateCategoryDto;

    return this.categoryRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("category not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.categoryRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.categoryRepository.findOne({ where: { id } });
  }
}




