import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { CompanyCategory } from "src/company_category/entities/company_category.entity";
import { Contact } from "src/contact/entities/contact.entity";
import { ContactCategory } from "src/company_category/entities/contact_category.entity";

@Entity()
@Unique(['code'])
export class Category extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;


    @Column({ default: true })
    active: boolean

    //CompanyCategory
    @OneToMany(() => CompanyCategory, (_) => _.category)
    companyCategories: Array<CompanyCategory>;

    @OneToMany(() => ContactCategory, (_) => _.category)
    contactCategories: ContactCategory[];
}






