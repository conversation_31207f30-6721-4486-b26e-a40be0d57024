import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';

import { LEAVE_TYPE_PAGINATION_CONFIG, LeaveTypeService } from './leave-type.service';
import { CreateLeaveTypeDto } from './dto/create-leave-type.dto';
import { UpdateLeaveTypeDto } from './dto/update-leave-type.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { GenerateLeaveTypeDto } from './dto/generate-leave-type.dto';

@Controller('leave-type')
@ApiTags('ประเภทการลา')
// @Auth()
export class LeaveTypeController {
  constructor(private readonly leaveTypeService: LeaveTypeService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(LEAVE_TYPE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.leaveTypeService.datatables(query);
  }

  @Post('/append-to-employee')
  @Auth()
  appendToEmployee(@Body() payload: GenerateLeaveTypeDto) {
    return this.leaveTypeService.appendToEmployee(payload.leaveTypeId);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createLeaveTypeDto: CreateLeaveTypeDto) {
    return this.leaveTypeService.create(createLeaveTypeDto);
  }

  @Get()
  findAll() {
    return this.leaveTypeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.leaveTypeService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateLeaveTypeDto: UpdateLeaveTypeDto) {
    return this.leaveTypeService.update(+id, updateLeaveTypeDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.leaveTypeService.remove(+id);
  }
}

