import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateLeaveTypeDto } from './dto/create-leave-type.dto';
import { UpdateLeaveTypeDto } from './dto/update-leave-type.dto';
import { LeaveType } from './entities/leave-type.entity';
import { LessThanOrEqual, MoreThanOrEqual, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Leave } from 'src/leave/entities/leave.entity';
import { Employee, EmployeeActiveEnum } from 'src/employee/entities/employee.entity';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';

export const LEAVE_TYPE_PAGINATION_CONFIG: PaginateConfig<LeaveType> = {
  sortableColumns: ['id', 'code', 'name', 'sex', 'show', 'active'],
  select: ['id', 'code', 'name', 'sex', 'active', 'show', 'createdAt'],
  searchableColumns: ['code', 'name', 'sex']
};
@Injectable()
export class LeaveTypeService {
  private readonly logger = new Logger(LeaveTypeService.name);

  constructor(
    @InjectRepository(LeaveType)
    private leaveTypeRepository: Repository<LeaveType>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<LeaveType>> {
    return paginate(query, this.leaveTypeRepository, LEAVE_TYPE_PAGINATION_CONFIG);
  }

  async create(createLeaveTypeDto: CreateLeaveTypeDto) {

    const { ...data } = createLeaveTypeDto;

    //check leave type code exist
    const check = await LeaveType.existsBy({
      code: createLeaveTypeDto?.code
    })
    if (check) {
      throw new BadRequestException('leave type code already.')
    }

    const item = this.leaveTypeRepository.create(
      {
        ...data,
      });

    return this.leaveTypeRepository.save(item);
  }

  findAll() {
    return this.leaveTypeRepository.find();
  }

  async findOne(id: number) {
    const item = await this.leaveTypeRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("leave type not found");

    return item;
  }

  async update(id: number, updateLeaveTypeDto: UpdateLeaveTypeDto) {
    //check leave type code exist
    const check = await LeaveType.existsBy({
      code: updateLeaveTypeDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Leave type code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("leave type not found");

    const { ...data } = updateLeaveTypeDto;

    return this.leaveTypeRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("leave type not found");

    // if leave used
    const leaveQuery = Leave.countBy({ leaveType: { id: id } })
    const leavePmsQuery = Leave.countBy({ leaveType: { id: id } })
    const empLeavPermisQuery = Leave.countBy({ leaveType: { id: id } })

    const [leave, leavePms, empLeavPermis] = await Promise.all([
      leaveQuery, leavePmsQuery, empLeavPermisQuery
    ])

    if (leave || leavePms || empLeavPermis) {
      throw new BadRequestException('Can\'t delete a leave')
    }

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.leaveTypeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.leaveTypeRepository.findOne({ where: { id } });
  }

  async appendToEmployee(leaveTypeId: number) {
    const leaveType = await LeaveType.findOne({
      where: {
        id: leaveTypeId
      }
    });

    if (!leaveType) {
      throw new NotFoundException('Leave Type not found.')
    }

    const employees = await Employee.find({
      where: {
        active: Not(EmployeeActiveEnum.RES)
      },
      relations: {
        level: {
          levelType: true
        }
      }
    });

    const employeeLeavePermissions = []
    for (const employee of employees) {
      //get qty day of employee
      const leavePermission = await LeavePermission.findOne({
        where: {
          ageWork: LessThanOrEqual(employee.totalWorkYear),
          leaveType: { id: leaveType.id },
          levelType: { id: employee.level.levelType.id }
        }
      });

      if (!leavePermission) {
        this.logger.warn('Employee code ' + employee.code + ' can\'t get leave permission');
        continue;
      }

      const employeeLeavePermission = EmployeeLeavePermission.create({
        employee: { id: employee.id },
        qtyDay: leavePermission.qtyDay,
        usedDay: 0,
        startPeriod: employee.currentStartPeriod,
        endPeriod: employee.currentEndPeriod,
        excessDay: 0,
        leaveType: { id: leaveType.id },
        year: employee.totalWorkYear,
        expireDate: employee.currentEndPeriod
      })

      employeeLeavePermissions.push(employeeLeavePermission)
    }

    if (employeeLeavePermissions.length > 0) {
      await EmployeeLeavePermission.save(employeeLeavePermissions);
    }

    return { status: true, message: 'OK' }
  }
}

