import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "../../employee/entities/employee.entity";
import { Leave } from "../../leave/entities/leave.entity";
import { LeavePermission } from "../../leave-permission/entities/leave-permission.entity";
import { EmployeeLeavePermission } from "../../employee-leave-permission/entities/employee-leave-permission.entity";


@Entity()
@Unique(['code'])
export class LeaveType extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({
        type: "enum",
        enum: ["all", "male", "female"],
        default: 'all'
    })
    sex: string

    @Column({ default: true })
    active: boolean

    @Column('boolean', { default: false })
    proRate: boolean;

    @Column('boolean', { default: false })
    ignoreDayOff: boolean;
    
    @Column('boolean', { default: true })
    show: boolean;

    @Column('int', { nullable: true })
    minDayAttachFile: number;

    //LeaveType
    @OneToMany(() => Leave, (_) => _.leaveType)
    leaves: Array<Leave>;

    //LeavePermission
    @OneToMany(() => LeavePermission, (_) => _.leaveType)
    leavePermissions: Array<LeavePermission>;

    @OneToMany(() => EmployeeLeavePermission, (_) => _.leaveType)
    employeeLeavePermissions: Array<EmployeeLeavePermission>;

    @Column('boolean', { default: false })
    toNextPeriod: boolean;
}


