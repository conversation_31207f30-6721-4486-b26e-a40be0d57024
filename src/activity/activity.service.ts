import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { Activity } from './entities/activity.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const ACTIVITY_PAGINATION_CONFIG: PaginateConfig<Activity> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class ActivityService {
  constructor(
    @InjectRepository(Activity)
    private activityRepository: Repository<Activity>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Activity>> {
    return paginate(query, this.activityRepository, ACTIVITY_PAGINATION_CONFIG);
  }

  async create(createActivityDto: CreateActivityDto) {

    const { ...data } = createActivityDto;

    //check activity code exist
    const check = await Activity.existsBy({
      code: createActivityDto?.code
    })
    if (check) {
      throw new BadRequestException('activity code already.')
    }

    const item = this.activityRepository.create(
      {
        ...data,
      });

    return this.activityRepository.save(item);
  }

  findAll() {
    return this.activityRepository.find();
  }

  async findOne(id: number) {
    const item = await this.activityRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("activity not found");

    return item;
  }

  async update(id: number, updateActivityDto: UpdateActivityDto) {
    //check activity code exist
    const check = await Activity.existsBy({
      code: updateActivityDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Activity code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("activity not found");

    const { ...data } = updateActivityDto;

    return this.activityRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("activity not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.activityRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.activityRepository.findOne({ where: { id } });
  }
}





