import { Column, Entity, Index, ManyToMany, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { CompanyCategory } from "src/company_category/entities/company_category.entity";
import { ContactActivity } from "src/contact_activity/entities/contact_activity.entity";

@Entity()
@Unique(['code'])
export class Activity extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;


    @Column({ default: true })
    active: boolean

    //ContactActivity
    @ManyToMany(() => ContactActivity, (_) => _.activities)
    contactActivities: ContactActivity[];


}







