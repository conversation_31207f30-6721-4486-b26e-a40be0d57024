import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';

import { ACTIVITY_PAGINATION_CONFIG } from './activity.service';
import { ActivityService } from './activity.service';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';


@Controller('activity')
@ApiTags('กิจกรรม')
export class ActivityController {
  constructor(private readonly activityService: ActivityService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(ACTIVITY_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.activityService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createActivityDto: CreateActivityDto) {
    return this.activityService.create(createActivityDto);
  }

  @Get()
  findAll() {
    return this.activityService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.activityService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateActivityDto: UpdateActivityDto) {
    return this.activityService.update(+id, updateActivityDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.activityService.remove(+id);
  }
}





