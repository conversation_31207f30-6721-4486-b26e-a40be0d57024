import { Modu<PERSON> } from '@nestjs/common';
import { LeavePermissionService } from './leave-permission.service';
import { LeavePermissionController } from './leave-permission.controller';
import { LeavePermission } from './entities/leave-permission.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Level } from 'src/level/entities/level.entity';
import { LevelType } from 'src/level-type/entities/level-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LeavePermission, LeaveType, Level, LevelType])],
  controllers: [LeavePermissionController],
  providers: [LeavePermissionService],
})
export class LeavePermissionModule { }
