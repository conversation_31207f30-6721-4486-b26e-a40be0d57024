import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { LevelType } from "../../level-type/entities/level-type.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";

@Entity()

export class LeavePermission extends CustomBaseEntity {

    @Column({ default: 0 })
    ageWork: number

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyDay: number;

    @Column({ default: true })
    active: boolean

    //LevelType
    @ManyToOne(() => LevelType, (_) => _.leavePermissions)
    @JoinColumn({ name: 'level_type_id' })
    levelType: LevelType;

    //leaveType
    @ManyToOne(() => LeaveType, (_) => _.leaves)
    @JoinColumn({ name: 'leave_type_id' })
    leaveType: LeaveType;
}



