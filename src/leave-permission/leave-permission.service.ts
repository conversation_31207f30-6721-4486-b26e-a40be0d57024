import { Injectable } from '@nestjs/common';
import { CreateLeavePermissionDto } from './dto/create-leave-permission.dto';
import { UpdateLeavePermissionDto } from './dto/update-leave-permission.dto';
import * as xlsx from 'xlsx'
import { Employee } from 'src/employee/entities/employee.entity';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';

@Injectable()
export class LeavePermissionService {
  create(createLeavePermissionDto: CreateLeavePermissionDto) {
    return 'This action adds a new leavePermission';
  }

  findAll() {
    return `This action returns all leavePermission`;
  }

  findOne(id: number) {
    return `This action returns a #${id} leavePermission`;
  }

  update(id: number, updateLeavePermissionDto: UpdateLeavePermissionDto) {
    return `This action updates a #${id} leavePermission`;
  }

  remove(id: number) {
    return `This action removes a #${id} leavePermission`;
  }

  async import(file: Express.Multer.File) {
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1 });

    for (const data of jsonData) {
      const employee = await Employee.findOne({
         where: {
          code: data[0]
         }
      })

      if (!employee) {
        continue;
      }

      const employeeLeavePermission = await EmployeeLeavePermission.findOne({
        where: {
          leaveType: {
            name: data[3]
          },
          employee: {
            id: employee.id
          }
        },
        order: {
          'createdAt': 'DESC'
        }
      })

      if (employeeLeavePermission) {
        await EmployeeLeavePermission.update(employeeLeavePermission.id, {
          qtyDay: data[5],
          usedDay: data[6],
          excessDay: data[8]
        })
      }

      console.log(employee.code, data[3], data[4], data[6], data[8]);
    }

    return { message: 'ok'}
  }
}
