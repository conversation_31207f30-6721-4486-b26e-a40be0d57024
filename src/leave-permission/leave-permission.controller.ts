import { Controller, Get, Post, Body, Patch, Param, Delete, BadRequestException, HttpStatus, ParseFilePipeBuilder, UploadedFile, UseInterceptors } from '@nestjs/common';
import { LeavePermissionService } from './leave-permission.service';
import { CreateLeavePermissionDto } from './dto/create-leave-permission.dto';
import { UpdateLeavePermissionDto } from './dto/update-leave-permission.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('leave-permission')
@ApiTags('Master การลา')
export class LeavePermissionController {
  constructor(private readonly leavePermissionService: LeavePermissionService) {}

  @Post('import-excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async importEmployee(@UploadedFile(
    new ParseFilePipeBuilder()
      .addFileTypeValidator({ fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }) // Adjusted regex to accept .txt as well
      .addMaxSizeValidator({ maxSize: 1000 * 1000 * 10 }) // 10 MB
      .build({
        errorHttpStatusCode: HttpStatus.BAD_REQUEST

      })
  ) file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.leavePermissionService.import(file);
  }

  // @Post()
  // create(@Body() createLeavePermissionDto: CreateLeavePermissionDto) {
  //   return this.leavePermissionService.create(createLeavePermissionDto);
  // }

  // @Get()
  // findAll() {
  //   return this.leavePermissionService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.leavePermissionService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateLeavePermissionDto: UpdateLeavePermissionDto) {
  //   return this.leavePermissionService.update(+id, updateLeavePermissionDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.leavePermissionService.remove(+id);
  // }
}
