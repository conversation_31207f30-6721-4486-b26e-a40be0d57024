import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantWorkController } from './applicant_work.controller';
import { ApplicantWorkService } from './applicant_work.service';

describe('ApplicantWorkController', () => {
  let controller: ApplicantWorkController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantWorkController],
      providers: [ApplicantWorkService],
    }).compile();

    controller = module.get<ApplicantWorkController>(ApplicantWorkController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
