import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantWorkService } from './applicant_work.service';

describe('ApplicantWorkService', () => {
  let service: ApplicantWorkService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicantWorkService],
    }).compile();

    service = module.get<ApplicantWorkService>(ApplicantWorkService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
