import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantWorkService } from './applicant_work.service';
import { CreateApplicantWorkDto } from './dto/create-applicant_work.dto';
import { UpdateApplicantWorkDto } from './dto/update-applicant_work.dto';

@Controller('applicant-work')
export class ApplicantWorkController {
  constructor(private readonly applicantWorkService: ApplicantWorkService) {}

  @Post()
  create(@Body() createApplicantWorkDto: CreateApplicantWorkDto) {
    return this.applicantWorkService.create(createApplicantWorkDto);
  }

  @Get()
  findAll() {
    return this.applicantWorkService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantWorkService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantWorkDto: UpdateApplicantWorkDto) {
    return this.applicantWorkService.update(+id, updateApplicantWorkDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantWorkService.remove(+id);
  }
}
