import { Injectable } from '@nestjs/common';
import { CreateApplicantWorkDto } from './dto/create-applicant_work.dto';
import { UpdateApplicantWorkDto } from './dto/update-applicant_work.dto';

@Injectable()
export class ApplicantWorkService {
  create(createApplicantWorkDto: CreateApplicantWorkDto) {
    return 'This action adds a new applicantWork';
  }

  findAll() {
    return `This action returns all applicantWork`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantWork`;
  }

  update(id: number, updateApplicantWorkDto: UpdateApplicantWorkDto) {
    return `This action updates a #${id} applicantWork`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantWork`;
  }
}
