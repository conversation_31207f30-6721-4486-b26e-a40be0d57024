
import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Applicant } from "src/applicant/entities/applicant.entity";

@Entity()
export class ApplicantWork extends CustomBaseEntity {

    @Column("text", { nullable: true })
    name: string;

    @Column("text", { nullable: true })
    position: string;

    @Column("text", { nullable: true })
    from: string;

    @Column("text", { nullable: true })
    to: string;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    salary: number;

    @Column("text", { nullable: true })
    reasonLeaving: string;


    @Column({ default: true })
    active: boolean

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantWorks)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;

}



