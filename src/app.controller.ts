import { Body, Controller, Get, Post } from '@nestjs/common';
import { AppService } from './app.service';
import { CheckInDto } from './check-in.dto';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('/check-in')
  checkIn(@Body() payload: CheckInDto) {
    return this.appService.checkIn(payload);
  }
}
