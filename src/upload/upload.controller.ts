import { Controller, Delete, HttpStatus, Param, ParseFilePipeBuilder, Post, UploadedFile, UploadedFiles, UseInterceptors } from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { readFileSync } from 'fs-extra';
import { Helper } from 'src/common/utils/helper';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthRole } from 'src/auth/auth.interface';

@Controller('upload')
@ApiTags('upload')
// @Auth()
// @UseInterceptors(ClassSerializerInterceptor)
export class UploadController {
  constructor(
    private uploadService: UploadService,
  ) { }

  // @Get(':id')
  // @ApiOperation({ summary: 'เรียกไฟล์' })
  // async get(@Param('id') id: string): Promise<StreamableFile> {
  //   const data = await this.uploadService.findOne(id)

  //   const file = createReadStream(join(process.cwd(), data.path));
  //   return new StreamableFile(file, {
  //     type: data.mimetype,
  //     disposition: `filename="${data.filename}"`
  //   });
  // }

  @Post('file')
  @Roles(AuthRole.Admin)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async uploadFile(@UploadedFile(
    new ParseFilePipeBuilder()
      .addFileTypeValidator({ fileType: "image/jpeg|image/png|application/pdf|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }) // Adjusted regex to accept .txt as well
      .addMaxSizeValidator({ maxSize: 1000 * 1000 * 10 }) // 10 MB
      .build({
        errorHttpStatusCode: HttpStatus.BAD_REQUEST

      })) file: Express.Multer.File) {
    const buffer: any = readFileSync(file.path)

    const checksum = Helper.generateChecksum(buffer, 'sha256', 'hex');

    file['sha256Checksum'] = checksum

    await this.uploadService.createOne(file);

    const _file: any = file;
    _file.pathUrl = _file.provider == 'local'
      ? process.env.APP_URL + '/' + _file.path
      : _file.filename;

    return file;
  }

  @Post('files')
  @Roles(AuthRole.Admin)
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          }
        },
      },
    },
  })
  async uploadFiles(@UploadedFiles(
    new ParseFilePipeBuilder()
      .addFileTypeValidator({ fileType: "image/jpeg|image/png|application/pdf|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }) // Adjusted regex to accept .txt as well
      .addMaxSizeValidator({ maxSize: 1000 * 1000 * 10 }) // 10 MB
      .build({
        errorHttpStatusCode: HttpStatus.BAD_REQUEST

      })) files: Array<Express.Multer.File>) {
    for (const file of files) {
      const buffer: any = readFileSync(file.path)

      const checksum = Helper.generateChecksum(buffer, 'sha256', 'hex');

      file['sha256Checksum'] = checksum
    }

    await this.uploadService.createMany(files);

    for (const file of files as any) {
      file.pathUrl = file.provider == 'local'
        ? process.env.APP_URL + '/' + file.filename
        : file.filename;
    }

    return files;
  }

  @Roles(AuthRole.Admin)
  @Delete(':uuid')
  remove(@Param('uuid') uuid: string) {
    return this.uploadService.remove(uuid);
  }
}