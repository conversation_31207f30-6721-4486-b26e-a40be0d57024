import { DynamicModule, Module } from '@nestjs/common';
import { UploadController } from './upload.controller';
import { UploadService } from './upload.service';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { mkdirSync } from 'fs-extra';
import * as moment from 'moment';
import * as crypto from 'crypto';
import { generate } from 'randomstring';
import { extname } from 'path';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Upload } from './entities/upload.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Upload
    ]),
    MulterModule.register({
      storage: diskStorage({
        destination: (req, file, cb) => {
          const nowFormat = moment().format('YYYY/MM/DD');
          const directory = nowFormat;

          const path = './uploads/' + directory

          mkdirSync(path, { recursive: true });

          cb(null, path)
        },
        filename: (req, file, cb) => {
          // const extension = extname(file.originalname);
          const fileName = generate() + '-' + Buffer.from(file.originalname, 'latin1').toString('utf8');

          cb(null, `${fileName}`)
        }
      })
    })
  ],
  controllers: [UploadController],
  providers: [UploadService]
})
export class UploadModule { }