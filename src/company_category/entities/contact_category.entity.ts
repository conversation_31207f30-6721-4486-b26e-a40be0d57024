import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Category } from "src/category/entities/category.entity";
import { Company } from "src/company/entities/company.entity";
import { Contact } from "src/contact/entities/contact.entity";

@Entity()
export class ContactCategory extends CustomBaseEntity {

    //Company
    @ManyToOne(() => Contact, (_) => _.contactCategories)
    contact: Contact;

    //category
    @ManyToOne(() => Category, (_) => _.contactCategories)
    category: Category;

    @Column({ default: true })
    active: boolean

}






