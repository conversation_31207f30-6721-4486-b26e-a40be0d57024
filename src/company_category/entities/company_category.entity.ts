import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Category } from "src/category/entities/category.entity";
import { Company } from "src/company/entities/company.entity";

@Entity()
export class CompanyCategory extends CustomBaseEntity {

    //Company
    @ManyToOne(() => Company, (_) => _.companyCategories)
    @JoinColumn({ name: 'company_id' })
    company: Company;

    //category
    @ManyToOne(() => Category, (_) => _.companyCategories)
    @JoinColumn({ name: 'category_id' })
    category: Category;

    @Column({ default: true })
    active: boolean

}






