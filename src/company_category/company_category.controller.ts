import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CompanyCategoryService } from './company_category.service';
import { CreateCompanyCategoryDto } from './dto/create-company_category.dto';
import { UpdateCompanyCategoryDto } from './dto/update-company_category.dto';

@Controller('company-category')
export class CompanyCategoryController {
  constructor(private readonly companyCategoryService: CompanyCategoryService) {}

  @Post()
  create(@Body() createCompanyCategoryDto: CreateCompanyCategoryDto) {
    return this.companyCategoryService.create(createCompanyCategoryDto);
  }

  @Get()
  findAll() {
    return this.companyCategoryService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.companyCategoryService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCompanyCategoryDto: UpdateCompanyCategoryDto) {
    return this.companyCategoryService.update(+id, updateCompanyCategoryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.companyCategoryService.remove(+id);
  }
}
