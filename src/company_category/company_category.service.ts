import { Injectable } from '@nestjs/common';
import { CreateCompanyCategoryDto } from './dto/create-company_category.dto';
import { UpdateCompanyCategoryDto } from './dto/update-company_category.dto';

@Injectable()
export class CompanyCategoryService {
  create(createCompanyCategoryDto: CreateCompanyCategoryDto) {
    return 'This action adds a new companyCategory';
  }

  findAll() {
    return `This action returns all companyCategory`;
  }

  findOne(id: number) {
    return `This action returns a #${id} companyCategory`;
  }

  update(id: number, updateCompanyCategoryDto: UpdateCompanyCategoryDto) {
    return `This action updates a #${id} companyCategory`;
  }

  remove(id: number) {
    return `This action removes a #${id} companyCategory`;
  }
}
