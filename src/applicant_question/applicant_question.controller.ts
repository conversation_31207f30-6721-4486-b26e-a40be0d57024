import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantQuestionService } from './applicant_question.service';
import { CreateApplicantQuestionDto } from './dto/create-applicant_question.dto';
import { UpdateApplicantQuestionDto } from './dto/update-applicant_question.dto';

@Controller('applicant-question')
export class ApplicantQuestionController {
  constructor(private readonly applicantQuestionService: ApplicantQuestionService) {}

  @Post()
  create(@Body() createApplicantQuestionDto: CreateApplicantQuestionDto) {
    return this.applicantQuestionService.create(createApplicantQuestionDto);
  }

  @Get()
  findAll() {
    return this.applicantQuestionService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantQuestionService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantQuestionDto: UpdateApplicantQuestionDto) {
    return this.applicantQuestionService.update(+id, updateApplicantQuestionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantQuestionService.remove(+id);
  }
}
