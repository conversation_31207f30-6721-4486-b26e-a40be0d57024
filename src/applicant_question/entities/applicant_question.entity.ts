import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Applicant } from "../../applicant/entities/applicant.entity";
import { Question } from "../../question/entities/question.entity";
import { Choice } from "../../question/entities/choice.entity";

@Entity()
export class ApplicantQuestion extends CustomBaseEntity {
    @ManyToOne(() => Question, (_) => _.applicantQuestions)
    question: Question;

    @ManyToOne(() => Choice, (_) => _.applicantQuestions)
    choice: Choice;

    @Column("text", { default: true })
    remark: string

    @Column("text", { default: true })
    remark2: string

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantQuestions)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;
}

