import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantQuestionController } from './applicant_question.controller';
import { ApplicantQuestionService } from './applicant_question.service';

describe('ApplicantQuestionController', () => {
  let controller: ApplicantQuestionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantQuestionController],
      providers: [ApplicantQuestionService],
    }).compile();

    controller = module.get<ApplicantQuestionController>(ApplicantQuestionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
