import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantQuestionService } from './applicant_question.service';

describe('ApplicantQuestionService', () => {
  let service: ApplicantQuestionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicantQuestionService],
    }).compile();

    service = module.get<ApplicantQuestionService>(ApplicantQuestionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
