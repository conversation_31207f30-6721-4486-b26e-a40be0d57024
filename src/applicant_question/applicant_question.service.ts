import { Injectable } from '@nestjs/common';
import { CreateApplicantQuestionDto } from './dto/create-applicant_question.dto';
import { UpdateApplicantQuestionDto } from './dto/update-applicant_question.dto';

@Injectable()
export class ApplicantQuestionService {
  create(createApplicantQuestionDto: CreateApplicantQuestionDto) {
    return 'This action adds a new applicantQuestion';
  }

  findAll() {
    return `This action returns all applicantQuestion`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantQuestion`;
  }

  update(id: number, updateApplicantQuestionDto: UpdateApplicantQuestionDto) {
    return `This action updates a #${id} applicantQuestion`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantQuestion`;
  }
}
