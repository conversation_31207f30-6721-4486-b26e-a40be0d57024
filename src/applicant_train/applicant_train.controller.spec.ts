import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantTrainController } from './applicant_train.controller';
import { ApplicantTrainService } from './applicant_train.service';

describe('ApplicantTrainController', () => {
  let controller: ApplicantTrainController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantTrainController],
      providers: [ApplicantTrainService],
    }).compile();

    controller = module.get<ApplicantTrainController>(ApplicantTrainController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
