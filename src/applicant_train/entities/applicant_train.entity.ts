import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Applicant } from "src/applicant/entities/applicant.entity";
@Entity()
export class ApplicantTrain extends CustomBaseEntity {
    @Column("text", { nullable: true })
    name: string;

    @Column("text", { nullable: true })
    detail: string;

    @Column("text", { nullable: true })
    monthYear: string;

    @Column("text", { nullable: true })
    certificate: string;

    @Column({ default: true })
    active: boolean

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantFamilies)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;

}

