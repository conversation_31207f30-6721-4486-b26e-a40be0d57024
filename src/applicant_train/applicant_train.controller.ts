import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantTrainService } from './applicant_train.service';
import { CreateApplicantTrainDto } from './dto/create-applicant_train.dto';
import { UpdateApplicantTrainDto } from './dto/update-applicant_train.dto';

@Controller('applicant-train')
export class ApplicantTrainController {
  constructor(private readonly applicantTrainService: ApplicantTrainService) {}

  @Post()
  create(@Body() createApplicantTrainDto: CreateApplicantTrainDto) {
    return this.applicantTrainService.create(createApplicantTrainDto);
  }

  @Get()
  findAll() {
    return this.applicantTrainService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantTrainService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantTrainDto: UpdateApplicantTrainDto) {
    return this.applicantTrainService.update(+id, updateApplicantTrainDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantTrainService.remove(+id);
  }
}
