import { Injectable } from '@nestjs/common';
import { CreateApplicantTrainDto } from './dto/create-applicant_train.dto';
import { UpdateApplicantTrainDto } from './dto/update-applicant_train.dto';

@Injectable()
export class ApplicantTrainService {
  create(createApplicantTrainDto: CreateApplicantTrainDto) {
    return 'This action adds a new applicantTrain';
  }

  findAll() {
    return `This action returns all applicantTrain`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantTrain`;
  }

  update(id: number, updateApplicantTrainDto: UpdateApplicantTrainDto) {
    return `This action updates a #${id} applicantTrain`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantTrain`;
  }
}
