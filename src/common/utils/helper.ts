import { BadRequestException } from "@nestjs/common";
import { BinaryToTextEncoding, createHash } from "crypto";
import { DateTime } from "luxon";
import moment from "moment";
import { Leave, LeaveStatusEnum, LeaveTypeEnum } from "src/leave/entities/leave.entity";
import { Between, In } from "typeorm";

export class Helper {
  static isToday(date: Date): boolean {
    const today = new Date();

    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  }

  static generateNo(prefix: string, lastId: number): string {
    // ใช้วันที่ปัจจุบันเพื่อสร้างหมายเลข
    const currentDate = DateTime.local();
    const year = currentDate.year;  // ปี
    const month = (currentDate.month).toString().padStart(2, '0'); // เดือน (1-12)

    // สร้างหมายเลขในรูปแบบ YYYYMM
    const formattedDate = `${year}${month}`;

    // หากมี lastId เพิ่มขึ้น 1, ถ้าไม่มีจะเริ่มต้นที่ 1
    const nextId = lastId ? lastId + 1 : 1;

    // สร้างหมายเลขลำดับเป็น 4 หลัก
    const runNumber = nextId.toString().padStart(4, '0');

    // สร้างรหัสเอกสารในรูปแบบ LYYYYMMXXXX
    const newLeaveCode = `${prefix}${formattedDate}${runNumber}`;

    return newLeaveCode;
  }

  static generateNumber(lastNumber: number | null): number {
    // Get current date in GMT+7 timezone
    const currentDate: Date = new Date();
    const gmtPlus7Time: number = currentDate.getTime() + (7 * 60 * 60 * 1000); // Adjust for GMT+7
    const gmtPlus7Date: string = new Date(gmtPlus7Time).toISOString().slice(0, 10);

    // If it's a new day or no last number provided, start from 1, else use provided last number + 1
    const currentNumber: number = lastNumber === null || gmtPlus7Date !== lastNumber.toString().slice(0, 8)
      ? 1
      : lastNumber + 1;

    // Pad the number with leading zeros
    const formattedNumber: string = currentNumber.toString().padStart(4, '0');

    // Construct the 12-digit number
    const number: number = parseInt(`${gmtPlus7Date.replace(/-/g, '')}${formattedNumber}`);
    return number - 100000000000;
  }

  static generate12DigitDateString = () => {
    const date = new Date();

    // Get the year, month, day, hours, minutes, and seconds from the date
    const year = date.getFullYear().toString().slice(-2);  // e.g., "24" for 2024
    const month = (date.getMonth() + 1).toString().padStart(2, '0');  // e.g., 05 for May
    const day = date.getDate().toString().padStart(2, '0');  // e.g., 29
    const hours = date.getHours().toString().padStart(2, '0');  // e.g., 13 for 1 PM
    const minutes = date.getMinutes().toString().padStart(2, '0');  // e.g., 45
    const seconds = date.getSeconds().toString().padStart(2, '0');  // e.g., 30

    // Combine the components into a single string
    const dateString = `${year}${month}${day}${hours}${minutes}${seconds}`;  // e.g., "20240529134530"

    return dateString;
  }

  static generateChecksum(str: string, algorithm: string, encoding: BinaryToTextEncoding) {
    return createHash(algorithm || 'md5')
      .update(str, 'utf8')
      .digest(encoding || 'hex');
  }

  static duplicateArrayValue(array: any[]) {
    return array.filter((item, index) => array.indexOf(item) !== index);
  }

  static dateInPeriod(start: string, end: string): string[] {
    const output: string[] = [];

    const startDate = DateTime.fromISO(start).toLocal();
    const endDate = DateTime.fromISO(end).toLocal();

    // คำนวณจำนวนวันระหว่าง start และ end
    const { days } = endDate.diff(startDate, 'day').toObject();

    for (let i = 0; i <= days; i++) {
      output.push(startDate.plus({ day: i }).toFormat('yyyy-MM-dd'));
    }

    return output;
  }

  static getDayOfWeek(dateString: string): number {
    const date = new Date(dateString);
    const dayIndex = date.getDay(); // Get day number: 0 (Sun) - 6 (Sat)

    // Adjust the index to make Monday = 1, Sunday = 7
    const adjustedDay = dayIndex === 0 ? 7 : dayIndex; // If it's Sunday (0), return 7 instead
    return adjustedDay;
  }

  static calculateMonthsDifference(dateRegister: Date, currentDate: Date): number {
    const diffTime = Math.abs(currentDate.getTime() - dateRegister.getTime());
    const diffMonths = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30.44)); // ใช้ค่าเฉลี่ยของจำนวนวันใน 1 เดือน (30.44)
    return diffMonths;
  }

  static calculateHours(timeStart: string, timeEnd: string): number {
    const baseDate = "1970-01-01";
    let start = DateTime.fromISO(`${baseDate}T${timeStart}`);
    let end = DateTime.fromISO(`${baseDate}T${timeEnd}`);

    // ถ้าเวลาสิ้นสุดน้อยกว่าเวลาเริ่ม ให้เพิ่มวันถัดไป
    if (end <= start) {
      end = end.plus({ days: 1 });
    }

    let diff = end.diff(start, "hours").hours;

    // คำนวณเวลาพัก (12:30 - 13:30)
    let breakStart = DateTime.fromISO(`${baseDate}T12:30`);
    let breakEnd = DateTime.fromISO(`${baseDate}T13:30`);

    // ปรับ breakStart/End ให้ match กับวันเดียวกับ start
    if (start > breakEnd) {
      // ถ้าเวลาเริ่มอยู่หลังช่วงพัก ให้เลื่อนช่วงพักไปวันถัดไป
      breakStart = breakStart.plus({ days: 1 });
      breakEnd = breakEnd.plus({ days: 1 });
    }

    // ตรวจสอบว่าช่วงเวลาพักอยู่ระหว่าง start และ end
    if (start < breakEnd && end > breakStart) {
      const overlapStart = start > breakStart ? start : breakStart;
      const overlapEnd = end < breakEnd ? end : breakEnd;
      const breakDuration = overlapEnd.diff(overlapStart, "hours").hours;
      diff -= breakDuration;
    }

    // ปัดเศษเป็นครึ่งชั่วโมง
    if (diff % 1 >= 0.5) {
      diff = Math.floor(diff) + 0.5;
    } else {
      diff = Math.floor(diff);
    }

    return diff;
  }

  static convertMinutesToHours(minutes: number): number {
    const hours = Math.floor(minutes / 60); // หาชั่วโมง
    const remainingMinutes = minutes % 60; // หานาทีที่เหลือ
    const decimalMinutes = (remainingMinutes / 60).toFixed(2); // แปลงนาทีเป็นทศนิยมและเก็บ 2 ตำแหน่งทศนิยม

    return parseFloat(`${hours}.${decimalMinutes}`); // รวมชั่วโมงและนาทีที่แปลงแล้ว
  }

  static formatDate(date: string | Date): string {
    const d = new Date(date);
    const year = d.getFullYear(); // ปี 4 หลัก
    const month = (d.getMonth() + 1).toString().padStart(2, '0'); // เดือน 2 หลัก
    const day = d.getDate().toString().padStart(2, '0'); // วัน 2 หลัก
    return `${year}-${month}-${day}`; // คืนค่ารูปแบบ YYYY-MM-DD
  }

  static randomOtp() {

    //send otp
    let otpCode = Math.floor(100000 + Math.random() * 900000).toString()
    let otpRef = this.generateString(4) //4 charater
    const otpExp = DateTime.now().plus({ minutes: 10 }).toFormat('yyyyMMddHHmmss');

    let otpKey = {
      otpCode: otpCode,
      // otpCode: '123456',
      otpRef: otpRef,
      otpExp: otpExp
    }

    return otpKey
  }

  static generateString(length) {

    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }

  static checkOtpExpire(otpExp: string) {

    const now = DateTime.now();
    const otpExpiration = DateTime.fromFormat(otpExp, 'yyyyMMddHHmmss');

    // คำนวณเวลาต่าง (หน่วยเป็นวินาที)
    const diffInSeconds = now.diff(otpExpiration, 'seconds').seconds;

    // แปลงเป็นนาที
    const timeDiffInMinutes = diffInSeconds / 60;

    return Number(timeDiffInMinutes);
  }
}



export function imagePath(path: string | null) {
  if (!path) {
    return null
  }

  return path;
}

/**
 * ตรวจสอบการขอลาทับซ้อน
 * @param employeeId - รหัสพนักงาน
 * @param startDate - วันที่เริ่มต้นของการลา (Date หรือ string)
 * @param endDate - วันที่สิ้นสุดของการลา (Date หรือ string)
 * @returns boolean - true ถ้าไม่พบการลาทับซ้อน, false ถ้ามีการลาทับซ้อน
 */
export async function isLeaveRequestValid(
  employeeId: number,
  startDate: Date,
  endDate: Date,
  type: LeaveTypeEnum
): Promise<boolean> {
  const overlappingLeave = await Leave.findOne({
    where: [
      {
        employee: {
          id: employeeId
        },
        type: type,
        status: In([LeaveStatusEnum.OPEN, LeaveStatusEnum.APPROVED]), // เงื่อนไขสถานะ
        dateStart: Between(startDate, endDate)
      },
      {
        employee: {
          id: employeeId
        },
        status: In([LeaveStatusEnum.OPEN, LeaveStatusEnum.APPROVED]), // เงื่อนไขสถานะ
        type: type,
        dateEnd: Between(startDate, endDate)
      },

    ],
  });

  return !overlappingLeave; // ถ้าไม่เจอการลาทับซ้อน จะคืนค่า true
}