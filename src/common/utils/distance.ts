import { CheckInArea } from "src/check-in-area/entities/check-in-area.entity";

// ฟังก์ชันแปลงองศาเป็นเรเดียน
function degToRad(deg: number): number {
  return deg * (Math.PI / 180);
}

// Calculate distance using the Haversine formula (returns meters)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = degToRad(lat2 - lat1);
  const dLon = degToRad(lon2 - lon1);

  const a = Math.sin(dLat / 2) ** 2 +
            Math.cos(degToRad(lat1)) * Math.cos(degToRad(lat2)) *
            Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

// Check if a location is within the radius (in meters)
export async function checkLocation(lat: number, lon: number, locations: CheckInArea[]) {
  let found = false;

  for (const loc of locations) {
    const distance = calculateDistance(lat, lon, loc.lat, loc.lon);

    if (distance <= loc.area) { // Compare in meters
      console.log(`✅ Within the radius of: ${loc.name} (Radius: ${loc.area} meters)`);
      found = true;
    }
  }

  if (!found) {
    console.log("❌ No locations found within the radius.");
  }

  return found;
}