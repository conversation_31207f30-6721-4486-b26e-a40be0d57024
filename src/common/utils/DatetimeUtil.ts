import { DateTime } from "luxon";

export function datetime2string(datetime: Date) {
  return {
    datetime: DateTime.fromJSDate(datetime).toFormat('dd/MM/yyyy HH:mm:ss'),
    date: DateTime.fromJSDate(datetime).toFormat('dd/MM/yyyy'),
    time: DateTime.fromJSDate(datetime).toFormat('HH:mm:ss')
  }
}

//case 1:  2024-02-29 2025-02-28
//case 2:  2024-03-01 2025-02-28
//case 3:  2023-02-28 2024-02-27
//case 4:  2023-03-01 2024-02-29
//case 5:  2024-01-01 2024-12-31
export function calculateNextPeriod(datetime: Date) {
  const endDate = DateTime.fromJSDate(datetime).toLocal().plus({ day: 1 });

  const startPeriod = endDate;
  let endPeriod = endDate.plus({ year: 1 }).minus({ day: 1 })

  // Adjust for leap year only if February is in the range
  if (startPeriod.month === 2 && startPeriod.day === 29) {
    endPeriod = endPeriod.plus({ day: 1 }); // Extend for leap year end period
  }

  return { startPeriod: startPeriod.toJSDate(), endPeriod: endPeriod.toJSDate() }
}