import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class EmployeeTypeSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "employee_type" RESTART IDENTITY CASCADE;');
        
        await dataSource
            .createQueryBuilder()
            .insert()
            .into('employee_type')
            .values([
                { code: '001', name: 'Office', group: 1 },
                { code: '002', name: 'On-site', group: 1 },
                { code: '003', name: 'Temporary', group: 1 },
                { code: '004', name: 'Outsource', group: 1 },
            ])
            .execute();
    }
}