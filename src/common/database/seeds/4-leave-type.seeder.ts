import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class LeaveTypeSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "leave_type" RESTART IDENTITY CASCADE;');

        await dataSource
            .createQueryBuilder()
            .insert()
            .into('leave_type')
            .values([
                { code: 'LT001', name: 'Annual', sex: 'all' },
                { code: 'LT002', name: 'Casual', sex: 'all' },
                { code: 'LT003', name: 'Sick', sex: 'all' },
                { code: 'LT004', name: 'Without Pay', sex: 'all' },
                { code: 'LT005', name: 'Other', sex: 'all' },
                { code: 'LT006', name: 'Maternity', sex: 'female' },
            ])
            .execute();
    }
}