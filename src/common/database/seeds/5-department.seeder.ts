import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class DepartmentSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "department" RESTART IDENTITY CASCADE;');
        
        await dataSource
            .createQueryBuilder()
            .insert()
            .into('department')
            .values([
                { code: '1', name: 'Accountant', group: 1 },
                { code: '2', name: 'HR', group: 1 },
                { code: '3', name: 'IT', group: 1 },
                { code: '4', name: 'Secretary', group: 1 },
                { code: '5', name: 'Admin', group: 1 },
                { code: '6', name: 'BD', group: 1 },
                { code: '7', name: 'CSA', group: 2 },
                { code: '8', name: 'ME<PERSON>', group: 2 },
                { code: '9', name: '<PERSON><PERSON>', group: 2 },
                { code: '10', name: 'Tranaportation', group: 2 },
                { code: '11', name: 'Lighting', group: 2 },
                { code: '12', name: '<PERSON><PERSON><PERSON><PERSON>', group: 2 },
                { code: '13', name: 'Quantity Surveyor', group: 2 },
                { code: '14', name: 'CSA BIM Modeler', group: 3 },
                { code: '15', name: 'MEP BIM Modeler', group: 3 },
                { code: '16', name: 'CSA CAD', group: 3 },
                { code: '17', name: 'MEP CAD', group: 3 },
                { code: '18', name: 'Lighting CAD', group: 3 },
                { code: '19', name: 'Site Staff', group: 4 },
                { code: '20', name: 'PMCM', group: 5 },
                { code: '21', name: 'Architect', group: 2 },
                { code: '22', name: 'Management', group: 7 },
                { code: '23', name: 'Director/Team Leader', group: 2 },
                { code: '24', name: 'Director/Dicipline Leader', group: 2 }
            ])
            .execute();
    }
}