import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class LevelSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "level" RESTART IDENTITY CASCADE;');

        await dataSource
            .createQueryBuilder()
            .insert()
            .into('level')
            .values([
                { code: 1, name: 'A1', description: 'Administrative Level 1' },
                { code: 2, name: 'A2', description: 'Administrative Level 2' },
                { code: 3, name: 'A3', description: 'Administrative Level 3' },
                { code: 4, name: 'AD1-TL', description: '' },
                { code: 5, name: 'AD2-TL', description: '' },
                { code: 6, name: 'AD-DL', description: '' },
                { code: 7, name: 'B1', description: 'BIM Modeler 1' },
                { code: 8, name: 'B2', description: 'BIM Modeler 2' },
                { code: 9, name: 'B3', description: 'BIM Modeler 3' },
                { code: 10, name: 'C1', description: '' },
                { code: 11, name: 'C2', description: '' },
                { code: 12, name: 'C3', description: '' },
                { code: 13, name: 'D1', description: 'Draftman 1' },
                { code: 14, name: 'DI', description: 'Director' },
                { code: 15, name: 'E1', description: 'Engineer Level 1' },
                { code: 16, name: 'E2', description: 'Engineer Level 2' },
                { code: 17, name: 'E3', description: 'Engineer Level 3' },
                { code: 18, name: 'EC1', description: '' },
                { code: 19, name: 'EC2', description: '' },
                { code: 20, name: 'EE1', description: '' },
                { code: 21, name: 'EE2', description: '' },
                { code: 22, name: 'EE2-TL', description: '' },
                { code: 23, name: 'EE1-TL', description: '' },
                { code: 24, name: 'ES1', description: '' },
                { code: 25, name: 'ES2', description: '' },
                { code: 26, name: 'ET1', description: 'Exclusive Technology Level 1' },
                { code: 27, name: 'ET2', description: 'Exclusive Technology Level 2' },
                { code: 28, name: 'I1', description: '' },
                { code: 29, name: 'I2', description: '' },
                { code: 30, name: 'I3', description: '' },
                { code: 31, name: 'MD', description: '' },
                { code: 32, name: 'S1', description: '' },
                { code: 33, name: 'S2', description: '' },
                { code: 34, name: 'S3', description: '' },
                { code: 35, name: 'SA1', description: '' },
                { code: 36, name: 'SA2', description: '' },
                { code: 37, name: 'SB1', description: '' },
                { code: 38, name: 'SB2', description: '' },
                { code: 39, name: 'SC1', description: '' },
                { code: 40, name: 'SC2', description: '' },
                { code: 41, name: 'SD2', description: '' },
                { code: 42, name: 'SE1', description: '' },
                { code: 43, name: 'SE2', description: '' },
                { code: 44, name: 'SES', description: '' },
                { code: 45, name: 'SI1', description: '' },
                { code: 46, name: 'SI2', description: '' },
                { code: 47, name: 'SS1', description: '' },
                { code: 48, name: 'SS2', description: '' },
                { code: 49, name: 'ST1', description: 'Senior IT Level 1' },
                { code: 50, name: 'T1', description: '' },
                { code: 51, name: 'TD', description: '' }
                
            ])
            .execute();
           }
       }