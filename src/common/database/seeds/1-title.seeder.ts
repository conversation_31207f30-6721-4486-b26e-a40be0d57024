import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class TitleSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "title" RESTART IDENTITY CASCADE;');

        await dataSource
            .createQueryBuilder()
            .insert()
            .into('title')
            .values([
                { code: 'T001', name: 'Administrative Manager' },
                { code: 'T002', name: 'Account Assistant' },
                { code: 'T003', name: 'Associate Director' },
                { code: 'T004', name: 'Senior C&S CAD Coordinator' },
                { code: 'T005', name: 'C&S CAD Operator1' },
                { code: 'T006', name: 'C&S CAD Operator2' },
                { code: 'T007', name: 'C&S CAD Operator3' },
                { code: 'T008', name: 'C&S Site Engineer' },
                { code: 'T009', name: 'C&S Site Inspector' },
                { code: 'T010', name: 'CAD Manager' },
                { code: 'T011', name: 'Chief Accountant' },
                { code: 'T012', name: 'Civil Engineer' },
                { code: 'T013', name: 'Construction Manager' },
                { code: 'T014', name: 'Director' },
                { code: 'T015', name: 'Driver' },
                { code: 'T016', name: 'Electrical Engineer' },
                { code: 'T017', name: 'Electrical Site Engineer' },
                { code: 'T018', name: 'Energy Technician' },
                { code: 'T019', name: 'Executive Electrical Engineer' },
                { code: 'T020', name: 'Executive Mechanical Engineer' },
                { code: 'T021', name: 'Executive Structural Engineer' },
                { code: 'T022', name: 'Geotechnical Engineer' },
                { code: 'T023', name: 'HR Assistant' },
                { code: 'T024', name: 'Human Resources Manager' },
                { code: 'T025', name: 'IT Manager' },
                { code: 'T026', name: 'IT Specialist' },
                { code: 'T027', name: 'IT Supervisor' },
                { code: 'T028', name: 'Lighting Designer' },
                { code: 'T029', name: 'Senior M&E CAD Co-ordinator' },
                { code: 'T030', name: 'M&E CAD Operator' },
                { code: 'T031', name: 'M&E Site Inspector' },
                { code: 'T032', name: 'Maid' },
                { code: 'T033', name: 'Managing Director' },
                { code: 'T034', name: 'Mechanical Engineer' },
                { code: 'T035', name: 'Mechanical Site Engineer' },
                { code: 'T036', name: 'Messenger' },
                { code: 'T037', name: 'Senior ODC CAD Operator' },
                { code: 'T038', name: 'Senior Project Manager' },
                { code: 'T039', name: 'Quantity Surveyor' },
                { code: 'T040', name: 'Receptionist' },
                { code: 'T041', name: 'Resident Engineer' },
                { code: 'T042', name: 'Safety Officer' },
                { code: 'T043', name: 'Sanitary Engineer' },
                { code: 'T044', name: 'Secretary' },
                { code: 'T045', name: 'Senior Electrical Engineer' },
                { code: 'T046', name: 'Senior Electrical Site Engineer' },
                { code: 'T047', name: 'Senior Lighting Designer' },
                { code: 'T048', name: 'Senior Mechanical Engineer' },
                { code: 'T049', name: 'Senior Mechanical Site Engineer' },
                { code: 'T050', name: 'Senior Sanitary Engineer' },
                { code: 'T051', name: 'Senior Site Architect' },
                { code: 'T052', name: 'Senior Structural Engineer' },
                { code: 'T053', name: 'Senior Structural Site Engineer' },
                { code: 'T054', name: 'Site Architect' },
                { code: 'T055', name: 'Site Secretary' },
                { code: 'T056', name: 'Senior Site Inspector' },
                { code: 'T057', name: 'Structural Engineer' },
                { code: 'T058', name: 'Structural Site Engineer' },
                { code: 'T059', name: 'Typist' },
                { code: 'T060', name: 'M&E CAD Operator1' },
                { code: 'T061', name: 'M&E CAD Operator2' },
                { code: 'T062', name: 'Lighting Designer1' },
                { code: 'T063', name: 'Executive Engineering Co-ordinator' },
                { code: 'T064', name: 'Graphic Designer' },
                { code: 'T065', name: 'Head Secretary' },
                { code: 'T066', name: 'Facade CAD Operator' },
                { code: 'T067', name: 'Executive Transport Engineer' },
                { code: 'T068', name: 'Quantity Surveyor' },
                { code: 'T069', name: 'Project Manager' },
                { code: 'T070', name: 'ESD Engineer' },
                { code: 'T071', name: 'Senior Facade Engineer' },
                { code: 'T072', name: 'Senior Admin' },
                { code: 'T073', name: 'Accountant' },
                { code: 'T074', name: 'Senior Accountant' },
                { code: 'T075', name: 'Admin' },
                { code: 'T076', name: 'Architect Site Inspector' },
                { code: 'T077', name: 'Assistant Project Manager' },
                { code: 'T078', name: 'Associate Director' },
                { code: 'T079', name: 'BIM Modeller' },
                { code: 'T080', name: 'BIM Manager' },
                { code: 'T081', name: 'BIM Supervisor' },
                { code: 'T082', name: 'Business Development Manager' },
                { code: 'T083', name: 'Cost Engineer' },
                { code: 'T084', name: 'Senior IT Specialist' },
                { code: 'T085', name: 'M&E CAD Operator' },
                { code: 'T086', name: 'M&E Site Inspector' },
                { code: 'T087', name: 'Lighting CAD Operator' },
                { code: 'T088', name: 'Maid & Circ' },
                { code: 'T089', name: 'ODC CAD Operator' },
                { code: 'T090', name: 'ODC Manager' },
                { code: 'T091', name: 'ESD Manager' },
                { code: 'T092', name: 'Executive Sanitary Engineer' },
                { code: 'T093', name: 'Senior Geotechnical Engineer' },
                { code: 'T094', name: 'Resident Engineer' },
                { code: 'T095', name: 'Senior C&S CAD Operator' },
                { code: 'T096', name: 'Senior M&E CAD Operator' },
                { code: 'T097', name: 'Senior Quantity Surveyor' },
                { code: 'T098', name: 'Senior Receptionist' },
                { code: 'T099', name: 'Senior Safety Officer' },
                { code: 'T100', name: 'Senior Structural Coordinator' },
                { code: 'T101', name: 'Senior Transportation Engineer' },
                { code: 'T102', name: 'Director' },
                { code: 'T103', name: 'Executive Lighting Designer' },
                { code: 'T104', name: 'Senior Civil Engineer' },
                { code: 'T105', name: 'Transportation Engineer' },
                { code: 'T106', name: 'Executive Geotechnical Engineer' },
                { code: 'T107', name: 'Business Development Assistant' },
                { code: 'T108', name: 'Assistant Facade Designer' },
                { code: 'T109', name: 'Senior Secretary' },
                { code: 'T110', name: 'Senior Site Secretary' },
                { code: 'T111', name: 'Facade Engineer' },
                { code: 'T112', name: 'BIM Technical Consultant' },
                { code: 'T113', name: 'Process Specialist' },
                { code: 'T114', name: 'Document Controller' },
                { code: 'T115', name: 'Piping Engineer' },
                { code: 'T116', name: 'Facade Designer' },
                { code: 'T117', name: 'Senior HR Assistant' },
                { code: 'T118', name: 'Senior Construction Manager' },
                { code: 'T119', name: 'Senior Document Controller' },
                { code: 'T120', name: 'Commercial Engineer' },
                { code: 'T121', name: 'Senior Commercial Engineer' },
                { code: 'T122', name: 'Executive Structural Coordinator' },
                { code: 'T123', name: 'Recruitment Specialist' },
                { code: 'T124', name: 'Executive Mechanical Engineer' },
                { code: 'T125', name: 'Senior Architect' },
                { code: 'T126', name: 'Architect' },
                { code: 'T127', name: 'Architectural CAD Operator' },
                { code: 'T128', name: 'Executive Civil Engineer' },
                { code: 'T129', name: 'Executive Facade Manager' },
                { code: 'T130', name: 'Senior QC Inspector' },
                { code: 'T131', name: 'Safety Manager' },
                { code: 'T132', name: 'IMR Assistant' },
                { code: 'T133', name: 'Senior Human Resources Officer' },
                { code: 'T134', name: 'Senior BIM Modeller' },
                { code: 'T135', name: 'Senior Facade Engineer' },
                { code: 'T136', name: 'Senior Document Controller' },
                { code: 'T137', name: 'Executive Mechanical Coordinator' },
                { code: 'T138', name: 'Junior Accountant' },
                { code: 'T139', name: 'Project Manager - Design Management' },
                { code: 'T140', name: 'Executive Facade Engineer' },
                { code: 'T141', name: 'Senior HR Officer' },
                { code: 'T142', name: 'Senior BIM Modeller' },
                { code: 'T143', name: 'IMS Administrator' },
                { code: 'T144', name: 'Executive Mechanical Coordinator & TL' },
                { code: 'T145', name: 'Executive Structural Engineer & TL' },
                { code: 'T146', name: 'Deputy Project Manager' },
                { code: 'T147', name: 'Senior Transportation Engineer' },
                { code: 'T148', name: 'Senior Piping Engineer' },
                { code: 'T149', name: 'Senior Civil Engineer' },
                { code: 'T150', name: 'C&S BIM Modeler' },
                { code: 'T151', name: 'CFD Modeler' },
                { code: 'T152', name: 'Senior Project Manager' },
                { code: 'T153', name: 'Senior ESD Engineer' },
                { code: 'T154', name: 'C&S BIM Manager' },
                { code: 'T155', name: 'Executive Civil Engineer' },
                { code: 'T156', name: 'Executive Civil Engineer' },
                { code: 'T157', name: 'Executive Transportation Engineer' },
                { code: 'T158', name: 'Deputy IMR' },
                { code: 'T159', name: 'Senior Quantity Surveyor' },
                { code: 'T160', name: 'Senior Planning Engineer' },
                { code: 'T161', name: 'IMS Officer' },
                { code: 'T162', name: 'Assistant Administrative Manager' },
                { code: 'T163', name: 'Technical Director' },
                { code: 'T164', name: 'Associate Director & Team Leader' },
                { code: 'T165', name: 'Associate Director & Discipline Leader' },
                { code: 'T166', name: 'M&E BIM Coordinator' },
                { code: 'T167', name: 'Procurement Engineer' },
                { code: 'T168', name: 'Procurement Engineer' },
                { code: 'T169', name: 'Senior Construction Manager' },
                { code: 'T170', name: 'Site Quantity Surveyor' },
                { code: 'T171', name: 'Leader Industrial Sector, Executive Mechanical Engineer' },
                { code: 'T172', name: 'IMR' },
                { code: 'T173', name: 'Associate Director, C&S CAD & BIM Supervisor' },
                { code: 'T174', name: 'Senior Architect Site Inspector' },
                { code: 'T175', name: 'Junior ESD Engineer' },
                { code: 'T176', name: 'Assistant HR Manager' },
                { code: 'T177', name: 'Senior E&I Engineer' },
                { code: 'T178', name: 'ESD Consultant' },
                { code: 'T179', name: 'Junior HR Assistant' },
                { code: 'T180', name: 'Director - Business Development & Marketing' },
                { code: 'T181', name: 'CSA BIM Manager' },
                { code: 'T182', name: 'Junior Purchasing Officer' },
                { code: 'T183', name: 'Executive Advisor to Board of Directors' },
                { code: 'T184', name: 'Graphic Designer' },
                { code: 'T185', name: 'Junior QA/QC Engineer' },
                { code: 'T186', name: 'Senior HR Manager' },
                { code: 'T187', name: 'Senior ESD Consultant' },
                { code: 'T188', name: 'Senior C&S BIM Modeler' },
                { code: 'T189', name: 'Senior M&E BIM Modeler' },
                { code: 'T190', name: 'M&E BIM Modeler' },
                { code: 'T191', name: 'Executive Personal Assistant & Senior Administrative Manager' },
                { code: 'T192', name: 'Human Resources Officer' },
                { code: 'T193', name: 'Acting Business Development Manager' },
                { code: 'T194', name: 'Senior M&E BIM Co-ordinator' },
                { code: 'T195', name: 'Acting Business Development Manager' },
                { code: 'T196', name: 'Senior Document Controller' },
                { code: 'T197', name: 'Assistant Business Development Manager' },
                { code: 'T198', name: 'Executive Sanitary Engineer & Acting TL' },
                { code: 'T199', name: 'Energy Modeler' },
                { code: 'T200', name: 'Acting Team Leader' },
                { code: 'T201', name: 'Senior Mechanical Engineer & Acting Team Leader' },
                { code: 'T202', name: 'Executive Sanitary Engineer & Acting Team Leader' },
                { code: 'T203', name: 'Senior C&S BIM Co-ordinator' },
                { code: 'T204', name: 'CSA BIM Coordinator' },
                { code: 'T205', name: 'Deputy Managing Director' },
                { code: 'T206', name: 'FM Manager' },
                { code: 'T207', name: 'Executive Sanitary Engineer & Team Leader' },
                { code: 'T208', name: 'Executive Structural Engineer & Team Leader' },
                { code: 'T209', name: 'Executive Mechanical Engineer & Team Leader' },
                { code: 'T210', name: 'Senior Procurement Engineer' },
                { code: 'T211', name: 'Senior M&E Site Inspector' },
                { code: 'T212', name: 'Senior BIM Modeler and OEDC Coordinator' },
                { code: 'T213', name: 'Senior BIM Modeler and OEDC Coordinator' },
                { code: 'T214', name: 'Executive Engineering Coordinator' }

    ])
     .execute();
    }
}