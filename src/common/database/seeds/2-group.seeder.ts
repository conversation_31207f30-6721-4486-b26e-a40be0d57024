import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class GroupSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "group" RESTART IDENTITY CASCADE;');

        await dataSource
            .createQueryBuilder()
            .insert()
            .into('group')
            .values([
                {code:'1', name:'Support'},
                {code:'2', name:'Engineering'},
                {code:'3', name:'BIM/CAD'},
                {code:'4', name:'Site Staff'},
                {code:'5', name:'PM/CM Management'},
                {code:'6', name:'Architect'},
                {code:'7', name:'Management'},
            ])
            .execute();
           }
       }