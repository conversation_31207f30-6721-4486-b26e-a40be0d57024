import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Employee } from "src/employee/entities/employee.entity";
import { Title } from "src/title/entities/title.entity";
import { Department } from "src/department/entities/department.entity";
import { User } from "src/user/entities/user.entity";
import { Job } from "src/job/entities/job.entity";

export enum PersonalFormStatusEnum {
  REQUEST = 'request',
  APPROVED = 'approved',
  REJECT = 'reject',
  CANCEL = 'cancel',
}

@Entity()
export class PersonalForm extends CustomBaseEntity {
  @Index({ unique: true })
  @Column()
  code: string;

  @ManyToOne(() => Title, (_) => _.personalForms)
  position: Title;

  @ManyToOne(() => Department, (_) => _.personalForms)
  department: Department;

  @Column('date', { nullable: true })
  dateToStart: Date;

  @Column({ nullable: true })
  numberOfRequests: number;

  @Column({ nullable: true })
  maritalStatus: string;

  @Column({ nullable: true })
  sex: string;

  @Column({ nullable: true })
  education: string;

  @Column({ nullable: true })
  major: string;

  @Column({ nullable: true })
  experience: string;

  @Column({ nullable: true })
  skillLanguage: string;

  @Column({ nullable: true })
  skillTyping: string;

  @Column({ nullable: true })
  skillOther: string

  @Column({ nullable: true })
  trainingCourse: string

  @Column({ nullable: true })
  otherRequirement: string

  @Column({ nullable: true })
  staffDetail: string

  @Column({ nullable: true })
  remark: string

  @ManyToOne(() => Employee, (_) => _.personalFormsRequest)
  requestBy: Employee;

  @Column('date', { nullable: true })
  requestDate: Date

  @ManyToOne(() => Employee, (_) => _.personalFormsApprove)
  approveBy: Employee;

  @Column('date', { nullable: true })
  approveDate: Date

  // @ManyToOne(() => User, (_) => _.personalFormsOrganise)
  // organisedBy: User;

  // @Column('date', { nullable: true })
  // organisedDate: Date

  @OneToMany(() => Job, (_) => _.personalForm)
  jobs: Job[];

  @Column({ type: 'enum', enum: PersonalFormStatusEnum, default: PersonalFormStatusEnum.REQUEST, nullable: true })
  status: PersonalFormStatusEnum;
}
