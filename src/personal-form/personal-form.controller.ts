import { Controller, Post, Body, Get, Param, Put, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { CreatePersonalFormDto } from './dto/create-personal-form.dto';
import { ApiTags } from '@nestjs/swagger';
import { PERSONAL_FORM_PAGINATION_CONFIG, PersonalFormService } from './personal-form.service';
import { UpdatePersonalFormDto } from './dto/update-personal-form.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApprovePersonalFormDto } from './dto/apprve-personal-form.dto';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('personal-form')
@ApiTags('เปิดรับพนักงาน')
@Auth()
export class PersonalFormController {
  constructor(private readonly personalFormService: PersonalFormService) { }

  @Post('/:id/approve')
  approve(@Param('id') id: string, @Body() payload: ApprovePersonalFormDto) {
    return this.personalFormService.approve(+id, payload);
  }

  @Post('/')
  create(@Body() payload: CreatePersonalFormDto) {
    return this.personalFormService.create(payload);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PERSONAL_FORM_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.personalFormService.datatables(query);
  }

  @Get('/:id')
  findOne(@Param('id') id: string) {
    return this.personalFormService.findOne(+id);
  }

  @Put('/:id')
  update(@Param('id') id: string, @Body() payload: UpdatePersonalFormDto) {
    return this.personalFormService.update(+id, payload);
  }

  @Delete('/:id')
  remove(@Param('id') id: string) {
    return this.personalFormService.remove(+id);
  }
}
