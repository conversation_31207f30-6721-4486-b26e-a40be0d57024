import { Injectable, NotFoundException } from '@nestjs/common';
import { CreatePersonalFormDto } from './dto/create-personal-form.dto';
import { UpdatePersonalFormDto } from './dto/update-personal-form.dto';
import { PersonalForm } from './entities/personal-form.entity';
import { FilterOperator, paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Helper } from 'src/common/utils/helper';
import { ApprovePersonalFormDto } from './dto/apprve-personal-form.dto';

export const PERSONAL_FORM_PAGINATION_CONFIG: PaginateConfig<PersonalForm> = {
  relations: ['department', 'position', 'approveBy', 'requestBy'],
  sortableColumns: ['id', 'code', 'dateToStart', 'status'],
  // select: [],
  searchableColumns: ['code', 'approveBy.firstname', 'approveBy.lastname', 'requestBy.firstname', 'requestBy.lastname'],
  filterableColumns: {
    'position.id': [FilterOperator.EQ],
    'department.id': [FilterOperator.EQ],
    'approveBy.id': [FilterOperator.EQ],
    'requestBy.id': [FilterOperator.EQ],
    // code: [FilterOperator.EQ],
    // date: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    // dateStart: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    // dateEnd: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    'status': [FilterOperator.EQ],
    // 'employee.id': [FilterOperator.EQ],
    // 'leaveType.id': [FilterOperator.EQ],
    // 'head.id': [FilterOperator.EQ]
  },
};

@Injectable()
export class PersonalFormService {

  findAll() {
    return `This action returns all personalForm`;
  }

  async findOne(id: number) {
    const data = await PersonalForm.findOne({
      where: {
        id: id,
      },
      relations: {
        position: true,
        department: true,
        approveBy: true,
        requestBy: true
      }
    });

    if (!data) {
      throw new NotFoundException("data not found");
    }

    return data
  }

  async update(id: number, payload: UpdatePersonalFormDto) {
    const data = await PersonalForm.findOne({
      where: {
        id: id,
      },
      relations: {
        position: true,
        department: true,
        approveBy: true,
        requestBy: true
      }
    });

    if (!data) {
      throw new NotFoundException("data not found");
    }

    const update = PersonalForm.create({
      ...payload,
      position: {
        id: payload.positionId
      },
      department: {
        id: payload.departmentId
      },
      requestBy: {
        id: payload.requesterId
      },
      approveBy: {
        id: payload.approverId
      }
    });

    await PersonalForm.update(id, update)

    return this.findOne(id);
  }

  async remove(id: number) {
    const data = await PersonalForm.findOne({
      where: {
        id: id,
      }
    });

    if (!data) {
      throw new NotFoundException("data not found");
    }

    await data.softRemove();
  }

  async create(payload: CreatePersonalFormDto) {
    const form = PersonalForm.create({
      ...payload,
      code: await this.autoCode(),
      position: {
        id: payload.positionId
      },
      department: {
        id: payload.departmentId
      },
      requestBy: {
        id: payload.requesterId
      },
      approveBy: {
        id: payload.approverId
      }
    });

    await form.save()

    return form;
  }

  async datatables(query: PaginateQuery): Promise<Paginated<PersonalForm>> {
    return paginate(query, PersonalForm.getRepository(), PERSONAL_FORM_PAGINATION_CONFIG);
  }

  private async autoCode() {
    const data = await PersonalForm.find({
      order: {
        id: 'DESC'
      },
      take: 1,
    })

    let lastId = 0;

    if (data.length > 0) {
      lastId = data[0].id;
    }

    return Helper.generateNo('RE', lastId).toString()
  }

  async approve(id: number, payload: ApprovePersonalFormDto) {
    const data = await PersonalForm.findOne({
      where: {
        id: id,
      },
      relations: {
        approveBy: true
      }
    });

    if (!data) {
      throw new NotFoundException("data not found");
    }

    if (data.approveBy.id != payload.approverId) {
      throw new NotFoundException("You don't have permission to approve this form");
    }

    let update = null

    if (payload.approverId) {
      update = PersonalForm.create({
        approveBy: {
          id: payload.approverId
        },
        approveDate: payload.approveDate,
        status: payload.status
      });
    }

    await PersonalForm.update(id, update)

    return this.findOne(id);
  }
}
