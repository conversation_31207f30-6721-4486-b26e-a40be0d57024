import { ApiProperty } from "@nestjs/swagger";
import { PersonalFormStatusEnum } from "../entities/personal-form.entity";
import { IsNotEmpty } from "class-validator";

export class ApprovePersonalFormDto {
    readonly approverId: number;

    @ApiProperty({ type: Date, format: 'date' })
    readonly approveDate: Date;

    @IsNotEmpty()
    readonly status: PersonalFormStatusEnum;
    // readonly organiserId: number;

    // @ApiProperty({ type: Date, format: 'date' })
    // readonly organisedDate: Date;
}
