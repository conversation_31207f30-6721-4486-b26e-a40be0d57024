import { ApiProperty } from "@nestjs/swagger";

export class CreatePersonalFormDto {
    readonly positionId: number;

    readonly departmentId: number;

    @ApiProperty({ type: Date, format: 'date' })
    readonly dateToStart: Date;

    readonly numberOfRequests: number;

    readonly sex: string;

    readonly education: string;

    readonly maritalStatus: string;

    readonly major: string;

    readonly experience: string;

    readonly skillLanguage: string;

    readonly skillTyping: string;

    readonly skillOther: string

    readonly trainingCourse: string

    readonly otherRequirement: string

    readonly staffDetail: string

    readonly remark: string

    readonly requesterId: number;

    @ApiProperty({ type: Date, format: 'date' })
    readonly requestDate: Date;

    readonly approverId: number;

    @ApiProperty({ type: Date, format: 'date' })
    readonly approveDate: Date;
}
