import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Permission } from '../../permission/entities/permission.entity';
import { User } from '../../user/entities/user.entity';
import {
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  Unique,
} from 'typeorm';

@Entity()
@Unique(['name'])
export class Role extends CustomBaseEntity {
  @Column({ comment: 'Unique name of the role' })
  // @Index({ unique: true })
  name: string; // Unique name of the role

  @Column({ nullable: true, comment: 'Description of the role' })
  description: string; // Description of the role (optional)

  @Column({ default: true })
  isDeletable: boolean;

  @ManyToMany(() => User, (_) => _.roles)
  users: User[];

  @ManyToMany(() => Permission, (_) => _.roles)
  @JoinTable()
  permissions: Permission[];
}
