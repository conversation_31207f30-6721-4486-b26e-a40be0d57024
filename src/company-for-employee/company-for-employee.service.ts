import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCompanyForEmployeeDto } from './dto/create-company-for-employee.dto';
import { UpdateCompanyForEmployeeDto } from './dto/update-company-for-employee.dto';
import { CompanyForEmployee } from './entities/company-for-employee.entity';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Not } from 'typeorm';

export const COM_4_EMP_PAGINATION_CONFIG: PaginateConfig<CompanyForEmployee> = {
  sortableColumns: ['id', 'initial', 'name', 'address'],
  // select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['initial', 'name', 'address']
};

@Injectable()
export class CompanyForEmployeeService {
  async datatables(query: PaginateQuery): Promise<Paginated<CompanyForEmployee>> {
    return paginate(query, CompanyForEmployee.getRepository(), COM_4_EMP_PAGINATION_CONFIG);
  }

  async create(createLevelDto: CreateCompanyForEmployeeDto) {

    //check code exist
    const check = await CompanyForEmployee.existsBy({
      initial: createLevelDto?.initial,
    })
    if (check) {
      throw new BadRequestException('Name already.')
    }

    const item = CompanyForEmployee.create({ ...createLevelDto });

    return item.save();
  }


  findAll() {
    return CompanyForEmployee.find({
      order: {
        initial: 'ASC'
      }
    });
  }

  async findOne(id: number) {
    const item = await CompanyForEmployee.findOne({
      where: { id }
    });

    if (!item) throw new NotFoundException("Data not found.");

    return item;
  }

  async update(id: number, updateLevelDto: UpdateCompanyForEmployeeDto) {
    //check level code exist
    const check = await CompanyForEmployee.existsBy({
      initial: updateLevelDto?.initial,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Name already.')
    }

    const item = await this.findOneById(id);

    if (!item) throw new NotFoundException("Data not found");

    return CompanyForEmployee.update(id, {
      ...updateLevelDto,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("Data not found");

    await item.remove();
  }

  findOneById(id: number) {
    return CompanyForEmployee.findOne({ where: { id } });
  }
}
