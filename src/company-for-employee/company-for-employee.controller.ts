import { Controller, Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus, Put } from '@nestjs/common';
import { COM_4_EMP_PAGINATION_CONFIG, CompanyForEmployeeService } from './company-for-employee.service';
import { CreateCompanyForEmployeeDto } from './dto/create-company-for-employee.dto';
import { UpdateCompanyForEmployeeDto } from './dto/update-company-for-employee.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiTags } from '@nestjs/swagger';

@Controller('company-for-employee')
@Auth()
@ApiTags('company-for-employee')
export class CompanyForEmployeeController {
  constructor(private readonly companyForEmployeeService: CompanyForEmployeeService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(COM_4_EMP_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.companyForEmployeeService.datatables(query);
  }

  @Post()
  create(@Body() createCompanyForEmployeeDto: CreateCompanyForEmployeeDto) {
    return this.companyForEmployeeService.create(createCompanyForEmployeeDto);
  }

  @Get()
  findAll() {
    return this.companyForEmployeeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.companyForEmployeeService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCompanyForEmployeeDto: UpdateCompanyForEmployeeDto) {
    return this.companyForEmployeeService.update(+id, updateCompanyForEmployeeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.companyForEmployeeService.remove(+id);
  }
}
