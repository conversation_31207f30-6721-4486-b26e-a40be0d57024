import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus, Put, Query } from '@nestjs/common';
import { CANDIDATE_PAGINATION_CONFIG, CandidateService } from './candidate.service';
import { UpdateCandidateDto } from './dto/update-candidate.dto';
import { ApplyCandidateDto } from './dto/apply-candidate.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { SelectCandidateDto } from './dto/select-candidate.dto';

@Controller('candidate')
@ApiTags('ผู้สมัครงาน')
export class CandidateController {
  constructor(private readonly candidateService: CandidateService) { }

  @Get('/my-job')
  myJob(@Query('applicantId') applicantId: number) {
    return this.candidateService.myJob(applicantId);
  }

  @Post('/select-candidate')
  selectCandiate(@Body() payload: SelectCandidateDto) {
    return this.candidateService.selectCandidate(payload);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CANDIDATE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.candidateService.datatables(query);
  }

  @Post('/apply-now')
  create(@Body() payload: ApplyCandidateDto) {
    return this.candidateService.applyNow(payload);
  }

  @Get()
  findAll() {
    return this.candidateService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.candidateService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCandidateDto: UpdateCandidateDto) {
    return this.candidateService.update(+id, updateCandidateDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.candidateService.remove(+id);
  }
}
