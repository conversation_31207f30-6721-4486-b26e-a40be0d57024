import { Applicant } from "src/applicant/entities/applicant.entity";
import { CustomBaseEntity } from "src/common/entities";
import { Employee } from "src/employee/entities/employee.entity";
import { Job } from "src/job/entities/job.entity";
import { Column, Entity, ManyToOne } from "typeorm";

export enum CandidateStatusEnum {
  APPLY = 'APPLY',
  SELECTED = 'SELECTED',
  INTERVIEW1 = 'INTERVIEW1',
  INTERVIEW2 = 'INTERVIEW2',
  INTERVIEW3 = 'INTERVIEW3',
  PASS = 'PASS',
  FAIL = 'FAIL',
}

@Entity()
export class Candidate extends CustomBaseEntity {
  @Column('date')
  applyDate: Date;
  
  @Column({ nullable: true})
  status: CandidateStatusEnum;
  
  @Column('date', { nullable: true})
  interview1Date: Date;
  
  @Column({ nullable: true})
  score1: number

  @Column({ nullable: true})
  result1: boolean;

  @Column({ nullable: true})
  comment1: string;
  
  @Column('date', { nullable: true})
  interview2Date: Date;
  
  @Column({ nullable: true})
  score2: number
  
  @Column({ nullable: true})
  result2: boolean;

  @Column({ nullable: true})
  comment2: string;

  @Column('date', { nullable: true})
  interview3Date: Date;

  @Column({ nullable: true})
  score3: number

  @Column({ nullable: true})
  result3: boolean;

  @Column({ nullable: true})
  comment3: string;

  @ManyToOne(() => Applicant, (_) => _.candidates, { onDelete: 'CASCADE' })
  applicant: Applicant;

  @ManyToOne(() => Job, (_) => _.candidates, { onDelete: 'CASCADE' })
  job: Job;

  @ManyToOne(() => Employee, (_) => _.candidates1)
  interviewer1: Employee

  @ManyToOne(() => Employee, (_) => _.candidates2)
  interviewer2: Employee

  @ManyToOne(() => Employee, (_) => _.candidates3)
  interviewer3: Employee
}
