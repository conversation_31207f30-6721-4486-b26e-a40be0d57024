import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCandidateDto } from './dto/create-candidate.dto';
import { UpdateCandidateDto } from './dto/update-candidate.dto';
import { ApplyCandidateDto } from './dto/apply-candidate.dto';
import { Job } from 'src/job/entities/job.entity';
import { Applicant } from 'src/applicant/entities/applicant.entity';
import { Candidate, CandidateStatusEnum } from './entities/candidate.entity';
import { FilterOperator, paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { SelectCandidateDto } from './dto/select-candidate.dto';
import { In } from 'typeorm';
import { MailerService } from '@nestjs-modules/mailer';

export const CANDIDATE_PAGINATION_CONFIG: PaginateConfig<Candidate> = {
  sortableColumns: ['id', 'applyDate', 'status'],
  searchableColumns: ['applicant.fullnameEn', 'applicant.fullnameTh', 'job.title'],
  relations: {
    applicant: true,
    job: true,
    interviewer1: true,
    interviewer2: true,
    interviewer3: true
  },
  filterableColumns: {
    'applicant.id': [FilterOperator.EQ],
    'applyDate': [FilterOperator.BTW],
    'status': true,
    'interviewer1.id': [FilterOperator.EQ],
    'interviewer2.id': [FilterOperator.EQ],
    'interviewer3.id': [FilterOperator.EQ],
    'result1': [FilterOperator.EQ],
    'result2': [FilterOperator.EQ],
    'result3': [FilterOperator.EQ],
    'job.id': [FilterOperator.EQ]
  }
};

@Injectable()
export class CandidateService {
  constructor(
    private readonly mailerService: MailerService,
  ) { }

  findAll() {
    return Candidate.find();
  }

  async findOne(id: number) {
    const candidate = await Candidate.findOne({
      where: {
        id,
      },
      relations: {
        job: {
          personalForm: true
        },
        applicant: {
          applicantFiles: true
        },
        interviewer1: true,
        interviewer2: true,
        interviewer3: true
      }
    });

    if (!candidate) {
      throw new NotFoundException('Candidate not found');
    }

    return candidate;
  }

  async update(id: number, updateCandidateDto: UpdateCandidateDto) {
    const candidate = await Candidate.findOne({
      where: {
        id,
      },
      // relations: {
      //   job: true,
      //   applicant: true,
      // }
    });

    if (!candidate) {
      throw new NotFoundException('Candidate not found');
    }

    const data = Candidate.create({
      ...updateCandidateDto
    })

    await Candidate.update(id, {
      ...candidate,
      ...data,
      job: {
        id: updateCandidateDto.jobId
      },
      applicant: {
        id: updateCandidateDto.applicantId
      },
      interviewer1: {
        id: updateCandidateDto.interviewer1Id
      },
      interviewer2: {
        id: updateCandidateDto.interviewer2Id
      },
      interviewer3: {
        id: updateCandidateDto.interviewer3Id
      },
    })

    return this.findOne(id);
  }

  async remove(id: number) {
    const candidate = await Candidate.findOne({
      where: { id: id }
    });

    if (!candidate) {
      throw new NotFoundException('Candidate not found');
    }

    await candidate.remove();
  }

  async applyNow(payload: ApplyCandidateDto) {
    const job = await Job.findOne({
      where: { id: payload.jobId },
      // relations: {
      //   personalForm: {
      //     requestBy: true
      //   }
      // }
    });
    if (!job) {
      throw new NotFoundException('Job not found');
    }

    const applicant = await Applicant.findOneBy({ id: payload.applicantId });
    if (!applicant) {
      throw new NotFoundException('Applicant not found');
    }

    //check if the application is already exist
    const existingCandidate = await Candidate.findOne({
      where: {
        job: { id: payload.jobId },
        applicant: { id: payload.applicantId }
      }
    });

    if (existingCandidate) {
      throw new NotFoundException('You have already applied for this job');
    }

    const candidate = Candidate.create({
      applyDate: new Date(),
      job,
      applicant,
    });

    await candidate.save();

    return { message: 'Application submitted successfully', data: candidate.id };
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Candidate>> {
    return paginate(query, Candidate.getRepository(), CANDIDATE_PAGINATION_CONFIG);
  }

  async selectCandidate(payload: SelectCandidateDto) {
    // Update status based on payload
    if (payload.status == CandidateStatusEnum.SELECTED) {
      await Candidate.update(
        { id: In(payload.candidateIds) },
        { status: CandidateStatusEnum.SELECTED }
      );

      const candidates = await Candidate.find({
        where: { id: In(payload.candidateIds) },
        relations: {
          interviewer1: true,
          interviewer2: true,
          interviewer3: true
        }
      });

      // Send email to requester
      // for (const candidate of candidates) {
      //   this.mailerService.sendMail({
      //     to: candidate.interviewer.email,
      //     subject: 'Candidate selected',
      //     template: 'candidate-selected',
      //     context: {},
      //   }).catch((emailErr) => {
      //     console.error('Failed to send email:', emailErr);
      //   });
      // }
    } else if (
      payload.status == CandidateStatusEnum.INTERVIEW1 ||
      payload.status == CandidateStatusEnum.INTERVIEW2 ||
      payload.status == CandidateStatusEnum.INTERVIEW3
    ) {
      await Candidate.update(
        { id: In(payload.candidateIds) },
        { status: payload.status }
      );

      const candidates = await Candidate.find({
        where: { id: In(payload.candidateIds) },
        relations: { applicant: true }
      });

      // Send email to applicants
      // for (const candidate of candidates) {
      //   this.mailerService.sendMail({
      //     to: candidate.applicant.email,
      //     subject: `Candidate ${payload.status.replace('INTERVIEW', 'Interview ')}`,
      //     template: 'candidate-interview',
      //     context: {},
      //   }).catch((emailErr) => {
      //     console.error('Failed to send email:', emailErr);
      //   });
      // }
    }
    return { message: 'Update successfully selected' };
  }

  myJob(applicantId: number) {
    return Candidate.find({
      where: { applicant: { id: applicantId } },
      relations: {
        job: true
      }
    });
  }
}
