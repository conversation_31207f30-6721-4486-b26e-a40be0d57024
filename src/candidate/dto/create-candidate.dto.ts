import { IsNotEmpty } from "class-validator";
import { CandidateStatusEnum } from "../entities/candidate.entity";

export class CreateCandidateDto {
  readonly applyDate: string;

  readonly status: CandidateStatusEnum;

  readonly interview1Date: string;

  readonly interviewer1Id: number;

  readonly score1: number;

  readonly result1: boolean;

  readonly comment1: string;

  readonly interview2Date: string;

  readonly interviewer2Id: number;

  readonly score2: number;

  readonly result2: boolean;

  readonly comment2: string;

  readonly interview3Date: string;

  readonly interviewer3Id: number;

  readonly score3: number;

  readonly result3: boolean;

  readonly comment3: string;

  readonly applicantId: number;

  readonly jobId: number;

  readonly interviewerId: number;
}
