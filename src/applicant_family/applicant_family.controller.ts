import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantFamilyService } from './applicant_family.service';
import { CreateApplicantFamilyDto } from './dto/create-applicant_family.dto';
import { UpdateApplicantFamilyDto } from './dto/update-applicant_family.dto';

@Controller('applicant-family')
export class ApplicantFamilyController {
  constructor(private readonly applicantFamilyService: ApplicantFamilyService) {}

  @Post()
  create(@Body() createApplicantFamilyDto: CreateApplicantFamilyDto) {
    return this.applicantFamilyService.create(createApplicantFamilyDto);
  }

  @Get()
  findAll() {
    return this.applicantFamilyService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantFamilyService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantFamilyDto: UpdateApplicantFamilyDto) {
    return this.applicantFamilyService.update(+id, updateApplicantFamilyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantFamilyService.remove(+id);
  }
}
