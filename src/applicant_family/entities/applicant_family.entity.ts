import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Applicant } from "src/applicant/entities/applicant.entity";

@Entity()
export class ApplicantFamily extends CustomBaseEntity {
    @Column("text", { nullable: true })
    name: string;

    @Column("text", { nullable: true })
    relationship: string;

    @Column('int', { nullable: true })
    age: number
    @Column("text", { nullable: true })
    occupation: string;

    @Column("text", { nullable: true })
    placeOfWork: string;

    @Column({ default: true })
    active: boolean

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantFamilies)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;

}

