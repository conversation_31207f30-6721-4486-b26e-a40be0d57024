import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantFamilyController } from './applicant_family.controller';
import { ApplicantFamilyService } from './applicant_family.service';

describe('ApplicantFamilyController', () => {
  let controller: ApplicantFamilyController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantFamilyController],
      providers: [ApplicantFamilyService],
    }).compile();

    controller = module.get<ApplicantFamilyController>(ApplicantFamilyController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
