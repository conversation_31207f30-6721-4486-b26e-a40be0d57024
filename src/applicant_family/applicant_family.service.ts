import { Injectable } from '@nestjs/common';
import { CreateApplicantFamilyDto } from './dto/create-applicant_family.dto';
import { UpdateApplicantFamilyDto } from './dto/update-applicant_family.dto';

@Injectable()
export class ApplicantFamilyService {
  create(createApplicantFamilyDto: CreateApplicantFamilyDto) {
    return 'This action adds a new applicantFamily';
  }

  findAll() {
    return `This action returns all applicantFamily`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantFamily`;
  }

  update(id: number, updateApplicantFamilyDto: UpdateApplicantFamilyDto) {
    return `This action updates a #${id} applicantFamily`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantFamily`;
  }
}
