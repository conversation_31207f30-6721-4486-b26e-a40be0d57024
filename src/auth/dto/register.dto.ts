import { IsNotEmpty, <PERSON><PERSON>eng<PERSON> } from "class-validator";
import { UserRole } from "src/user/entities/user.entity";

export class RegisterDto {
    @IsNotEmpty()
    @MinLength(3)
    readonly username: string;

    @IsNotEmpty()
    @MinLength(8)
    readonly password: string;

    @IsNotEmpty()
    readonly firstname: string;

    @IsNotEmpty()
    readonly lastname: string;

    @IsNotEmpty()
    readonly roles: UserRole[];

}