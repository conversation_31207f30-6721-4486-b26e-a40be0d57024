import { Body, Controller, Get, HttpCode, HttpStatus, NotFoundException, Post, Req, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Request } from 'express';
import { AccessTokenGuard, RefreshTokenGuard } from 'src/common/guards';
// import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { ApiBearerAuth, ApiBody, ApiExcludeEndpoint, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RegisterDto, AuthDto, ForgetDto, VerifyDto, AuthLdapDto, ChangePasswordDto } from './dto';
import { TokenDto } from './dto/token.dto';
// import { Auth } from './decorators/auth.decorator';
// import { LogoutDto } from './dto/logout.dto';

@Controller('auth')
@ApiTags('เข้าสู่ระบบ')
export class AuthController {
  constructor(private readonly authService: AuthService) { }

  // @Post('create-super-admin')
  // @ApiExcludeEndpoint()
  // async createSuperAdmin(@Req() req: Request, @Body() payload: any) {
  //   if (req.headers['x-api-key'] !== "ASHATECH") {
  //     throw new NotFoundException();
  //   }

  //   await this.authService.createSuperAdmin(payload);
  // }

  // @Post('/register')
  // @HttpCode(HttpStatus.OK)
  // async register(@Body() payload: RegisterDto) {
  //   return this.authService.register(payload);
  // }

  @Post('/login')
  @HttpCode(HttpStatus.OK)
  async signIn(@Body() payload: AuthDto) {
    return this.authService.signIn(payload);
  }

  @Post('/login-emp')
  // @UseGuards(AuthGuard('local'))
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ description: 'Login Employee' })
  async signInMember(@Body() payload: AuthLdapDto) {
    return this.authService.ldapLogin(payload.username, payload.password);
  }

  // @Post('/forget')
  // @HttpCode(HttpStatus.OK)
  // async forget(@Body() payload: ForgetDto) {
  //   return this.authService.forget(payload);
  // }

  @Post('sign-in-with-token')
  signInWithToken(@Body() body: TokenDto) {
    return this.authService.signInWithToken(body.accessToken);
  }

  @Post('/change-password')
  @ApiOperation({ summary: 'เปลี่ยนรหัสของ Role ที่เข้า Admin' })
  changePassword(@Req() req: Request, @Body() payload: ChangePasswordDto) {
    const userId = req.user['sub'];

    return this.authService.changePassword(userId, payload.oldPassword, payload.newPassword)
  }

  // @Post('/verify')
  // @HttpCode(HttpStatus.OK)
  // async verify(@Body() payload: VerifyDto) {
  //   return this.authService.verifyToken(payload.token);
  // }

  // @Auth()
  // @Post('logout')
  // logout(@Req() req: Request, @Body() body: LogoutDto) {
  //   return this.authService.logout(req.user['sub'], body.password);
  // }

  // @UseGuards(RefreshTokenGuard)
  // @Post('refresh')
  // refreshTokens(@Req() req: Request) {
  //   const userId = req.user['sub'];
  //   const refreshToken = req.user['refreshToken'];
  //   return this.authService.refreshTokens(userId, refreshToken);
  // }

  // @Post('sign-in-with-token')
  // signInWithToken(@Body() body: { accessToken: string }) {
  //   return this.authService.signInWithToken(body.accessToken);
  // }
}
