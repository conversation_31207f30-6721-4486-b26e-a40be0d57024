import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { AccessTokenStrategy, RefreshTokenStrategy } from './strategies';
import { CommandModule } from 'nestjs-command';
import { AuthCommand } from './auth.command';

@Module({
  imports: [JwtModule.register({}), CommandModule],
  controllers: [AuthController],
  providers: [AuthService, AccessTokenStrategy, RefreshTokenStrategy, AuthCommand],
})
export class AuthModule { }
