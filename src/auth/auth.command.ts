import { Command, Positional } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { AuthService } from './auth.service';

@Injectable()
export class AuthCommand {
  constructor(private readonly authService: AuthService) { }

  @Command({
    command: 'create:super-admin <username> <password> <firstname> <lastname>',
    describe: 'create a super admin',
  })
  async create(
    @Positional({ name: 'username', describe: 'the username', type: 'string' }) username: string,
    @Positional({ name: 'password', describe: 'the password', type: 'string' }) password: string,
    @Positional({ name: 'firstname', describe: 'the firstname', type: 'string' }) firstName: string,
    @Positional({ name: 'lastname', describe: 'the lastname', type: 'string' }) lastName: string,
  ) {
    await this.authService.createSuperAdmin({
      username: username,
      password: password,
      firstname: firstName,
      lastname: lastName,
    });
  }
}