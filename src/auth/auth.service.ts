import * as argon2 from 'argon2';

import { BadRequestException, ForbiddenException, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthDto } from './dto/auth.dto';
import { RegisterDto } from './dto/register.dto';
import { ForgetDto } from './dto/forget.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { User, UserRole } from 'src/user/entities/user.entity';
import { Employee, EmployeeActiveEnum } from 'src/employee/entities/employee.entity';
import { authenticate, AuthenticationOptions } from 'ldap-authentication';
import { Not } from 'typeorm';

@Injectable()
export class AuthService {
  constructor(
    // private usersService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private readonly mailerService: MailerService
  ) { }

  async register(payload: RegisterDto) {
    const checkUser = await User.findOne({
      where: { username: payload.username, }
    });

    if (checkUser) {
      throw new BadRequestException('username already registered')
    }

    const hash = await argon2.hash(payload.password);

    const user = User.create({
      username: payload.username,
      passwordHash: hash,
      firstname: payload.firstname,
      lastname: payload.lastname,
      roles: payload.roles
    })

    await user.save();

    const tokens = await this.getTokens(user.id, user.username, 'ADMIN');

    return {
      ...tokens,
    };
  }

  // async createSuperAdmin(createUserDto: CreateUserDto) {

  //   try {
  //     await this.usersService.create({
  //       code: createUserDto.code,
  //       username: createUserDto.username,
  //       password: createUserDto.password,
  //       firstName: createUserDto.firstName,
  //       lastName: createUserDto.lastName,
  //       phoneNumber: null,
  //       roleId: 1,
  //       branchIds: []
  //     });
  //   } catch (error) {
  //     throw new BadRequestException();
  //   }
  // }

  async signIn(authDto: AuthDto) {
    const user = await User.findOne({
      where: {
        username: authDto.username,
      }
    })

    if (!user) {
      throw new BadRequestException('email or password is not correct');
    }

    const passwordMatches = await argon2.verify(user.passwordHash, authDto.password);
    if (!passwordMatches) {
      throw new BadRequestException('username or password is not correct');
    }

    const tokens = await this.getTokens(user.id, user.username, 'ADMIN');

    return {
      ...tokens,
      user,
      roles: user.roles
    };
  }

  async signInMember(authDto: AuthDto) {
    const employee = await Employee.findOne({
      where: {
        username: authDto.username,
      },
      select: ['id', 'username', 'password']
    })

    if (!employee) {
      throw new BadRequestException('username or password is not correct');
    }

    const passwordMatches = await argon2.verify(employee.password, authDto.password);
    if (!passwordMatches) {
      throw new BadRequestException('username or password is not correct');
    }

    const tokens = await this.getTokens(employee.id, employee.username, 'EMP');

    return {
      ...tokens,
    };
  }

  async forget(payload: ForgetDto) {
    // TODO: Test Email
    const parent = {
      email: '<EMAIL>'
    }

    // File templates/forget.hbs
    await this.mailerService.sendMail({
      to: parent.email,
      subject: 'BCC Global Program Password Reset Request',
      template: 'forget',
      context: {
        resetUrl: process.env.WEB_URL + '/reset-password?token=' + 'token'
      },
    })

    return {
      status: 'ok',
      data: {
        email: parent.email
      }
    }
  }

  private async getTokens(userId: number, username: string, role: string) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync({ sub: userId, username, role }, { secret: this.configService.get('JWT_ACCESS_SECRET'), expiresIn: '1d' }),
      this.jwtService.signAsync({ sub: userId, username, role }, { secret: this.configService.get('JWT_REFRESH_SECRET'), expiresIn: '7d' })
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async verifyToken(token: string) {
    try {
      const decode = this.jwtService.verify(token, { secret: 'FORGET' });
      return { status: 'ok' }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async signInWithToken(accessToken: string) {
    const { username, role } = this.jwtService.decode(accessToken);

    if (role == 'ADMIN') {
      const user = await User.findOneBy({ username: username });
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return await this.getTokens(user.id, user.username, 'ADMIN');
    } else if (role == 'EMP') {
      const employee = await Employee.findOneBy({ username: username });
      if (!employee) {
        throw new UnauthorizedException('User not found');
      }

      return await this.getTokens(employee.id, employee.username, 'EMP');
    } else {
      throw new UnauthorizedException('Unsupported role')
    }
  }

  // async updateRefreshToken(userId: number, refreshToken: string) {
  //   const hash = await argon2.hash(refreshToken);
  //   await this.usersService.updateRefreshToken(userId, hash)
  // }

  // async logout(userId: number, password: string) {
  //   const user = await this.usersService.findOne(userId);
  //   if (!user) throw new NotFoundException('User not found.');

  //   const passwordMatches = await argon2.verify(user.password, password);
  //   if (!passwordMatches) throw new UnauthorizedException('username or password is not correct');

  //   await this.usersService.updateRefreshToken(userId, null)

  //   return true
  // }

  // async refreshTokens(userId: number, refreshToken: string) {
  //   const user = await this.usersService.findOne(userId);
  //   if (!user || !user.refreshToken)
  //     throw new ForbiddenException('Access Denied');
  //   const refreshTokenMatches = await argon2.verify(
  //     user.refreshToken,
  //     refreshToken,
  //   );
  //   if (!refreshTokenMatches) throw new ForbiddenException('Access Denied');
  //   const tokens = await this.getTokens(user.id, user.username, user.role.name);
  //   await this.updateRefreshToken(user.id, tokens.refreshToken);
  //   return tokens;
  // }

  // async signInWithToken(accessToken: string) {
  //   const data = this.jwtService.decode(accessToken);

  //   const user = await this.usersService.findByUsername(data.username);
  //   if (!user) throw new UnauthorizedException('user not found');

  //   const tokens = await this.getTokens(user.id, user.username, user.role.name);
  //   await this.updateRefreshToken(user.id, tokens.refreshToken);
  //   return tokens;
  // }

  async createSuperAdmin(payload: { username: string; password: string; firstname: string; lastname: string; }) {
    const checkUser = await User.findOne({
      where: { username: payload.username, }
    });

    if (checkUser) {
      throw new Error('username already registered')
    }

    const hash = await argon2.hash(payload.password);

    const user = User.create({
      username: payload.username,
      passwordHash: hash,
      firstname: payload.firstname,
      lastname: payload.lastname,
      roles: [UserRole.ADMIN]
    })

    await user.save();
  }

  async ldapLogin(username: string, password: string) {
    const user = await this.verifyLdapUser(username, password);

    const employee = await Employee.findOne({
      where: {
        username: user[process.env.LDAP_SEARCH],
        active: Not(EmployeeActiveEnum.RES)
      },
      loadRelationIds: true
    })

    if (!employee) {
      throw new BadRequestException('Can not find employee in system');
    }

    const tokens = await this.getTokens(employee.id, employee.username, 'EMP');

    return {
      ...tokens,
      employee,
      role: 'EMP'
    };
  }

  async changePassword(userId: any, oldPassword: string, newPassword: string) {
    const user = await User.findOne({
      where: {
        id: userId
      }
    });

    if (!user) {
      throw new BadRequestException('User not found.');
    }

    const passwordMatches = await argon2.verify(user.passwordHash, oldPassword);
    if (!passwordMatches) {
      throw new BadRequestException('old password is not correct');
    }

    const tokens = await this.getTokens(user.id, user.username, 'ADMIN');

    return {
      ...tokens,
      user,
      roles: user.roles
    };
  }

  private async verifyLdapUser(username: string, password: string) {
    const options = {
      ldapOpts: {
        url: 'ldap://' + process.env.LDAP_IP,
        // tlsOptions: { rejectUnauthorized: false }
      },
      adminDn: process.env.LDAP_DN,
      adminPassword: process.env.LDAP_PASSWORD,
      userSearchBase: process.env.LDAP_BASE,
      usernameAttribute: process.env.LDAP_SEARCH,
      username: username,
      userPassword: password,
      // starttls: false
    }

    try {
      const user = await authenticate(options)

      return user

    } catch (error) {
      throw new UnauthorizedException('username or password incorrect')
    }
  }
}
