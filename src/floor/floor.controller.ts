
import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';

import { FLOOR_PAGINATION_CONFIG } from './floor.service';
import { FloorService } from './floor.service';
import { CreateFloorDto } from './dto/create-floor.dto';
import { UpdateFloorDto } from './dto/update-floor.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';


@Controller('floor')
@ApiTags('ชั้น')
export class FloorController {
  constructor(private readonly floorService: FloorService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(FLOOR_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.floorService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createFloorDto: CreateFloorDto) {
    return this.floorService.create(createFloorDto);
  }

  @Get()
  findAll() {
    return this.floorService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.floorService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateFloorDto: UpdateFloorDto) {
    return this.floorService.update(+id, updateFloorDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.floorService.remove(+id);
  }
}



