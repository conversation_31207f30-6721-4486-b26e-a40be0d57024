import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateFloorDto } from './dto/create-floor.dto';
import { UpdateFloorDto } from './dto/update-floor.dto';
import { Floor } from './entities/floor.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const FLOOR_PAGINATION_CONFIG: PaginateConfig<Floor> = {
  sortableColumns: ['id', 'code', 'name', 'pic', 'active'],
  select: ['id', 'code', 'name', 'pic', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class FloorService {
  constructor(
    @InjectRepository(Floor)
    private floorRepository: Repository<Floor>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Floor>> {
    return paginate(query, this.floorRepository, FLOOR_PAGINATION_CONFIG);
  }

  async create(createFloorDto: CreateFloorDto) {

    const { ...data } = createFloorDto;

    //check floor code exist
    const check = await Floor.existsBy({
      code: createFloorDto?.code
    })
    if (check) {
      throw new BadRequestException('floor code already.')
    }

    const item = this.floorRepository.create(
      {
        ...data,
      });

    return this.floorRepository.save(item);
  }

  findAll() {
    return this.floorRepository.find();
  }

  async findOne(id: number) {
    const item = await this.floorRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("floor not found");

    return item;
  }

  async update(id: number, updateFloorDto: UpdateFloorDto) {
    //check floor code exist
    const check = await Floor.existsBy({
      code: updateFloorDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Floor code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("floor not found");

    const { ...data } = updateFloorDto;

    return this.floorRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("floor not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.floorRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.floorRepository.findOne({ where: { id } });
  }
}




