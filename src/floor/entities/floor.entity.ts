import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Ot } from "src/ot/entities/ot.entity";
import { OtAir } from "src/ot-air/entities/ot-air.entity";

@Entity()
@Unique(['code'])
export class Floor extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column('text', { nullable: true })
    pic: string

    @Column({ default: true })
    active: boolean

    //
    //
    @OneToMany(() => OtAir, (_) => _.floor)
    otAirs: Array<OtAir>;


    @AfterLoad()
    @AfterInsert()
    @AfterUpdate()
    getPic() {
        this.pic = this.pic ? `${process.env.APP_URL}/${this.pic}` : null
    }

}






