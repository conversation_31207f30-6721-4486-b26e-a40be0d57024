import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { CreateLeavePermissionDto } from "src/leave-permission/dto/create-leave-permission.dto";

export class CreateLevelTypeDto  {
  @IsNotEmpty()
  @ApiProperty({ example: '03' })
  readonly code: string;

  @IsNotEmpty()
  @ApiProperty({ example: 'test' })
  readonly name: string;

  @ApiProperty({
    example: [
      { leaveTypeId: 1, ageWork: 1, qtyDay: 5, active: true },
      { leaveTypeId: 2, ageWork: 1, qtyDay: 5, active: true },
      { leaveTypeId: 3, ageWork: 1, qtyDay: 5, active: true },
    ]
  })
  readonly leavePermissions: CreateLeavePermissionDto[]
}


