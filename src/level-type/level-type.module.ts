import { Module } from '@nestjs/common';
import { LevelTypeService } from './level-type.service';
import { LevelTypeController } from './level-type.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LevelType } from './entities/level-type.entity';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LevelType,LeavePermission,LeaveType])],
  controllers: [LevelTypeController],
  providers: [LevelTypeService],
})
export class LevelTypeModule { }
