import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Req } from '@nestjs/common';
import { LEVEL_TYPE_PAGINATION_CONFIG, LevelTypeService } from './level-type.service';
import { CreateLevelTypeDto } from './dto/create-level-type.dto';
import { UpdateLevelTypeDto } from './dto/update-level-type.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('level-type')
@ApiTags('ประเภทระดับตำแหน่ง')
// @Auth()

export class LevelTypeController {
  constructor(private readonly levelTypeService: LevelTypeService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(LEVEL_TYPE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.levelTypeService.datatables(query);
  }

  @Post()
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       code: { type: 'string', example: '03' },
  //       name: { type: 'string', example: 'test' },
  //       leavePermissions: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             leaveTypeId: { type: 'number', example: 1 },
  //             ageWork: { type: 'number', example: 1 },
  //             qtyDay: { type: 'number', example: 5 },
  //             active: { type: 'boolean', example: true },
  //           },
  //         },
  //         example: [
  //           { leaveTypeId: 1, ageWork: 1, qtyDay: 5, active: true },
  //           { leaveTypeId: 2, ageWork: 1, qtyDay: 5, active: true },
  //           { leaveTypeId: 3, ageWork: 1, qtyDay: 5, active: true },
  //         ],
  //         description: 'List of leave permissions',
  //       },
  //     },
  //   },
  // })
  @Roles(AuthRole.Admin)
  async create(@Body() createLevelTypeDto: CreateLevelTypeDto) {
    return this.levelTypeService.create(createLevelTypeDto);
  }

  @Get()
  findAll() {
    return this.levelTypeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.levelTypeService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateLevelTypeDto: UpdateLevelTypeDto) {
    return this.levelTypeService.update(+id, updateLevelTypeDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.levelTypeService.remove(+id);
  }
}


