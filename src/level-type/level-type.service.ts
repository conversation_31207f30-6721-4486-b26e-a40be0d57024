import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateLevelTypeDto } from './dto/create-level-type.dto';
import { UpdateLevelTypeDto } from './dto/update-level-type.dto';
import { LevelType } from './entities/level-type.entity';
import { DataSource, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { Level } from 'src/level/entities/level.entity';

export const LEVEL_TYPE_PAGINATION_CONFIG: PaginateConfig<LevelType> = {
  relations: ['leavePermissions.leaveType', 'levels'],
  sortableColumns: ['id', 'code', 'name', 'active', 'leavePermissions.id', 'leavePermissions.ageWork',
    'leavePermissions.qtyDay', 'leavePermissions.leaveType.id', 'leavePermissions.leaveType.code',
    'leavePermissions.leaveType.name'],
  select: ['id', 'code', 'name', 'active', 'createdAt', 'leavePermissions.id',
    'leavePermissions.ageWork', 'leavePermissions.qtyDay', 'leavePermissions.leaveType.id',
    'leavePermissions.leaveType.code', 'leavePermissions.leaveType.name'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class LevelTypeService {
  constructor(
    @InjectRepository(LevelType)
    private levelTypeRepository: Repository<LevelType>,

    @InjectRepository(LeavePermission)
    private leavePermissionRepository: Repository<LeavePermission>,

    @InjectRepository(LeaveType)
    private leaveTypeRepository: Repository<LeaveType>,

    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<LevelType>> {
    return paginate(query, this.levelTypeRepository, LEVEL_TYPE_PAGINATION_CONFIG);
  }

  async create(createLevelTypeDto: CreateLevelTypeDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { ...data } = createLevelTypeDto;

      //check level type code exist
      const check = await LevelType.existsBy({
        code: createLevelTypeDto?.code
      })
      if (check) {
        throw new BadRequestException('level type code already.')
      }

      const item = this.levelTypeRepository.create(
        {
          ...data,
        });

      await queryRunner.manager.save(item)

      //add work shift time
      for (let i = 0; i < createLevelTypeDto.leavePermissions.length; i++) {

        const leaveTypeId = createLevelTypeDto.leavePermissions[i].leaveTypeId

        //check leaveType
        const leaveType = await this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } })

        if (!leaveType) {
          throw new NotFoundException("leave type not found");
        }

        //add leavePermission
        const leavePermission = new LeavePermission();

        leavePermission.levelType = item
        leavePermission.leaveType = leaveType
        leavePermission.ageWork = createLevelTypeDto.leavePermissions[i].ageWork;
        leavePermission.qtyDay = createLevelTypeDto.leavePermissions[i].qtyDay;
        leavePermission.active = createLevelTypeDto.leavePermissions[i].active;

        await queryRunner.manager.save(leavePermission)
      }

      await queryRunner.commitTransaction();

      return item

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }

  findAll() {

    return this.levelTypeRepository.find({
      relations: ['leavePermissions.leaveType', 'levels'],
      // where: {
      //   employee: {
      //     id: employeeId ? employeeId : null,
      //   }
      // }
    });
  }

  async findOne(id: number) {
    const item = await this.levelTypeRepository.findOne({
      relations: ['leavePermissions.leaveType', 'levels'],
      where: { id }
    });

    if (!item) throw new NotFoundException("level type not found");

    return item;
  }

  async update(id: number, updateLevelTypeDto: UpdateLevelTypeDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      //check level type code exist
      const check = await LevelType.existsBy({
        code: updateLevelTypeDto?.code,
        id: Not(id)
      })
      if (check) {
        throw new BadRequestException('Level type code already.')
      }

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("work shift not found");

      item.code = updateLevelTypeDto.code
      item.name = updateLevelTypeDto.name
      item.active = updateLevelTypeDto.active

      await queryRunner.manager.save(item)

      // update leave permission
      if (updateLevelTypeDto.leavePermissions.length > 0) {

        //
        const getLeavePermission = await this.leavePermissionRepository.find({
          relations: ['levelType'],
          where: {
            levelType: {
              id: id
            }
          },
        });

        if (!getLeavePermission) {
          throw new BadRequestException(`leave Permission is not found`)
        }

        //del
        getLeavePermission.forEach(async (item) => {
          const deleteResponse = await this.leavePermissionRepository.softDelete(item.id);
        });

        //add
        for (let i = 0; i < updateLevelTypeDto.leavePermissions.length; i++) {

          const leaveTypeId = updateLevelTypeDto.leavePermissions[i].leaveTypeId

          //check leaveType
          const leaveType = await this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } })

          if (!leaveType) {
            throw new NotFoundException("leave type not found");
          }


          const leavePermission = new LeavePermission();

          leavePermission.levelType = item
          leavePermission.leaveType = leaveType
          leavePermission.ageWork = updateLevelTypeDto.leavePermissions[i].ageWork;
          leavePermission.qtyDay = updateLevelTypeDto.leavePermissions[i].qtyDay;
          leavePermission.active = updateLevelTypeDto.leavePermissions[i].active;

          await queryRunner.manager.save(leavePermission)
        }
      }

      await queryRunner.commitTransaction();

      return item

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("level type not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.levelTypeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.levelTypeRepository.findOne({ where: { id } });
  }
}


