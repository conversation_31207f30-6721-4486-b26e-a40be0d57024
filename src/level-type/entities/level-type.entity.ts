import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "../../employee/entities/employee.entity";
import { Leave } from "../../leave/entities/leave.entity";
import { Level } from "../../level/entities/level.entity";
import { LeavePermission } from "../../leave-permission/entities/leave-permission.entity";
import { EmployeeLeavePermission } from "../../employee-leave-permission/entities/employee-leave-permission.entity";


@Entity()
@Unique(['code'])
export class LevelType extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //Level
    @OneToMany(() => Level, (_) => _.levelType)
    levels: Array<Level>;

    //LeavePermission
    @OneToMany(() => LeavePermission, (_) => _.levelType)
    leavePermissions: Array<LeavePermission>;

    @OneToMany(() => EmployeeLeavePermission, (_) => _.levelType)
    employeeLeavePermissions: EmployeeLeavePermission[];
}



