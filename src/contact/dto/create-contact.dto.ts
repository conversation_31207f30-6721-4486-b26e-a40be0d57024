
import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, ValidateIf } from "class-validator";
import { ContactActivity } from "src/contact_activity/entities/contact_activity.entity";
import { ContactType } from "../entities/contact.entity";
export class CreateContactDto {
    // @IsNotEmpty()
    // @ApiProperty()
    // readonly code: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly firstname: string;

    @ApiProperty()
    @IsNotEmpty()
    readonly lastname: string;

    @ApiProperty()
    readonly position: string;

    @ApiProperty()
    readonly email: string;

    @ApiProperty()
    readonly mobileNumber: string;

    @ApiProperty()
    readonly building: string;

    @ApiProperty()
    readonly street: string;

    @ApiProperty()
    readonly district: string;

    @ApiProperty()
    readonly city: string;

    @ApiProperty()
    readonly postCode: string;

    @ApiProperty()
    readonly country: string;


    @ApiProperty()
    // @IsNotEmpty()
    readonly categoryIds: number[];

    @ApiProperty()
    readonly type: ContactType;

    @ApiProperty()
    @ValidateIf((o) => o.type === ContactType.BUSINESS)
    @IsNotEmpty({ message: 'companyId is required when type is individual' })
    readonly companyId: number;

    @ApiProperty()
    readonly employeeActivity: EmployeeActivityDto[];
}

export class EmployeeActivityDto {
    @ApiProperty()
    readonly employeeId: number;

    @ApiProperty()
    readonly activityIds: number[];
}





