import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query } from '@nestjs/common';

import { CONTACT_PAGINATION_CONFIG } from './contact.service';
import { ContactService } from './contact.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContact1Dto } from './dto/update-contact.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('contact')
@ApiTags('ผู้ติดต่อ')
@Auth()
export class ContactController {
  constructor(private readonly contactService: ContactService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CONTACT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.contactService.datatables(query);
  }

  @Post()
  // @Roles(AuthRole.Admin)
  create(@Body() createContactDto: CreateContactDto) {
    return this.contactService.create(createContactDto);
  }

  @Get()
  @ApiQuery({ name: 'companyId', required: false })
  @ApiQuery({ name: 'activityId', required: false })
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'categoryId', required: false, description: '1,2,3,4,5' })
  @ApiQuery({ name: 'activityId', required: false, description: '1,2,3,4,5' })
  findAll(@Query() query) {
    return this.contactService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.contactService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateContactDto: UpdateContact1Dto) {
    return this.contactService.update(+id, updateContactDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.contactService.remove(+id);
  }
}




