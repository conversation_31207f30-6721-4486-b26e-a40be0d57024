import { Modu<PERSON> } from '@nestjs/common';
import { ContactService } from './contact.service';
import { ContactController } from './contact.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Contact } from './entities/contact.entity';
import { Activity } from 'src/activity/entities/activity.entity';
import { ContactActivity } from 'src/contact_activity/entities/contact_activity.entity';
import { Company } from 'src/company/entities/company.entity';
import { Employee } from 'src/employee/entities/employee.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Contact, Activity, ContactActivity,Company,Employee])],
  controllers: [ContactController],
  providers: [ContactService],
})
export class ContactModule {}
