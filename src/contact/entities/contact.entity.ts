import { <PERSON>umn, <PERSON><PERSON><PERSON>, Index, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { CompanyCategory } from "src/company_category/entities/company_category.entity";
import { Employee } from "src/employee/entities/employee.entity";
import { Company } from "src/company/entities/company.entity";
import { ContactActivity } from "src/contact_activity/entities/contact_activity.entity";
import { Category } from "src/category/entities/category.entity";
import { ContactCategory } from "src/company_category/entities/contact_category.entity";

export enum ContactType{
    INDIVIDUAL = 'individual',
    BUSINESS = 'business',
}

@Entity()
// @Unique(['code'])
export class Contact extends CustomBaseEntity {
    @Index()
    @Column()
    code: string;

    @Column("text")
    firstname: string;

    @Column("text", { nullable: true })
    lastname: string;

    @Column("text", { nullable: true })
    position: string;

    @Column("text", { nullable: true })
    email: string;

    @Column("text", { nullable: true })
    mobileNumber: string;

    @Column("text", { nullable: true })
    building: string;

    @Column("text", { nullable: true })
    street: string;

    @Column("text", { nullable: true })
    district: string;

    @Column("text", { nullable: true })
    city: string;

    @Column("text", { nullable: true })
    postCode: string;

    @Column("text", { nullable: true })
    country: string;

    @Column({ default: true })
    active: boolean

    @Column('enum', { enum: ContactType, nullable: true })
    type: ContactType;

    //Company
    @ManyToOne(() => Company, (_) => _.contacts)
    @JoinColumn({ name: 'company_id' })
    company: Company;

    @OneToMany(() => ContactCategory, (_) => _.contact)
    contactCategories: ContactCategory[];

    //employee
    // @ManyToOne(() => Employee, (_) => _.contacts)
    // @JoinColumn({ name: 'employee_id' })
    // employee: Employee;

    @OneToMany(() => ContactActivity, (_) => _.contact)
    contactActivities: Array<ContactActivity>;

}













