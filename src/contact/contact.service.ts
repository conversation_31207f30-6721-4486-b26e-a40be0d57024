import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContact1Dto } from './dto/update-contact.dto';
import { Contact, ContactType } from './entities/contact.entity';
import { DataSource, In, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Activity } from 'src/activity/entities/activity.entity';
import { ContactActivity } from 'src/contact_activity/entities/contact_activity.entity';
import { Helper } from 'src/common/utils/helper';
import { Company } from 'src/company/entities/company.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { Category } from 'src/category/entities/category.entity';
import { ContactCategory } from 'src/company_category/entities/contact_category.entity';

export const CONTACT_PAGINATION_CONFIG: PaginateConfig<Contact> = {
  relations: {
    company: true,
    contactActivities: {
      employee: true,
      activities: true
    }
  },
  sortableColumns: ['id', 'code', 'firstname', 'lastname', 'position', 'email', 'mobileNumber', 'building', 'street', 'district', 'district', 'city', 'postCode', 'country', 'active'],
  // select: ['id', 'code', 'firstname', 'lastname', 'position', 'email', 'mobileNumber', 'active', 'createdAt'
  //   , 'company.id', 'company.code', 'company.name', 'company.building', 'company.street', 'company.district', 'company.city', 'company.postCode', 'company.country', 'company.phoneNumber', 'company.faxNumber', 'company.email', 'company.webSite', 'company.active', 'company.createdAt'
  //   , 'employee.id',
  //   'employee.code',
  //   'employee.firstname',
  //   'employee.lastname',
  // ],
  searchableColumns: ['id', 'code', 'firstname', 'lastname', 'position', 'email', 'mobileNumber', 'building', 'street', 'district', 'district', 'city', 'postCode', 'country', 'active'],
  filterableColumns: {
    status: [FilterOperator.EQ],
    'contactActivities.employee.id': [FilterOperator.EQ],
    'company.id': [FilterOperator.EQ],
    'contactActivities.activity.id': [FilterOperator.EQ]
  },
};


@Injectable()
export class ContactService {
  constructor(
    @InjectRepository(Contact)
    private contactRepository: Repository<Contact>,

    @InjectRepository(Activity)
    private activityRepository: Repository<Activity>,

    @InjectRepository(ContactActivity)
    private contactActivityRepository: Repository<ContactActivity>,

    @InjectRepository(Company)
    private companyRepository: Repository<Company>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,

    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Contact>> {
    return paginate(query, this.contactRepository, CONTACT_PAGINATION_CONFIG);
  }

  async create(createContactDto: CreateContactDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { companyId } = createContactDto;

      const [company] = await Promise.all([
        this.companyRepository.findOne({ where: { id: companyId } }),
        // this.employeeRepository.findOne({ where: { id: employeeId } }),
      ]);

      if (!company) throw new NotFoundException("company not found");

      //get last id
      const last = await Contact.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = last.length;
      const Code = Helper.generateNo('C', lastId).toString();
      //

      const contact = createContactDto.type == ContactType.BUSINESS
        ? Contact.create({
          ...createContactDto,
          code: Code,
          company: company
        }) :
        Contact.create({
          ...createContactDto,
          code: Code,
        })

      await queryRunner.manager.save(contact)

      const categories = await Category.find({
        where: {
          id: In(createContactDto.categoryIds)
        }
      })

      const newContactCategories = []
      for (const category of categories) {
        const contactCategories = ContactCategory.create({
          contact: contact,
          category: category,
          active: true
        })

        newContactCategories.push(contactCategories)
      }

      await queryRunner.manager.save(ContactCategory, newContactCategories)

      //add contactActivity
      const newContactActivity = [];
      for (let i = 0; i < createContactDto.employeeActivity.length; i++) {

        const employeeActivity = createContactDto.employeeActivity[i];

        const activities = await this.activityRepository.find({
          where: {
            id: In(employeeActivity.activityIds),
          }
        });

        const contactActivity = ContactActivity.create({
          contact: { id: contact.id },
          employee: { id: employeeActivity.employeeId },
          activities: activities,
        });

        newContactActivity.push(contactActivity);
      }

      await queryRunner.manager.save(ContactActivity, newContactActivity);


      await queryRunner.commitTransaction();
      return contact;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll(query) {

    const employeeId = query.employeeId;
    const companyId = query.companyId;
  
    const type = query.type;
    const categoryIdArray = query.categoryId ? query.categoryId.split(',').map(id => parseInt(id.trim())) : [];
    const activityIdArray = query.activityId ? query.activityId.split(',').map(id => parseInt(id.trim())) : [];


    return this.contactRepository.find({
      relations: {
        company: true,
        contactActivities: {
          employee: true,
          activities: true
        }
      },
      where: {
        active: true,
        type: type ? type : null,
        contactCategories: {
          category: {
            id: categoryIdArray.length > 0 ? In(categoryIdArray) : null,
          },
        },
        contactActivities: {
          employee: {
            id: employeeId ? employeeId : null,
          },
          activities: {
            id: activityIdArray.length > 0 ? In(activityIdArray) : null,
          }
        },
        company: {
          id: companyId ? companyId : null,
        },
      },
    });
  }

  async findOne(id: number) {
    const item = await this.contactRepository.findOne({
      relations: {
        company: true,
        contactActivities: {
          employee: true,
          activities: true
        },
        contactCategories: {
          category: true
        }
      },
      where: {
        id: id
      }
    });

    if (!item) throw new NotFoundException("contact not found");

    return item;
  }

  async update(id: number, updateContactDto: UpdateContact1Dto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { companyId } = updateContactDto;

      const [company] = await Promise.all([
        this.companyRepository.findOne({ where: { id: companyId } }),
        // this.employeeRepository.findOne({ where: { id: employeeId } }),
      ]);

      if (!company) throw new NotFoundException("company not found");
      // if (!employee) throw new NotFoundException("employee not found");

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("contact not found");

      item.firstname = updateContactDto.firstname;
      item.lastname = updateContactDto.lastname;
      item.position = updateContactDto.position;
      item.email = updateContactDto.email;
      item.mobileNumber = updateContactDto.mobileNumber;

      item.building = updateContactDto.building;
      item.street = updateContactDto.street;
      item.district = updateContactDto.district;
      item.city = updateContactDto.city;
      item.postCode = updateContactDto.postCode;
      item.country = updateContactDto.country;

      item.type = updateContactDto.type;
      item.active = updateContactDto.active;

      item.company = company;

      // item.employee = employee;
      await queryRunner.manager.save(item)

      await queryRunner.manager.delete(ContactCategory, {
        contact: {
          id: id
        }
      });

      const categories = await Category.find({
        where: {
          id: In(updateContactDto.categoryIds)
        }
      })

      const newContactCategories = []
      for (const category of categories) {
        const contactCategories = ContactCategory.create({
          contact: { id: item.id },
          category: category,
          active: true
        })

        newContactCategories.push(contactCategories)
      }

      await queryRunner.manager.save(newContactCategories)

      //dell
      await this.contactActivityRepository.delete({
        contact: {
          id: id
        }
      });

      //add contactActivity
      const newContactActivity = [];
      for (let i = 0; i < updateContactDto.employeeActivity.length; i++) {

        const employeeActivity = updateContactDto.employeeActivity[i];

        const activities = await this.activityRepository.find({
          where: {
            id: In(employeeActivity.activityIds),
          }
        });

        const contactActivity = ContactActivity.create({
          contact: { id: id },
          employee: { id: employeeActivity.employeeId },
          activities: activities,
        });

        newContactActivity.push(contactActivity);
      }

      await queryRunner.manager.save(newContactActivity);

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("contact not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.contactRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.contactRepository.findOne({
      relations: {
        company: true,
        contactActivities: {
          employee: true,
          activities: true
        }
      },
      where: { id }
    });
  }
}




