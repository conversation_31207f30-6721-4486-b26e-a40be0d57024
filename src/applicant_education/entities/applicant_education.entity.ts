
import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Applicant } from "src/applicant/entities/applicant.entity";

@Entity()
export class ApplicantEducation extends CustomBaseEntity {

    @Column({
        type: "enum",
        enum: ["primary", "pre_university", "university"],
        default: 'primary'
    })
    level: string

    @Column("text", { nullable: true })
    name: string;

    @Column("text", { nullable: true })
    from: string;

    @Column("text", { nullable: true })
    to: string;

    @Column("text", { nullable: true })
    major: string;

    @Column("text", { nullable: true })
    degree: string;

    @Column({ default: true })
    active: boolean

    //applicant
    @ManyToOne(() => Applicant, (_) => _.applicantEducations)
    @JoinColumn({ name: 'applicant_id' })
    applicant: Applicant;

}


