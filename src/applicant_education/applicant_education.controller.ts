import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantEducationService } from './applicant_education.service';
import { CreateApplicantEducationDto } from './dto/create-applicant_education.dto';
import { UpdateApplicantEducationDto } from './dto/update-applicant_education.dto';

@Controller('applicant-education')
export class ApplicantEducationController {
  constructor(private readonly applicantEducationService: ApplicantEducationService) {}

  @Post()
  create(@Body() createApplicantEducationDto: CreateApplicantEducationDto) {
    return this.applicantEducationService.create(createApplicantEducationDto);
  }

  @Get()
  findAll() {
    return this.applicantEducationService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantEducationService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantEducationDto: UpdateApplicantEducationDto) {
    return this.applicantEducationService.update(+id, updateApplicantEducationDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantEducationService.remove(+id);
  }
}
