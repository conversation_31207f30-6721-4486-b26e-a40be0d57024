import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantEducationController } from './applicant_education.controller';
import { ApplicantEducationService } from './applicant_education.service';

describe('ApplicantEducationController', () => {
  let controller: ApplicantEducationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantEducationController],
      providers: [ApplicantEducationService],
    }).compile();

    controller = module.get<ApplicantEducationController>(ApplicantEducationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
