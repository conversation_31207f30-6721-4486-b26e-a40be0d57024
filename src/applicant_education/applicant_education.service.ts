import { Injectable } from '@nestjs/common';
import { CreateApplicantEducationDto } from './dto/create-applicant_education.dto';
import { UpdateApplicantEducationDto } from './dto/update-applicant_education.dto';

@Injectable()
export class ApplicantEducationService {
  create(createApplicantEducationDto: CreateApplicantEducationDto) {
    return 'This action adds a new applicantEducation';
  }

  findAll() {
    return `This action returns all applicantEducation`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantEducation`;
  }

  update(id: number, updateApplicantEducationDto: UpdateApplicantEducationDto) {
    return `This action updates a #${id} applicantEducation`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantEducation`;
  }
}
