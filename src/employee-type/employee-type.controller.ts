
import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';

import { EMPLOYEE_TYPE_PAGINATION_CONFIG, EmployeeTypeService } from './employee-type.service';
import { CreateEmployeeTypeDto } from './dto/create-employee-type.dto';
import { UpdateEmployeeTypeDto } from './dto/update-employee-type.dto';

import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('employee-type')
@ApiTags('ประเภทพนักงาน')
// @Auth()

export class EmployeeTypeController {
  constructor(private readonly employeeTypeService: EmployeeTypeService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(EMPLOYEE_TYPE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.employeeTypeService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createEmployeeTypeDto: CreateEmployeeTypeDto) {
    return this.employeeTypeService.create(createEmployeeTypeDto);
  }

  @Get()
  findAll() {
    return this.employeeTypeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.employeeTypeService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateEmployeeTypeDto: UpdateEmployeeTypeDto) {
    return this.employeeTypeService.update(+id, updateEmployeeTypeDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.employeeTypeService.remove(+id);
  }
}

