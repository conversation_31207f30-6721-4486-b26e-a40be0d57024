import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateEmployeeTypeDto } from './dto/create-employee-type.dto';
import { UpdateEmployeeTypeDto } from './dto/update-employee-type.dto';
import { EmployeeType } from './entities/employee-type.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const EMPLOYEE_TYPE_PAGINATION_CONFIG: PaginateConfig<EmployeeType> = {
  sortableColumns: ['id', 'code', 'name','active'],
  select: ['id', 'code', 'name','active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class EmployeeTypeService {
  constructor(
    @InjectRepository(EmployeeType)
    private employeeTypeRepository: Repository<EmployeeType>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<EmployeeType>> {
    return paginate(query, this.employeeTypeRepository, EMPLOYEE_TYPE_PAGINATION_CONFIG);
  }

  async create(createEmployeeTypeDto: CreateEmployeeTypeDto) {

    const { ...data } = createEmployeeTypeDto;

    //check employee type code exist
    const check = await EmployeeType.existsBy({
      code: createEmployeeTypeDto?.code
    })
    if (check) {
      throw new BadRequestException('employee type code already.')
    }

    const item = this.employeeTypeRepository.create(
      {
        ...data,
      });

    return this.employeeTypeRepository.save(item);
  }

  findAll() {
    return this.employeeTypeRepository.find();
  }

  async findOne(id: number) {
    const item = await this.employeeTypeRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("employee type not found");

    return item;
  }

  async update(id: number, updateEmployeeTypeDto: UpdateEmployeeTypeDto) {
    //check employee type code exist
    const check = await EmployeeType.existsBy({
      code: updateEmployeeTypeDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Employee type code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("employee type not found");

    const { ...data } = updateEmployeeTypeDto;

    return this.employeeTypeRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("employee type not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.employeeTypeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.employeeTypeRepository.findOne({ where: { id } });
  }
}

