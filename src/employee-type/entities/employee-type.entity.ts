import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "../../employee/entities/employee.entity";
import { Holiday } from "../../holiday/entities/holiday.entity";


@Entity()
@Unique(['code'])
export class EmployeeType extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //employee
    @OneToMany(() => Employee, (_) => _.employeeType)
    employees: Array<Employee>;

    @OneToMany(() => Holiday, (_) => _.employeeType)
    holidays: Holiday[];
}

