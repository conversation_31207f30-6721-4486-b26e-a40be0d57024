import { Module } from '@nestjs/common';
import { EmployeeTypeService } from './employee-type.service';
import { EmployeeTypeController } from './employee-type.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployeeType } from './entities/employee-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EmployeeType])],
  controllers: [EmployeeTypeController],
  providers: [EmployeeTypeService],
})
export class EmployeeTypeModule {}
