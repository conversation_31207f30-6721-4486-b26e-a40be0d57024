import { Employee } from "../../employee/entities/employee.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne } from "typeorm";

export enum HistoryAttandanceStatusEnum {
  PENDING = 'pending',
  APPROVE = 'approve',
}

@Entity()
export class HistoryAttandance extends CustomBaseEntity {
  @Column()
  year: number;

  @Column()
  month: number;

  @Column({ type: 'enum', enum: HistoryAttandanceStatusEnum, default: HistoryAttandanceStatusEnum.PENDING, nullable: true })
  confirmStatus: HistoryAttandanceStatusEnum

  @Column({ type: 'enum', enum: HistoryAttandanceStatusEnum, default: HistoryAttandanceStatusEnum.PENDING, nullable: true })
  headConfirmStatus: HistoryAttandanceStatusEnum

  @Column('int', { default: 0 })
  case1: number

  @Column('int', { default: 0 })
  case2: number

  @Column('int', { default: 0 })
  case3: number

  @Column('int', { default: 0 })
  case4: number

  @ManyToOne(() => Employee, (_) => _.historyAttandances, { onDelete: 'CASCADE'})
  employee: Employee;
}
