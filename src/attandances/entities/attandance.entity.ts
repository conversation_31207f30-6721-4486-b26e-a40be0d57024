import { Employee } from "../../employee/entities/employee.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne } from "typeorm";

export enum AttandanceStatusEnum {
  ABSENT = 'absent',
  PRESENT = 'present',
  LEAVE = 'leave',
}

export enum AttandanceConfirmStatusEnum {
  DRAFT = 'draft',
  CONFIRM = 'confirm',
  APPROVE = 'approve',
}

@Entity()
export class Attandance extends CustomBaseEntity {
  @Column({ type: 'date' })
  workDate: Date;

  @Column({ type: 'time', nullable: true })
  workStart: string;

  @Column({ type: 'time', nullable: true })
  workEnd: string;

  @Column({ type: 'time', nullable: true })
  checkIn: string;

  @Column({ type: 'time', nullable: true })
  checkOut: string;

  @Column({ nullable: true })
  inMode: string;

  @Column({ nullable: true })
  outMode: string;

  @Column('boolean')
  isWorkday: boolean;

  @Column('boolean')
  isHoliday: boolean;

  @Column({ type: 'float8', default: 0 })
  workedHours: number;

  @Column({ type: 'float8', default: 0 })
  overtimeHours: number;

  @Column({ type: 'float8', default: 0 })
  expectedHours: number;

  @Column({ type: 'enum', enum: AttandanceStatusEnum, nullable: true })
  status: AttandanceStatusEnum

  @Column({ type: 'enum', enum: AttandanceConfirmStatusEnum, nullable: true })
  confirmStatus: AttandanceConfirmStatusEnum

  @Column({ default: false })
  deduct: boolean

  @Column({ default: true })
  active: boolean

  @Column({ nullable: true })
  reason: string;

  @Column({ nullable: true })
  explanation: string;

  @Column('int', { default: 0 })
  case1: number

  @Column('int', { default: 0 })
  case2: number

  @Column('int', { default: 0 })
  case3: number

  @Column('int', { default: 0 })
  case4: number


  calc_hrs: number
  work_hrs: number
  column_leave_type: any

  @ManyToOne(() => Employee, (_) => _.attandances, { onDelete: 'CASCADE'})
  employee: Employee;
}
