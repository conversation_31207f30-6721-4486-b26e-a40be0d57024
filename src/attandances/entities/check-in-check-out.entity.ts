import { Employee } from "../../employee/entities/employee.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne } from "typeorm";

@Entity()
export class CheckInCheckOut extends CustomBaseEntity {
  @Column()
  accessDateTime: Date;

  @Column('date')
  date: Date;

  @Column('time')
  time: Date;

  @Column({ nullable: true })
  deviceName: string;

  @Column({ nullable: true })
  deviceSerial: string;

  @Column({ nullable: true })
  attachment: string;

  @Column({ type: 'float8', nullable: true })
  latitude: number;

  @Column({ type: 'float8', nullable: true })
  longitude: number;

  @ManyToOne(() => Employee, (_) => _.checkInCheckOuts)
  employee: Employee;
}

// CREATE TABLE "public"."timein_timeout" (
//   "device_name" varchar(50) COLLATE "pg_catalog"."default",
//   "device_serial" varchar(50) COLLATE "pg_catalog"."default",
//   "reader_name" varchar(50) COLLATE "pg_catalog"."default",
//   "firstname" varchar(50) COLLATE "pg_catalog"."default",
//   "lastname" varchar(50) COLLATE "pg_catalog"."default",
//   "person_name" varchar(50) COLLATE "pg_catalog"."default",
//   "person_group" varchar(50) COLLATE "pg_catalog"."default",
//   "cardnumber" varchar(50) COLLATE "pg_catalog"."default",
//   "direction" varchar(50) COLLATE "pg_catalog"."default",
//   "skin_temp" varchar(50) COLLATE "pg_catalog"."default",
//   "temp_status" varchar(50) COLLATE "pg_catalog"."default",
//   "mask_status" varchar(50) COLLATE "pg_catalog"."default",
//   "attendance_status" varchar(50) COLLATE "pg_catalog"."default",
//   "resource_name" varchar(50) COLLATE "pg_catalog"."default"
// )
