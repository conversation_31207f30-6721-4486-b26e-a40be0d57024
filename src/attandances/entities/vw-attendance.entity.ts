import { Employee } from "../../employee/entities/employee.entity";
import { BaseEntity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn, ViewColumn, ViewEntity } from "typeorm";

@ViewEntity({
  name: 'vw_attendance',
  expression: `
    SELECT employee.id,
      employee.code,
      employee.firstname,
      employee.lastname,
      "TimeIn_TimeOut"."Date" AS date,
      min("TimeIn_TimeOut"."Time") AS min,
      max("TimeIn_TimeOut"."Time") AS max,
      ROUND(EXTRACT(EPOCH FROM (MAX("Time") - MIN("Time"))) / 3600, 0) AS calhrs
    FROM employee
      INNER JOIN "TimeIn_TimeOut" ON employee.code::text = TRIM("TimeIn_TimeOut"."ID")
    GROUP BY employee.id, employee.code, employee.firstname, employee.lastname, "TimeIn_TimeOut"."Date"
    ORDER BY employee.code, "TimeIn_TimeOut"."Date"`,
  // synchronize: false
})
export class ViewAttendance extends BaseEntity {
  @ViewColumn()
  @PrimaryColumn()
  id: string;

  @ViewColumn()
  code: string;

  @ViewColumn()
  firstname: string;

  @ViewColumn()
  lastname: string;

  @ViewColumn()
  date: string;

  @ViewColumn()
  min: string;

  @ViewColumn()
  max: string;

  @ViewColumn()
  calhrs: number;

  @ManyToOne(() => Employee, (_) => _.vwAttendance)
  @JoinColumn({ name: 'id' })
  employee: Employee;
}