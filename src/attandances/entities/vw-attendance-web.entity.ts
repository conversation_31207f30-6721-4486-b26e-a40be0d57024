import { Employee } from "../../employee/entities/employee.entity";
import { BaseEntity, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn, ViewColumn, ViewEntity } from "typeorm";

@ViewEntity({
  name: 'vw_attendance_web',
  expression: `
    SELECT
      "c".*, 
      t_in.latitude AS in_lat, 
      t_in.longitude AS in_lon, 
      t_in."source" AS in_source, 
      t_in."image" AS in_image,
      t_out.latitude AS out_lat, 
      t_out.longitude AS out_lon, 
      t_out."source" AS out_source,
      t_out."image" AS out_image
    FROM
      vw_attendance AS "c"
      LEFT JOIN
      "TimeIn_TimeOut" AS t_in
      ON 
        "c".code = t_in."ID" AND
        "c"."date" = t_in."Date" AND
        "c"."min" = t_in."Time"
      LEFT JOIN
      "TimeIn_TimeOut" AS t_out
      ON 
        "c".code = t_out."ID" AND
        "c"."date" = t_out."Date" AND
        "c"."max" = t_out."Time"
      WHERE
        t_in.source = 'web' OR t_out.source = 'web'
    ORDER BY
      "code" ASC, 
      "date" ASC`,
  // synchronize: false
})
export class ViewAttendanceWeb extends BaseEntity {
  @ViewColumn()
  @PrimaryColumn()
  id: string;

  @ViewColumn()
  code: string;

  @ViewColumn()
  firstname: string;

  @ViewColumn()
  lastname: string;

  @ViewColumn()
  date: string;

  @ViewColumn()
  min: string;

  @ViewColumn()
  max: string;

  @ViewColumn()
  calhrs: number;

  @ViewColumn()
  inLat: number;
  
  @ViewColumn()
  inLon: number;

  @ViewColumn()
  inSource: string;

  @ViewColumn()
  inImage: string;

  @ViewColumn()
  outLat: number;
  
  @ViewColumn()
  outLon: number;

  @ViewColumn()
  outSource: string;

  @ViewColumn()
  outImage: string;

  @ManyToOne(() => Employee, (_) => _.vwAttendance)
  @JoinColumn({ name: 'id' })
  employee: Employee;
}