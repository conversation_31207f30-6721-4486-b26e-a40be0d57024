export class UpdateEmpAttendanceDto {
  id: number
  employeeId: number
  date: string
  workStart: string
  workEnd: string
  timeIn: string
  timeOut: string
  // day: string
  case1: number
  case2: number
  case3: number
  case4: number
  // status: string
  reason: string
  explanation: string
  workHrs: number
  otHrs: number
  calcHrs: number
  isWorkday: boolean
  isHoliday: boolean
  // confirmStatus: string
  // leaveCode: string
  // leaveName: string
  // leaveQty: number
  // color: string
  deduct: boolean
  // leaveType: LeaveTypeDto[]
}

// export class LeaveTypeDto {
//   name: string
//   useDay: number
// }