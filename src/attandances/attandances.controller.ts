import { Controller, Post, Body, Put, ParseIntPipe, Param } from '@nestjs/common';
import { AttandancesService } from './attandances.service';
import { PullAttandanceDto } from './dto/pull-attandance.dto';
import { CreateAttandanceDto } from './dto/create-attandance.dto';
import { ApiOperation } from '@nestjs/swagger';

@Controller('attandances')
export class AttandancesController {
  constructor(private readonly attandancesService: AttandancesService) { }

  @Post('/pull')
  pullAttendance(@Body() payload: PullAttandanceDto) {
    return this.attandancesService.pullAttendance(payload.year, payload.month,)
  }

  @Put('/:id')
  @ApiOperation({ summary: 'แก้ไข reason ของ attandance' })
  update(@Param('id', ParseIntPipe) id: string, @Body() updateAttendanceDto: CreateAttandanceDto) {
    return this.attandancesService.update(+id, updateAttendanceDto);
  }
}
