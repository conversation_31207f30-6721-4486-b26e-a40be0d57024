import { Test, TestingModule } from '@nestjs/testing';
import { AttandancesService } from './attandances.service';

describe('AttandancesService', () => {
  let service: AttandancesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AttandancesService],
    }).compile();

    service = module.get<AttandancesService>(AttandancesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
