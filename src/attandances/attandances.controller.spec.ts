import { Test, TestingModule } from '@nestjs/testing';
import { AttandancesController } from './attandances.controller';
import { AttandancesService } from './attandances.service';

describe('AttandancesController', () => {
  let controller: AttandancesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AttandancesController],
      providers: [AttandancesService],
    }).compile();

    controller = module.get<AttandancesController>(AttandancesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
