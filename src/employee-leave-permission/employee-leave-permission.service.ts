import { Injectable } from '@nestjs/common';
import { CreateEmployeeLeavePermissionDto } from './dto/create-employee-leave-permission.dto';
import { UpdateEmployeeLeavePermissionDto } from './dto/update-employee-leave-permission.dto';

@Injectable()
export class EmployeeLeavePermissionService {
  create(createEmployeeLeavePermissionDto: CreateEmployeeLeavePermissionDto) {
    return 'This action adds a new employeeLeavePermission';
  }

  findAll() {
    return `This action returns all employeeLeavePermission`;
  }

  findOne(id: number) {
    return `This action returns a #${id} employeeLeavePermission`;
  }

  update(id: number, updateEmployeeLeavePermissionDto: UpdateEmployeeLeavePermissionDto) {
    return `This action updates a #${id} employeeLeavePermission`;
  }

  remove(id: number) {
    return `This action removes a #${id} employeeLeavePermission`;
  }
}
