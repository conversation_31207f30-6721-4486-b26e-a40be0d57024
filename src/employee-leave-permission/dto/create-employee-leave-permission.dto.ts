import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateEmployeeLeavePermissionDto {

    @IsNotEmpty()
    @ApiProperty({ type: Date, format: 'date' })
    readonly startPeriod: string
    
    @IsNotEmpty()
    @ApiProperty({ type: Date, format: 'date' })
    readonly endPeriod: string

    @ApiProperty({ type: Date, format: 'date' })
    readonly expireDate: string

    readonly employeeLeavePermissions: EmployeeLeavePermissionDto[];
}

export class EmployeeLeavePermissionDto {

    @IsNotEmpty()
    readonly id: undefined | null | number;

    @IsNotEmpty()
    readonly qtyDay: number;
    

    @IsNotEmpty()
    readonly leaveTypeId: number;

    @IsNotEmpty()
    readonly usedDay: number;

    @IsNotEmpty()
    readonly excessDay: number;

    @ApiProperty({ type: Date, format: 'date' })
    readonly expireDate: string

    @ApiProperty({ type: Boolean, default: false })
    readonly toNextPeriod: boolean;
}
