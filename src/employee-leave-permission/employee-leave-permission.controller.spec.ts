import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeLeavePermissionController } from './employee-leave-permission.controller';
import { EmployeeLeavePermissionService } from './employee-leave-permission.service';

describe('EmployeeLeavePermissionController', () => {
  let controller: EmployeeLeavePermissionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmployeeLeavePermissionController],
      providers: [EmployeeLeavePermissionService],
    }).compile();

    controller = module.get<EmployeeLeavePermissionController>(EmployeeLeavePermissionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
