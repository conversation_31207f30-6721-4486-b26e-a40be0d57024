import { Employee } from "../../employee/entities/employee.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne, OneToMany } from "typeorm";
import { LeavePermission } from "../../leave-permission/entities/leave-permission.entity";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";
import { LevelType } from "../../level-type/entities/level-type.entity";
import { EmployeeLeavePermissionChange } from "./employee-leave-permission-change.entity";
import { Expose } from "class-transformer";

@Entity()
export class EmployeeLeavePermission extends CustomBaseEntity {
    @Column('date')
    startPeriod: Date

    @Column('date')
    endPeriod: Date

    @Column('date', { nullable: true })
    expireDate: Date

    @Column('int', { nullable: true })
    year: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    initialNo: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    usedDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    excessDay: number;

    @ManyToOne(() => Employee, (_) => _.employeeLeavePermissions, { onDelete: 'CASCADE' })
    employee: Employee;

    @ManyToOne(() => LevelType, (_) => _.employeeLeavePermissions, { onDelete: 'CASCADE' })
    levelType: LevelType;

    @ManyToOne(() => LeaveType, (_) => _.employeeLeavePermissions, { onDelete: 'CASCADE' })
    leaveType: LeaveType;

    @OneToMany(() => EmployeeLeavePermissionChange, (_) => _.employeeLeavePermission)
    employeeLeavePermissionChanges: EmployeeLeavePermissionChange[];

    @Expose()
    get remain(): number {
        return this.qtyDay + this.excessDay - this.usedDay;
    }

    //
    leavePermission: any

    @Column({ default: false })
    toNextPeriod: boolean
}
