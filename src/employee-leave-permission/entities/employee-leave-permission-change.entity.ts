import { Employee } from "../../employee/entities/employee.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne } from "typeorm";
import { LeavePermission } from "../../leave-permission/entities/leave-permission.entity";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";
import { LevelType } from "src/level-type/entities/level-type.entity";
import { EmployeeLeavePermission } from "./employee-leave-permission.entity";

export enum EmployeeLeavePermissionChangeEnum {
    QTY = 'QTY',
    EXCESS = 'EXCESS',
    USED = 'USED'
}

@Entity()
export class EmployeeLeavePermissionChange extends CustomBaseEntity {
    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qty: number;

    @Column({ type: 'enum', enum: EmployeeLeavePermissionChangeEnum })
    changeType: EmployeeLeavePermissionChangeEnum;

    @ManyToOne(() => EmployeeLeavePermission, (_) => _.employeeLeavePermissionChanges)
    employeeLeavePermission: EmployeeLeavePermission;
}
