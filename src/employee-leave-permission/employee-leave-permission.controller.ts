import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { EmployeeLeavePermissionService } from './employee-leave-permission.service';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('โควต้าการลา')
@Controller('employee-leave-permission')
@Auth()
export class EmployeeLeavePermissionController {
  constructor(private readonly employeeLeavePermissionService: EmployeeLeavePermissionService) {}

  // @Post()
  // create(@Body() createEmployeeLeavePermissionDto: CreateEmployeeLeavePermissionDto) {
  //   return this.employeeLeavePermissionService.create(createEmployeeLeavePermissionDto);
  // }

  // @Get()
  // findAll() {
  //   return this.employeeLeavePermissionService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.employeeLeavePermissionService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateEmployeeLeavePermissionDto: UpdateEmployeeLeavePermissionDto) {
  //   return this.employeeLeavePermissionService.update(+id, updateEmployeeLeavePermissionDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.employeeLeavePermissionService.remove(+id);
  // }
}
