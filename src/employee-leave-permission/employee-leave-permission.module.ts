import { Module } from '@nestjs/common';
import { EmployeeLeavePermissionService } from './employee-leave-permission.service';
import { EmployeeLeavePermissionController } from './employee-leave-permission.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployeeLeavePermission } from './entities/employee-leave-permission.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([EmployeeLeavePermission])
  ],
  // controllers: [EmployeeLeavePermissionController],
  // providers: [EmployeeLeavePermissionService],
})
export class EmployeeLeavePermissionModule {}
