import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Employee } from "../../employee/entities/employee.entity";
import { PersonalForm } from "src/personal-form/entities/personal-form.entity";

@Entity()
@Unique(['code'])
export class Title extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //employee
    @OneToMany(() => Employee, (_) => _.title)
    employees: Array<Employee>;

    @OneToMany(() => PersonalForm, (_) => _.position)
    personalForms: Array<PersonalForm>;
}

