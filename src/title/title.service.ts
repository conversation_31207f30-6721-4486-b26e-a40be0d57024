import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateTitleDto } from './dto/create-title.dto';
import { UpdateTitleDto } from './dto/update-title.dto';
import { Title } from './entities/title.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const TITLE_PAGINATION_CONFIG: PaginateConfig<Title> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class TitleService {
  constructor(
    @InjectRepository(Title)
    private titleRepository: Repository<Title>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Title>> {
    return paginate(query, this.titleRepository, TITLE_PAGINATION_CONFIG);
  }

  async create(createTitleDto: CreateTitleDto) {

    const { ...data } = createTitleDto;

    //check title code exist
    const check = await Title.existsBy({
      code: createTitleDto?.code
    })
    if (check) {
      throw new BadRequestException('title code already.')
    }

    const item = this.titleRepository.create(
      {
        ...data,
      });

    return this.titleRepository.save(item);
  }

  findAll() {
    return this.titleRepository.find();
  }

  async findOne(id: number) {
    const item = await this.titleRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("title not found");

    return item;
  }

  async update(id: number, updateTitleDto: UpdateTitleDto) {
    //check title code exist
    const check = await Title.existsBy({
      code: updateTitleDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Title code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("title not found");

    const { ...data } = updateTitleDto;

    return this.titleRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("title not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.titleRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.titleRepository.findOne({ where: { id } });
  }
}
