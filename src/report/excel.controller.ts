import { Body, Controller, Get, Post, Query, Req, Res, StreamableFile } from '@nestjs/common';
import { Readable } from 'stream'; // Correct import for Readable
import { Response } from 'express';
import { ReportService } from './report.service';
import { ApiBody, ApiQuery, ApiTags } from '@nestjs/swagger';
import { OtReportDto } from './report.controller';

@Controller('excel')
@ApiTags('Report')
export class ExcelController {
  constructor(private reportservice: ReportService) { }
  @Post('/excel')
  async reportemployee(@Body() payload: any, @Res({ passthrough: true }) res: Response) {
    const content: any = await this.reportservice.exportEmployee();

    const file = Readable.from(content);

    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="reportEmployee.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition'
    });

    return new StreamableFile(file);
  }

  @Post('/view-daily-raw-transactions')
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  async viewDailyRawTransactions(@Query('date') date: string, @Query('employeeId') employeeId: string, @Query('departmentId') departmentId: string, @Res({ passthrough: true }) res: Response) {
    const content: any = await this.reportservice.viewDailyRawTransactions(date, +employeeId, +departmentId, 'EXCEL');

    const file = Readable.from(content);

    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="View Daily Raw Transactions.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition'
    });

    return new StreamableFile(file);
  }

  @Post('/monthly-leave-report')
  @ApiQuery({ name: 'year', required: false })
  @ApiQuery({ name: 'month', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  async monthlyLeaveReportExcel(@Query('year') year: string, @Query('month') month: string, @Query('departmentId') departmentId: string, @Res({ passthrough: true }) res: Response) {

    const departmentIdArray = departmentId ? departmentId.split(',').map(id => Number(id.trim())) : [];
    const content: any = await this.reportservice.monthlyLeaveReportExcel(year, month, departmentIdArray, 'EXCEL');

    // return content
    const file = Readable.from(content);

    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="Monthly Leave Report.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition'
    });

    return new StreamableFile(file);
  }

  @Post('/ot-request-excel')
  @ApiBody({ description: 'Excel OT Request', type: OtReportDto })
  async otRequest(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportOTRequest(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=OT Request.xlsx');

    res.end(buffer);
  }

  @Post('/ot-request-group-by-team-excel')
  @ApiBody({ description: 'Excel OT Request Report Group By team', type: OtReportDto })
  async otRequestGroupByTeam(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportOTRequestGroupByTeam(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=OT Request Report Group By Team.xlsx');

    res.end(buffer);
  }
  @Post('/ot-request-group-by-staff-excel')
  @ApiBody({ description: 'Excel OT Request Report Group By Staff', type: OtReportDto })
  async otRequestGroupByStaff(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportOTRequestGroupByStaff(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=OT Request Report Group By Staff.xlsx');

    res.end(buffer);
  }

  @Post('/ot-request-group-by-project-owner-excel')
  @ApiBody({ description: 'Excel OT Request Report Group By Project Owner', type: OtReportDto })
  async otRequestReprotGroupByProjectOwner(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportOTRequestByProjectOwner(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=OT Request Report Group By Project Owner.xlsx');

    res.end(buffer);
  }

  @Post('/view-pending-ot-request')
  @ApiBody({ description: 'Excel View Pending OT Request', type: OtReportDto })
  async otRequestViewPendingRequest(
    @Res() res: Response,
    @Body() body: OtReportDto,
    @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportViewPendingOTRequest(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=View Pending OT Request.xlsx');

    res.end(buffer);
  }
  @Post('/time-attendance-view-by-user-excel')
  @ApiBody({ description: 'Excel Time Attendance view by user', type: OtReportDto })
  async timeAttendanceViewByUser(
    @Res() res: Response,
    @Body() body: OtReportDto,
    @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportTimeAttendanceViewByUser(body);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=Time Attendance View By User.xlsx');

    res.end(buffer);
  }

  @Post('/daily-attendance-report-excel')
  @ApiBody({ description: 'Excel Daily Attendance Report', type: OtReportDto })
  async dailyAttendanceReport(
    @Res() res: Response,
    @Body() body: OtReportDto,
    @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportDailyAttendanceReport(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=Daily Attendance Report.xlsx');

    res.end(buffer);
  }

  @Post('/monthly-attendance-report-excel')
  @ApiBody({ description: 'Excel Monthly Attendance Report', type: OtReportDto })
  async monthlyAttendanceReport(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportMonthlyAttendanceReport(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=Monthly Attendance Report.xlsx');

    res.end(buffer);
  }

  @Post('/summary-deduction-report-excel')
  @ApiBody({ description: 'Excel Summary Deduction Report', type: OtReportDto })
  async summaryDeductionReport(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.exportSummaryDeductReport(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=Summary Deduction Report.xlsx');

    res.end(buffer);
  }

  @Post('/accumulate-staff-report-excel')
  @ApiBody({ description: 'Excel Accumulate Staff', type: OtReportDto })
  async AccumulateStaffExcel(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer = await this.reportservice.AccumulateStaffReportExcel(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename= Accumulate Staff.xlsx');

    res.end(buffer);
  }

  @Post('/detail-accumulate-staff-report-excel')
  @ApiBody({ description: 'Excel Detail Accumulate Staff', type: OtReportDto })
  async detailAccumulateStaffExcel(
    @Res() res: Response,
    @Body() body: OtReportDto, @Req() req: Request
  ) {
    const buffer: any = await this.reportservice.detailAccumulateStaffReportExcel(body);
    // ตั้งค่า Header Response
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=Detail Accumulate Staff.xlsx');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

    res.end(buffer);
  }


}
