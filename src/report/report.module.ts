import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import { Employee } from 'src/employee/entities/employee.entity';
import { ExcelController } from './excel.controller';
import { EmployeeModule } from 'src/employee/employee.module';
import { Department } from 'src/department/entities/department.entity';
import { LeaveModule } from 'src/leave/leave.module';
import { Leave } from 'src/leave/entities/leave.entity';
import { AttandancesModule } from 'src/attandances/attandances.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([Employee,Department,Leave]), // Use the entity instead of the custom repository
        EmployeeModule,
        LeaveModule,
        AttandancesModule
    ],
    providers: [ReportService],
    controllers: [ReportController, ExcelController],
    exports: [ReportService],
})
export class ReportModule {}

