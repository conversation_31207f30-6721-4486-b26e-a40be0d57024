import { Body, Controller, Get, HttpException, HttpStatus, Post, Query, Redirect, Req, Res, StreamableFile } from '@nestjs/common';
import { Readable } from 'stream'; // Correct import for Readable
import { Response } from 'express';
import { ReportService } from './report.service';
import { ApiBody, ApiProperty, ApiQuery, ApiTags } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';
import axios from 'axios';

export class ReportDto {
  @ApiProperty({
    description: 'dateStart',
    required: false,
    type: String,
  })
  dateStart?: string;

  @ApiProperty({
    description: 'dateEnd',
    required: false,
    type: String,
  })
  dateEnd?: string;

  @ApiProperty({
    description: 'month',
    required: false,
    type: String,
  })
  month?: string;

  @ApiProperty({
    description: 'year',
    required: false,
    type: String,
  })
  year?: string;

  @ApiProperty({
    description: 'employeeId',
    required: false,
    type: String,
  })
  employeeId?: number;

  @ApiProperty({
    description: 'List of department IDs',
    required: false,
    type: [Number],
  })
  @IsArray()
  departmentId?: number[];

  @ApiProperty({
    description: 'leaveTypeId',
    required: false,
    type: String,
  })
  leaveTypeId?: string;

  @ApiProperty({
    description: 'Approver ID',
    required: false,
    type: Number,
  })
  headId?: number;

}

export class ReportAttendanceDailyDto {
  @ApiProperty({
    description: 'dateStart',
    required: false,
    type: String,
  })
  dateStart?: string;

  @ApiProperty({
    description: 'dateEnd',
    required: false,
    type: String,
  })
  dateEnd?: string;

  @ApiProperty({
    description: 'employeeId',
    required: false,
    type: String,
  })
  employeeId?: number;

  @ApiProperty({
    description: 'List of department IDs',
    required: false,
    type: [Number],
  })
  @IsArray()
  departmentId?: number[];
}

export class OtReportDto {
  @ApiProperty({
    description: 'dateStart',
    required: false,
    type: String,
  })
  dateStart?: string;

  @ApiProperty({
    description: 'dateEnd',
    required: false,
    type: String,
  })
  dateEnd?: string;


  @ApiProperty({
    description: 'employeeId',
    required: false,
    type: String,
  })
  employeeId?: number;

  @ApiProperty({
    description: 'headId',
    required: false,
    type: String,
  })
  headId?: number;

  @ApiProperty({
    description: 'approverId',
    required: false,
    type: String,
  })
  approverId?: number;

  @ApiProperty({
    description: 'projectId',
    required: false,
    type: String,
  })
  projectId?: number;

  @ApiProperty({
    description: 'projectOwnerId',
    required: false,
    type: String,
  })
  projectOwnerId?: number;

  @ApiProperty({
    description: 'List of department IDs',
    required: false,
    type: [Number],
  })
  @IsArray()
  departmentId?: number[];

  @ApiProperty({
    description: 'status',
    required: false,
    type: String,
  })
  status?: string;


}


@Controller('report')
@ApiTags('Report')
export class ReportController {
  constructor(private reportservice: ReportService) { }


  @Get('redirect-report')
  @Redirect()
  @ApiQuery({ name: 'id', required: true })
  @ApiQuery({ name: 'type', required: true })
  async fetchReportMpdf(@Query('id') id: string, @Query('type') type: string, @Res() res: Response) {
    let url: string | null = null;

    if (type === 'applicant') {
      url = `${process.env.PDF_URL}/api/employmentformpdf/${id}`;
    } else if (type === 'personal_form') {
      url = `${process.env.PDF_URL}/api/personnerequisitionformpdf/${id}`;
    } else {
      throw new HttpException('Invalid type', HttpStatus.BAD_REQUEST);
    }

    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${id}.pdf"`,
        'Content-Length': response.data.length,
      });

      res.send(response.data);
    } catch (error) {
      throw new HttpException('Failed to fetch PDF', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }



  @Get('/view-daily-raw-transactions')
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  viewDailyRawTransactions(@Query('date') date: string, @Query('employeeId') employeeId: string, @Query('departmentId') departmentId: string) {
    return this.reportservice.viewDailyRawTransactions(date, +employeeId, +departmentId, 'REPORT');
  }

  @Get('/view-daily-raw-transactions-web')
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  viewDailyRawTransactionsWeb(@Query('date') date: string, @Query('employeeId') employeeId: string, @Query('departmentId') departmentId: string) {
    return this.reportservice.viewDailyRawTransactionsWeb(date, +employeeId, +departmentId, 'REPORT');
  }

  @Get('/time-attendance-view-by-user')
  // @ApiQuery({ name: 'employeeId', required: false})
  timeAttendanceViewByUser(@Query('year') year: string, @Query('month') month: string, @Query('employeeId') employeeId: string) {
    return this.reportservice.timeAttendanceViewByUser(+year, +month, +employeeId);
  }


  @Post('/accumulate_staff')
  @ApiBody({ description: 'Accumulate staff report request', type: ReportDto })
  reportAccumulateStaff(@Body() body: ReportDto, @Req() req: Request) {
    return this.reportservice.reportAccumulateStaff(body);
  }

  @Post('/detail_accumulate_staff')
  @ApiBody({ description: 'detail_accumulate_staff request', type: ReportDto })
  detailAccumulateStaff(@Body() body: ReportDto, @Req() req: Request) {
    return this.reportservice.detailAccumulateStaff(body);
  }

  @Post('/monthlyLeave_report')
  @ApiBody({ description: 'monthly leave report request', type: ReportDto })
  monthlyLeaveReport(@Body() body: ReportDto, @Req() req: Request) {
    return this.reportservice.monthlyLeaveReport(body);
  }

  @Post('/daily_attendance_report')
  @ApiBody({ description: 'daily_attendance_report request', type: ReportAttendanceDailyDto })
  dailyAttendanceReport(@Body() body: ReportAttendanceDailyDto, @Req() req: Request) {
    return this.reportservice.dailyAttendanceReport(body);
  }

  @Post('/monthly_attendance_report')
  @ApiBody({ description: 'monthly_attendance_report request', type: ReportDto })
  monthlyAttendanceReport(@Body() body: ReportDto, @Req() req: Request) {
    return this.reportservice.monthlyAttendanceReport(body);
  }

  @Post('/summary_deduction_report')
  @ApiBody({ description: 'summary_deduction_report request', type: ReportDto })
  summaryDeductionReport(@Body() body: ReportDto, @Req() req: Request) {
    return this.reportservice.summaryDeductionReport(body);
  }

  @Post('/ot_request_report')
  @ApiBody({ description: 'ot_request_report request', type: OtReportDto })
  otRequestReport(@Body() body: OtReportDto, @Req() req: Request) {
    return this.reportservice.otRequestReport(body);
  }

  @Post('/ot_comparison_report')
  @ApiBody({ description: 'OT & TA Comparison By Staff', type: OtReportDto })
  otComparisonReport(@Body() body: OtReportDto, @Req() req: Request) {
    return this.reportservice.otComparisonReport(body);
  }

  @Post('/ot_request_group_by_team')
  @ApiBody({ description: 'OT & TA Comparison By Staff', type: OtReportDto })
  otRequestGroupByTeam(@Body() body: OtReportDto, @Req() req: Request) {
    return this.reportservice.otRequestGroupByTeam(body);
  }

  @Post('/ot_air_request_report')
  @ApiBody({ description: 'ot_request_report request', type: OtReportDto })
  otAirRequestReport(@Body() body: OtReportDto, @Req() req: Request) {
    return this.reportservice.otAirRequestReport(body);
  }


}
