import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { CreateWorkShiftTimeDto } from "src/work-shift/dto/create-work-shift-time.dto";

export class CreateWorkShiftDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;

    @ApiProperty()
    readonly time: CreateWorkShiftTimeDto[]
}

