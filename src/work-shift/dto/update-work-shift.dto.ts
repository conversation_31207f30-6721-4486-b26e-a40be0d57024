import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateWorkShiftDto } from './create-work-shift.dto';
import { IsNotEmpty } from 'class-validator';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';

export class UpdateWorkShiftDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;

    @ApiProperty()
    time: WorkShiftTime[]

    @IsNotEmpty()
    @ApiProperty()
    readonly active: boolean
}

