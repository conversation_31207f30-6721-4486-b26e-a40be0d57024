import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { WorkShift } from "./work-shift.entity";


@Entity()
export class WorkShiftTime extends CustomBaseEntity {
    @Column()
    day: string;

    @Column("time", { nullable: true })
    time_in: string

    @Column("time", { nullable: true })
    time_out: string

    @Column("time", { nullable: true })
    break_in: string

    @Column("time", { nullable: true })
    break_out: string


    @Column({ default: true })
    active: boolean

    //WorkShift
    @ManyToOne(() => WorkShift, (_) => _.workShiftTimes)
    @JoinColumn({ name: 'work_shift_id' })
    workShift: WorkShift;

}




