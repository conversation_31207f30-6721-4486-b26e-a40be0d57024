import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "../../employee/entities/employee.entity";
import { WorkShiftTime } from "./work-shift-time.entity";


@Entity()
@Unique(['code'])
export class WorkShift extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    countEmployee: number

    //employee
    @OneToMany(() => Employee, (_) => _.workShift)
    employees: Array<Employee>;

    //WorkShiftTime
    @OneToMany(() => WorkShiftTime, (_) => _.workShift)
    workShiftTimes: Array<WorkShiftTime>;

}



