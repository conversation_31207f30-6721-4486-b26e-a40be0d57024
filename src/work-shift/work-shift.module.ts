import { Modu<PERSON> } from '@nestjs/common';
import { WorkShiftService } from './work-shift.service';
import { WorkShiftController } from './work-shift.controller';
import { WorkShift } from './entities/work-shift.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';

@Module({
  imports: [TypeOrmModule.forFeature([WorkShift, WorkShiftTime])],
  controllers: [WorkShiftController],
  providers: [WorkShiftService],
})
export class WorkShiftModule { }
