import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { WORK_SHIFT_PAGINATION_CONFIG, WorkShiftService } from './work-shift.service';
import { CreateWorkShiftDto } from './dto/create-work-shift.dto';
import { UpdateWorkShiftDto } from './dto/update-work-shift.dto';

import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('work-shift')
@ApiTags('เวลาเข้า-ออกงานงาน')
// @Auth()

export class WorkShiftController {
  constructor(private readonly workShiftService: WorkShiftService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(WORK_SHIFT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.workShiftService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createWorkShiftDto: CreateWorkShiftDto) {
    return this.workShiftService.create(createWorkShiftDto);
  }

  @Get()
  findAll() {
    return this.workShiftService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.workShiftService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateWorkShiftDto: UpdateWorkShiftDto) {
    return this.workShiftService.update(+id, updateWorkShiftDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.workShiftService.remove(+id);
  }
}

