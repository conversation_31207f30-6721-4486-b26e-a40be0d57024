import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateWorkShiftDto } from './dto/create-work-shift.dto';
import { UpdateWorkShiftDto } from './dto/update-work-shift.dto';
import { WorkShift } from './entities/work-shift.entity';
import { DataSource, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';
import { log } from 'console';

export const WORK_SHIFT_PAGINATION_CONFIG: PaginateConfig<WorkShift> = {
  sortableColumns: ['id', 'code', 'name', 'active'],
  select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class WorkShiftService {
  constructor(
    @InjectRepository(WorkShift)
    private workShiftRepository: Repository<WorkShift>,

    @InjectRepository(WorkShiftTime)
    private workShiftTimeRepository: Repository<WorkShiftTime>,

    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<WorkShift>> {
    return paginate(query, this.workShiftRepository, WORK_SHIFT_PAGINATION_CONFIG);
  }

  async create(createWorkShiftDto: CreateWorkShiftDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { ...data } = createWorkShiftDto;

      //check work shift code exist
      const check = await WorkShift.existsBy({
        code: createWorkShiftDto?.code
      })
      if (check) {
        throw new BadRequestException('work shift code already.')
      }

      const item = this.workShiftRepository.create(
        {
          ...data,
        });

      await queryRunner.manager.save(item)

      //add work shift time
      for (let i = 0; i < createWorkShiftDto.time.length; i++) {

        const workShiftTime = new WorkShiftTime();

        workShiftTime.workShift = item
        workShiftTime.day = createWorkShiftDto.time[i].day;
        workShiftTime.time_in = createWorkShiftDto.time[i].time_in;
        workShiftTime.time_out = createWorkShiftDto.time[i].time_out;
        workShiftTime.break_in = createWorkShiftDto.time[i].break_in;
        workShiftTime.break_out = createWorkShiftDto.time[i].break_out;
        workShiftTime.active = createWorkShiftDto.time[i].active;

        await queryRunner.manager.save(workShiftTime)
      }

      await queryRunner.commitTransaction();

      return item

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }

  async findAll() {
    let items = await this.workShiftRepository.find({
      relations: ['workShiftTimes', 'employees'],
    });

    if (items) {
      for (let i = 0; i < items.length; i++) {
        items[i].countEmployee = (items[i].employees ? items[i].employees.length : 0)
      }

    }

    return items;
  }

  async findOne(id: number) {
    const item = await this.workShiftRepository.findOne(
      {
        relations: ['workShiftTimes', 'employees'],
        where: { id }
      });

    if (!item) throw new NotFoundException("work shift not found");

    return item;
  }

  async update(id: number, updateWorkShiftDto: UpdateWorkShiftDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      //check work shift code exist
      const check = await WorkShift.existsBy({
        code: updateWorkShiftDto?.code,
        id: Not(id)
      })
      if (check) {
        throw new BadRequestException('Work shift code already.')
      }

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("work shift not found");

      item.code = updateWorkShiftDto.code
      item.name = updateWorkShiftDto.name
      item.active = updateWorkShiftDto.active

      await queryRunner.manager.save(item)


      // update work shift time
      if (updateWorkShiftDto.time.length > 0) {

        //
        const getWorkShiftTime = await this.workShiftTimeRepository.find({
          relations: ['workShift'],
          where: {
            workShift: {
              id: id
            }
          },
        });

        if (!getWorkShiftTime) {
          throw new BadRequestException(`workShiftTime is not found`)
        }

        //del
        getWorkShiftTime.forEach(async (item) => {
          const deleteResponse = await this.workShiftTimeRepository.softDelete(item.id);
        });

        //add
        for (let i = 0; i < updateWorkShiftDto.time.length; i++) {

          const workShiftTime = new WorkShiftTime();

          workShiftTime.workShift = item
          workShiftTime.day = updateWorkShiftDto.time[i].day;
          workShiftTime.time_in = updateWorkShiftDto.time[i].time_in;
          workShiftTime.time_out = updateWorkShiftDto.time[i].time_out;
          workShiftTime.break_in = updateWorkShiftDto.time[i].break_in;
          workShiftTime.break_out = updateWorkShiftDto.time[i].break_out;
          workShiftTime.active = updateWorkShiftDto.time[i].active;

          await queryRunner.manager.save(workShiftTime)
        }
      }

      await queryRunner.commitTransaction();

      return item

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("work shift not found");

    //check employee must be selected
    const empLength = (await WorkShift.findOne({
      where: {
        id: id
      },
      relations: {
        employees: true
      }
    })).employees.length

    if (empLength > 0) {
      throw new BadRequestException(`work shift can't delete`)
    }

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.workShiftRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.workShiftRepository.findOne({ where: { id } });
  }
}

