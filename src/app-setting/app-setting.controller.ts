import { Controller, Get, Post, Body, Patch, Param, Delete, Put, Query } from '@nestjs/common';
import { AppSettingService } from './app-setting.service';
import { CreateAppSettingDto } from './dto/create-app-setting.dto';
import { UpdateAppSettingDto } from './dto/update-app-setting.dto';
import { ApiQuery, ApiTags } from '@nestjs/swagger';

@Controller('app-setting')
@ApiTags('ตั้งค่าโปรแกรม')
export class AppSettingController {
  constructor(private readonly appSettingService: AppSettingService) { }

  // @Post()
  // create(@Body() createAppSettingDto: CreateAppSettingDto) {
  //   return this.appSettingService.create(createAppSettingDto);
  // }

  @Get()
  // @ApiQuery({ name: 'keys', isArray: true, enum: ['MAIL_OT', 'URL'] })
  findAll() {
    return this.appSettingService.findAll();
  }

  @Get(':key')
  findOneBykey(@Param('key') key: string) {
    return this.appSettingService.findOneBykey(key);
  }

  @Put(':key')
  update(@Param('key') key: string, @Body() updateAppSettingDto: UpdateAppSettingDto) {
    return this.appSettingService.update(key, updateAppSettingDto);
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.appSettingService.remove(+id);
  // }
}
