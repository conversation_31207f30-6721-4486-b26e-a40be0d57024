import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateAppSettingDto } from './dto/create-app-setting.dto';
import { UpdateAppSettingDto } from './dto/update-app-setting.dto';
import { AppSetting } from './entities/app-setting.entity';
import { In } from 'typeorm';

@Injectable()
export class AppSettingService {
  create(createAppSettingDto: CreateAppSettingDto) {
    return 'This action adds a new appSetting';
  }

  findAll() {
    return AppSetting.find();
  }

  findOneBykey(key: string) {
    const data = AppSetting.findOne({
      where: {
        key: key,
      }
    })

    if (!data) throw new NotFoundException("app setting not found.");

    return data
  }

  async update(key: string, updateAppSettingDto: UpdateAppSettingDto) {
    const appSetting = await this.findOneBykey(key);

    await AppSetting.update(appSetting.id, {
      value: updateAppSettingDto.value,
      type: updateAppSettingDto.type,
    })

    return this.findOneBykey(key);
  }

  remove(id: number) {
    return `This action removes a #${id} appSetting`;
  }
}
