import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query, Req } from '@nestjs/common';
import { OT_AIR_PAGINATION_CONFIG, OtAirService } from './ot-air.service';

import { CreateOtAirDto } from './dto/create-ot-air.dto';
import { UpdateOtAirDto } from './dto/update-ot-air.dto';

import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags, ApiQuery } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UpdateStatusOtAirDto } from './dto/update-status-ot-air.dto';
import { Request } from 'express';

@Controller('ot-air')
@ApiTags('โอทีแอร์')
@Auth()

export class OtAirController {
  constructor(private readonly otAirService: OtAirService) { }


  // @Get('/wait-approve')
  // @HttpCode(HttpStatus.OK)
  // @ApiPaginationQuery(OT_AIR_PAGINATION_CONFIG)
  // waitApprove(@Req() req: Request, @Paginate() query: PaginateQuery) {
  //   const headId = req.user['sub'];

  //   query.filter = {
  //     ...query.filter,
  //     'head.id': headId + ""
  //   }

  //   return this.otAirService.datatables(query);
  // }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(OT_AIR_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.otAirService.datatables(query);
  }

  @Post()
  @Roles(AuthRole.Employee)
  create(@Req() req: Request, @Body() createOtAirDto: CreateOtAirDto) {
    const employeeId = req.user['sub'];
    return this.otAirService.create(employeeId, createOtAirDto);
  }

  @Get()
  @ApiQuery({ name: 'zoneId', required: false })
  @ApiQuery({ name: 'floorId', required: false })
  @ApiQuery({ name: 'projectId', required: false })
  @ApiQuery({ name: 'employeeId', required: false })
  @ApiQuery({ name: 'headId', required: false })
  findAll(@Query() query) {
    return this.otAirService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.otAirService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Req() req: Request, @Body() updateOtAirDto: UpdateOtAirDto) {
    const employeeId = updateOtAirDto.employeeId ?? req.user['sub'];

    return this.otAirService.update(employeeId, +id, updateOtAirDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.otAirService.remove(+id);
  }

  @Put(':id/approve')
  @Roles(AuthRole.Employee)
  updateStatus(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Body() updateStatusOtAirDto: UpdateStatusOtAirDto) {
    const employeeId = req.user['sub'];

    return this.otAirService.updateStatus(+id, updateStatusOtAirDto, employeeId);
  }

  @Put(':id/approve-admin')
  @Roles(AuthRole.Employee)
  updateStatusAdmin(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Body() updateStatusOtAirDto: UpdateStatusOtAirDto) {
    const userId = req.user['sub'];

    return this.otAirService.updateStatusAdmin(+id, updateStatusOtAirDto, userId);
  }
}




