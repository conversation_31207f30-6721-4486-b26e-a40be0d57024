import { <PERSON>umn, <PERSON><PERSON>ty, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { Employee } from "../../employee/entities/employee.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";
import { Project } from "src/project/entities/project.entity";
import { Floor } from "src/floor/entities/floor.entity";
import { Zone } from "src/zone/entities/zone.entity";
import { User } from "src/user/entities/user.entity";


@Entity()
@Unique(['code'])
export class OtAir extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column("date", { nullable: true })
    date: Date

    @Column("time", { nullable: true })
    timeStart: string

    @Column("time", { nullable: true })
    timeEnd: string

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyHour: number;

    @Column("text", { nullable: true })
    detail: string;

    @Column({
        type: "enum",
        enum: ["open", "process", "head_approved", "approved", "cancel", "reject"],
        default: 'open'
    })
    status: string

    @Column("timestamp", { nullable: true })
    statusDate: Date;

    @Column('text', { nullable: true })
    statusRemark: string

    @Column({ default: true })
    active: boolean

    //employee
    @ManyToOne(() => Employee, (_) => _.leaves)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;


    //zone
    @ManyToOne(() => Zone, (_) => _.otAirs)
    @JoinColumn({ name: 'zone_id' })
    zone: Zone;

    //floor
    @ManyToOne(() => Floor, (_) => _.otAirs)
    @JoinColumn({ name: 'floor_id' })
    floor: Floor;

    //project
    @ManyToOne(() => Project, (_) => _.otAirs)
    @JoinColumn({ name: 'project_id' })
    project: Project;

    //head
    @ManyToOne(() => Employee, (_) => _.otAirs)
    @JoinColumn({ name: 'head_id' })
    head: Employee;

     //approver
     @ManyToOne(() => Employee, (_) => _.ots)
     @JoinColumn({ name: 'approver_id' })
     approver: Employee;

    isHoliday: boolean;
    isIssue: boolean;
    attendance: any;

    @Column({ nullable: true })
    email: string;

    @ManyToOne(() => User, (_) => _.ots)
    @JoinColumn({ name: 'admin_id' })
    admin: User;
}





