import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
export class CreateOtAirDto {

    @IsNotEmpty()
    @ApiProperty()
    readonly date: string;

    @ApiProperty()
    readonly timeStart: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly timeEnd: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly detail: string;

    @ApiProperty()
    readonly employeeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly zoneId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly floorId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly projectId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly headId: number;

    @ApiProperty({
        example: '<EMAIL>,<EMAIL>'
    })
    readonly email: string;
}


