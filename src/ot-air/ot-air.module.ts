import { Modu<PERSON> } from '@nestjs/common';
import { OtAirService } from './ot-air.service';
import { OtAirController } from './ot-air.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OtAir } from './entities/ot-air.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { Project } from 'src/project/entities/project.entity';
import { Floor } from 'src/floor/entities/floor.entity';
import { Zone } from 'src/zone/entities/zone.entity';

@Module({
  imports: [TypeOrmModule.forFeature([OtAir, Employee, Project, Zone, Floor])],
  controllers: [OtAirController],
  providers: [OtAirService],
})
export class OtAirModule { }
