import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateOtAirDto } from './dto/create-ot-air.dto';
import { UpdateOtAirDto } from './dto/update-ot-air.dto';
import { UpdateStatusOtAirDto } from './dto/update-status-ot-air.dto';

import { DataSource, LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Helper } from 'src/common/utils/helper';

import { Employee } from 'src/employee/entities/employee.entity';

import { MailerService } from '@nestjs-modules/mailer';
import { DateTime } from 'luxon';
import { chain, groupBy, sortBy } from 'lodash';
import { OtAir } from './entities/ot-air.entity';
import { Project } from 'src/project/entities/project.entity';
import { Floor } from 'src/floor/entities/floor.entity';
import { Zone } from 'src/zone/entities/zone.entity';
import { User } from 'src/user/entities/user.entity';

export const OT_AIR_PAGINATION_CONFIG: PaginateConfig<OtAir> = {
  relations: ['employee', 'zone', 'floor', 'project', 'head', 'approver'],
  sortableColumns: [
    'id',
    'code',
    'date',
    'timeStart',
    'timeEnd',
    'qtyHour',
    'detail',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'floor.id',
    'floor.code',
    'floor.name',
    'zone.id',
    'zone.code',
    'zone.name',
    'project.id',
    'project.code',
    'project.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'approver.id',
    'approver.code',
    'approver.firstname',
    'approver.lastname',
    'status',
    'statusRemark',
    'statusDate'
  ],
  select: [
    'id',
    'code',
    'date',
    'timeStart',
    'timeEnd',
    'qtyHour',
    'detail',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'project.id',
    'project.code',
    'project.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'approver.id',
    'approver.code',
    'approver.firstname',
    'approver.lastname',
    'status',
    'statusRemark',
    'statusDate'
  ],
  searchableColumns: ['code', 'employee.code', 'employee.firstname', 'employee.lastname'],
  filterableColumns: {
    code: [FilterOperator.EQ],
    date: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE, FilterOperator.BTW],
    timeStart: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    timeEnd: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    status: [FilterOperator.IN],
    'employee.id': [FilterOperator.EQ],
    'project.id': [FilterOperator.EQ],
    'zone.id': [FilterOperator.EQ],
    'floor.id': [FilterOperator.EQ],
    'head.id': [FilterOperator.EQ],
    'approver.id': [FilterOperator.EQ]
  },
};
@Injectable()
export class OtAirService {
  constructor(
    @InjectRepository(OtAir)
    private otAirRepository: Repository<OtAir>,

    @InjectRepository(Zone)
    private zoneRepository: Repository<Zone>,

    @InjectRepository(Floor)
    private floorRepository: Repository<Floor>,

    @InjectRepository(Project)
    private projectRepository: Repository<Project>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,

    private dataSource: DataSource,
    private readonly mailerService: MailerService,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<OtAir>> {
    return paginate(query, this.otAirRepository, OT_AIR_PAGINATION_CONFIG);
  }

  async create(employeeId: number, createOtAirDto: CreateOtAirDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { zoneId, floorId, projectId, headId, date, timeStart, timeEnd } = createOtAirDto;

      const [zone, floor, checkProject, employee, head] = await Promise.all([
        this.zoneRepository.findOne({ where: { id: zoneId } }),
        this.floorRepository.findOne({ where: { id: floorId } }),
        this.projectRepository.findOne({ where: { id: projectId } }),
        this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true } }),
        this.employeeRepository.findOne({ where: { id: headId } })
      ]);

      if (!zone) throw new NotFoundException("zone not found");
      if (!floor) throw new NotFoundException("floor not found");
      if (!checkProject) throw new NotFoundException("project not found");
      if (!employee) throw new NotFoundException("employee not found");
      if (!head) throw new NotFoundException("head not found");

      //get last id
      const lastOt = await OtAir.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = lastOt.length;

      //add ot
      const otCode = Helper.generateNo('OTA', lastId).toString();
      // คำนวณ
      const qtyHour = Helper.calculateHoursWithoutBreak(createOtAirDto.timeStart, createOtAirDto.timeEnd);

      const newOt = OtAir.create({
        code: otCode,
        date: createOtAirDto.date,
        timeStart: createOtAirDto.timeStart,
        timeEnd: createOtAirDto.timeEnd,
        detail: createOtAirDto.detail,
        qtyHour: qtyHour,
        status: 'open',
        zone: { id: zoneId },
        floor: { id: floorId },
        project: { id: projectId },
        employee: { id: employeeId },
        head: { id: headId },
        email: createOtAirDto?.email
      });

      // บันทึก ot
      await queryRunner.manager.save(OtAir, newOt)


      await queryRunner.commitTransaction();

      //sent email to Approver head
      this.mailerService.sendMail({
        to: head.email,
        subject: 'Overtime (Air) Request',
        template: 'ot-air-approve',
        context: {
          fullname: employee.fullname,
          project: checkProject.name,
          date: DateTime.fromISO(date).toLocal().toFormat('dd/MM/yyyy'),
          timeStart: DateTime.fromISO(timeStart).toLocal().toFormat('HH:mm'),
          timeEnd: DateTime.fromISO(timeEnd).toLocal().toFormat('HH:mm'),
          total: qtyHour,
          detail: newOt?.detail,
          // web: process.env.WEB_URL + '/ot-air/ot-air-approval',
          web: process.env.WEB_URL + '/sign-in',
        }
      }).catch((error) => {
        console.error(head.email);
        console.error(new Date().toISOString(), error);
      });

      //senf eamil to Requester
      this.mailerService.sendMail({
        to: employee.email,
        subject: 'Overtime (Air) Request',
        template: 'ot-air-request',
        context: {}
      }).catch((error) => {
        console.error(employee.email);
        console.error(new Date().toISOString(), error);
      });

      //senf eamil to CC
      if (createOtAirDto?.email) {
        const ccEmail = createOtAirDto?.email?.split(',');

        if (ccEmail.length > 0) {
          this.mailerService.sendMail({
            to: ccEmail,
            subject: 'Overtime (Air) Request',
            template: 'ot-air-request',
            context: {}
          }).catch((error) => {
            console.error(employee.email);
            console.error(new Date().toISOString(), error);
          });
        }

      }

      return newOt;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll(query) {
    const zoneId = query.zoneId
    const floorId = query.floorId
    const projectId = query.projectId
    const employeeId = query.employeeId
    const headId = query.headId

    return this.otAirRepository.find({
      relations: ['employee', 'zone', 'floor', 'project', 'head'],
      where: {
        zone: {
          id: zoneId ?? null,
        },
        floor: {
          id: floorId ?? null,
        },
        project: {
          id: projectId ?? null,
        },
        employee: {
          id: employeeId ?? null,
        },
        head: {
          id: headId ?? null,
        },
      }
    });
  }



  async findOne(id: number) {
    const item = await this.otAirRepository.findOne({
      relations: ['employee', 'zone', 'floor', 'project', 'head'],
      where: { id }
    });

    if (!item) throw new NotFoundException("ot request not found");

    return item;
  }

  async update(employeeId: number, id: number, updateOtAirDto: UpdateOtAirDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("employee not found");

      const { zoneId, floorId, projectId, headId, date, timeStart, timeEnd } = updateOtAirDto;

      const [zone, floor, checkProject, employee, head] = await Promise.all([
        this.zoneRepository.findOne({ where: { id: zoneId } }),
        this.floorRepository.findOne({ where: { id: floorId } }),
        this.projectRepository.findOne({ where: { id: projectId } }),
        this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true } }),
        this.employeeRepository.findOne({ where: { id: headId } })
      ]);

      if (!zone) throw new NotFoundException("zone not found");
      if (!floor) throw new NotFoundException("floor not found");
      if (!checkProject) throw new NotFoundException("project not found");
      if (!employee) throw new NotFoundException("employee not found");
      if (!head) throw new NotFoundException("head not found");


      // คำนวณ
      const qtyHour = Helper.calculateHoursWithoutBreak(updateOtAirDto.timeStart, updateOtAirDto.timeEnd);

      // อัปเดตข้อมูล ot
      await this.otAirRepository.update(id, {
        date: updateOtAirDto.date,
        timeStart: updateOtAirDto.timeStart,
        timeEnd: updateOtAirDto.timeEnd,
        detail: updateOtAirDto.detail,
        qtyHour: qtyHour,
        // status: 'open',
        zone: { id: zoneId },
        floor: { id: floorId },
        project: { id: projectId },
        employee: { id: employeeId },
        head: { id: headId },
      });

      await queryRunner.commitTransaction();

      return item;

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }


  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("employee not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.otAirRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.otAirRepository.findOne({
      // relations: ['employee', 'project', 'head'],
      relations: {
        employee: true,
        project: true,
        head: true,
      },
      where: { id }
    });
  }


  async updateStatus(id: number, updateStatusOtAirDto: UpdateStatusOtAirDto, headId: number) {

    const { status, statusRemark } = updateStatusOtAirDto;

    const ot = await this.otAirRepository.findOne({
      where: { id },
      relations: {
        project: true,
        employee: true,
        head: true
      }
    });

    if (!ot) {
      throw new NotFoundException('ot not found');
    }

    const admin = await User.findOne({
      where: { id: updateStatusOtAirDto.adminId },
    });

    if (!admin) {// ถ้าไม่ใช่ Admin จะต้องเช็คสถานะและ head
      if (ot.head.id != headId) {
        throw new BadRequestException("You can't Approver for this ot.")
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      const updateData: any = {
        status,
        statusRemark,
        statusDate: new Date(),
      };

      if (admin) {
        updateData.admin = { id: admin.id };
      }

      await queryRunner.manager.update(OtAir, ot.id, updateData);

      await queryRunner.commitTransaction();

      // Send email in the background
      const emailTemplate = status === 'approved' ? 'ot-air-confirm' : 'ot-air-reject';
      this.mailerService.sendMail({
        to: ot.employee?.email,
        subject: 'Overtime (Air) Request',
        template: emailTemplate,
        context: {},
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });


      return { message: 'ok' }
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async updateStatusAdmin(id: number, updateStatusOtAirDto: UpdateStatusOtAirDto, headId: number) {

    const { status, statusRemark } = updateStatusOtAirDto;

    const ot = await this.otAirRepository.findOne({
      where: { id },
      relations: {
        project: true,
        employee: true,
        head: true
      }
    });

    if (!ot) {
      throw new NotFoundException('ot not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      await queryRunner.manager.update(OtAir, ot.id, {
        status,
        statusRemark,
        statusDate: new Date(),
      });

      await queryRunner.commitTransaction();

      // Send email in the background
      const emailTemplate = status === 'approved' ? 'ot-air-confirm' : 'ot-air-reject';
      this.mailerService.sendMail({
        to: ot.employee?.email,
        subject: 'Overtime (Air) Request',
        template: emailTemplate,
        context: {},
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });


      return { message: 'ok' }
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }
}



