import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { LeaveTypeEnum } from "../entities/leave.entity";
export class CreateLeaveDto {

    @IsNotEmpty()
    @ApiProperty()
    readonly date: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly type: LeaveTypeEnum;

    @ApiProperty()
    readonly dateStart: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly dateEnd: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly reason: string;

    // @IsNotEmpty()
    @ApiProperty()
    readonly file: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly employeeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly leaveTypeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly headId: number;
}

export class CreateLeaveAdminDto extends CreateLeaveDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly employeeId: number;
}

