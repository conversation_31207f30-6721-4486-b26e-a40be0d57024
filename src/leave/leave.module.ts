import { Module } from '@nestjs/common';
import { LeaveService } from './leave.service';
import { LeaveController } from './leave.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Leave } from './entities/leave.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { LeaveDate } from 'src/leave/entities/leave-date.entity';
import { WorkShift } from 'src/work-shift/entities/work-shift.entity';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { Holiday } from 'src/holiday/entities/holiday.entity';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { Department } from 'src/department/entities/department.entity';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Leave, LeaveType, LeavePermission, LeaveDate, WorkShift, WorkShiftTime, Employee, Holiday, Department,EmployeeLeavePermission])],
  controllers: [LeaveController],
  providers: [LeaveService],
  exports: [LeaveService]
})
export class LeaveModule { }
