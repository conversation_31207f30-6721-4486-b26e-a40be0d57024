import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query, Req } from '@nestjs/common';
import { LEAVE_PAGINATION_CONFIG, LeaveService } from './leave.service';
import { CreateLeaveAdminDto, CreateLeaveDto } from './dto/create-leave.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags, ApiQuery } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UpdateStatusLeaveDto } from './dto/update-status-leave.dto';
import { Request } from 'express';

@Controller('leave')
@ApiTags('ลา')
@Auth()

export class LeaveController {
  constructor(private readonly leaveService: LeaveService) { }

  @Post('/report-of-approver')
  reportOfApprover(@Req() req: Request) {
    const headId = req.user['sub'];

    return this.leaveService.reportOfApprover(headId)
  }

  @Post('/test-email/')
  testEmail() {
    return this.leaveService.testEmail();
  }

  @Get('/wait-approve')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(LEAVE_PAGINATION_CONFIG)
  waitApprove(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const headId = req.user['sub'];

    query.filter = {
      ...query.filter,
      'head.id': headId + ""
    }

    return this.leaveService.datatables(query);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(LEAVE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.leaveService.datatables(query);
  }


  @Post('/qty-day')
  // @Roles(AuthRole.Employee)
  getQtyDayEmployee(@Req() req: Request, @Body() createLeaveDto: CreateLeaveDto) {
    // const employeeId = req.user['sub'];

    return this.leaveService.qtyDay(createLeaveDto.employeeId, createLeaveDto);
  }

  @Post('/admin-create')
  // @Roles(AuthRole.Employee)
  createByAdmin(@Req() req: Request, @Body() createLeaveDto: CreateLeaveAdminDto) {
    return this.leaveService.create(createLeaveDto.employeeId, createLeaveDto);
  }

  @Post()
  @Roles(AuthRole.Employee)
  create(@Req() req: Request, @Body() createLeaveDto: CreateLeaveDto) {
    const employeeId = req.user['sub'];

    return this.leaveService.create(employeeId, createLeaveDto);
  }

  @Get()
  @ApiQuery({ name: 'titleId', required: false })
  @ApiQuery({ name: 'levelId', required: false })
  @ApiQuery({ name: 'departmentId', required: false })
  @ApiQuery({ name: 'employeeTypeId', required: false })
  @ApiQuery({ name: 'workShiftId', required: false })
  @ApiQuery({ name: 'headId', required: false })
  findAll(@Query() query) {
    return this.leaveService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.leaveService.findOne(+id);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.leaveService.remove(+id);
  }

  @Put(':id/approve')
  // @Roles(AuthRole.Employee)
  updateStatus(@Req() req: Request, @Param('id', ParseIntPipe) id: string, @Body() updateStatusLeaveDto: UpdateStatusLeaveDto) {
    const employeeId = req.user['sub'];

    const role = req.body['role'];

    return this.leaveService.updateStatus(+id, updateStatusLeaveDto, employeeId, role);
  }
}


