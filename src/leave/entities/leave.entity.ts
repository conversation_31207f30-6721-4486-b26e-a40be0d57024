import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { Employee } from "../../employee/entities/employee.entity";
import { LeaveDate } from "./leave-date.entity";
import { DecimalColumnTransformer } from "../../common/utils/decimal-column-transformer";

export enum LeaveStatusEnum {
    OPEN = 'open',
    APPROVED = 'approved',
    CANCEL = 'cancel',
    REJECT = 'reject',
    HR_APPROVED = 'hr_approved'
}

export enum LeaveTypeEnum {
    HALF_DAY_MOR = 'half_day_morning',
    HALF_DAY_AFT = 'half_day_afternoon',
    FULL_DAY = 'full_day',
    CONS_FULL_DAY_AND_MOR = 'consecutive_full_day_and_morning',//1.5
    HALF_AFT_CONS = 'half_afternoon_consecutive',//1.5
    CONS_FULL_DAY_AND_BOTH_HALF = 'consecutive_full_day_and_both_half'//2
}

@Entity()
@Unique(['code'])
export class Leave extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column("date", { nullable: true })
    date: Date

    @Column({
        type: "enum",
        enum: LeaveTypeEnum,
        nullable: true
    })
    type: LeaveTypeEnum

    @Column("date", { nullable: true })
    dateStart: Date

    @Column("date", { nullable: true })
    dateEnd: Date

    @Column('int', { nullable: true })
    year: number

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    entitlementDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    takenDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    remainDay: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    excessDay: number;

    @Column("text", { nullable: true })
    reason: string;

    @Column('text', { nullable: true })
    file: string

    @Column({
        type: "enum",
        enum: LeaveStatusEnum,
        nullable: true
    })
    status: LeaveStatusEnum


    @Column("timestamp", { nullable: true })
    statusDate: Date;

    @Column('text', { nullable: true })
    statusRemark: string

    @Column('text', { nullable: true })
    hrRemark: string

    @Column({ default: true })
    active: boolean


    //employee
    @ManyToOne(() => Employee, (_) => _.leaves)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;


    //leaveType
    @ManyToOne(() => LeaveType, (_) => _.leaves)
    @JoinColumn({ name: 'leave_type_id' })
    leaveType: LeaveType;

    //head
    @ManyToOne(() => Employee, (_) => _.leaves)
    @JoinColumn({ name: 'head_id' })
    head: Employee;


    //Leave date
    @OneToMany(() => LeaveDate, (_) => _.leave)
    leaveDates: Array<LeaveDate>;
}



