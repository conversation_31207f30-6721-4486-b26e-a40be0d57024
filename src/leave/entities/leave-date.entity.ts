import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Leave } from "./leave.entity";

@Entity()
export class LeaveDate extends CustomBaseEntity {

    @Column("date", { nullable: true })
    date: Date

    @Column('float8', { nullable: true})
    qty: number;

    @Column("time", { nullable: true })
    time_start: string

    @Column("time", { nullable: true })
    time_end: string

    @Column({ default: true })
    active: boolean

    //Leave
    @ManyToOne(() => Leave, (_) => _.leaveDates, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
    @JoinColumn({ name: 'leave_id' })
    leave: Leave;
}



