import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateLeaveDto } from './dto/create-leave.dto';
import { Leave, LeaveStatusEnum, LeaveTypeEnum } from './entities/leave.entity';
import { DataSource, In, LessThanOrEqual, MoreThanOrEqual, Not, Raw, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Helper, isLeaveRequestValid } from 'src/common/utils/helper';
import { LeaveDate } from 'src/leave/entities/leave-date.entity';
import { WorkShift } from 'src/work-shift/entities/work-shift.entity';
import { Employee, EmployeeActiveEnum } from 'src/employee/entities/employee.entity';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';
import { Holiday } from 'src/holiday/entities/holiday.entity';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { UpdateStatusLeaveDto } from './dto/update-status-leave.dto';
import { LeavePermission } from 'src/leave-permission/entities/leave-permission.entity';
import { EmployeeLeavePermission } from 'src/employee-leave-permission/entities/employee-leave-permission.entity';
import { EmployeeLeavePermissionChange, EmployeeLeavePermissionChangeEnum } from 'src/employee-leave-permission/entities/employee-leave-permission-change.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { DateTime } from 'luxon';
import { chain, groupBy, sortBy } from 'lodash';
import { AuthRole } from 'src/auth/auth.interface';
import { Department } from 'src/department/entities/department.entity';

export const LEAVE_PAGINATION_CONFIG: PaginateConfig<Leave> = {
  relations: ['employee', 'leaveType', 'head', 'leaveDates'],
  sortableColumns: [
    'id',
    'code',
    'date',
    'type',
    'dateStart',
    'dateEnd',
    'qtyDay',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'leaveType.id',
    'leaveType.code',
    'leaveType.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'status',
    'statusRemark',
    'statusDate'
  ],
  select: [
    'id',
    'code',
    'date',
    'type',
    'dateStart',
    'dateEnd',
    'qtyDay',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'leaveType.id',
    'leaveType.code',
    'leaveType.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'status',
    'statusRemark',
    'statusDate',
    'file'
  ],
  searchableColumns: ['code', 'employee.code', 'employee.firstname', 'employee.lastname'],
  filterableColumns: {
    code: [FilterOperator.EQ],
    date: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    dateStart: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    dateEnd: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    status: [FilterOperator.EQ],
    'employee.id': [FilterOperator.EQ],
    'leaveType.id': [FilterOperator.EQ],
    'head.id': [FilterOperator.EQ]
  },
};
@Injectable()
export class LeaveService {
  constructor(
    @InjectRepository(Leave)
    private leaveRepository: Repository<Leave>,

    @InjectRepository(LeaveType)
    private leaveTypeRepository: Repository<LeaveType>,


    @InjectRepository(LeaveDate)
    private leaveDateRepository: Repository<LeaveDate>,

    @InjectRepository(LeavePermission)
    private leavePermissionRepository: Repository<LeavePermission>,


    @InjectRepository(WorkShift)
    private workShiftRepository: Repository<WorkShift>,

    @InjectRepository(WorkShiftTime)
    private workShiftTimeRepository: Repository<WorkShiftTime>,

    @InjectRepository(Holiday)
    private holidayRepository: Repository<Holiday>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,

    @InjectRepository(EmployeeLeavePermission)
    private employeeLeavePermissionRepository: Repository<EmployeeLeavePermission>,

    private dataSource: DataSource,
    private readonly mailerService: MailerService,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Leave>> {
    return paginate(query, this.leaveRepository, LEAVE_PAGINATION_CONFIG);
  }

  async checkLeaveOnHoliday(leaveTypeId: number, dateInPeriod: string[]) {
    const leaveType = await this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } });
    if (leaveType?.minDayAttachFile != null && dateInPeriod.length >= leaveType.minDayAttachFile) {
      return true;
    }
    return false;
  }

  async create(employeeId: number, createLeaveDto: CreateLeaveDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { leaveTypeId, headId, dateStart, dateEnd, type } = createLeaveDto;

      const [checkLeave, employee, head] = await Promise.all([
        this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } }),
        this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true, workShift: true } }),
        this.employeeRepository.findOne({ where: { id: headId } })
      ]);

      if (!checkLeave) throw new NotFoundException("leave type not found");
      if (!employee) throw new NotFoundException("employee not found");
      if (!head) throw new NotFoundException("head not found");

      //เช็ควันลาทับซ้อน
      const valid = await isLeaveRequestValid(employeeId, new Date(dateStart), new Date(dateEnd), type)
      if (!valid) {
        throw new BadRequestException("There is already a leave request for the overlapping period.");
      }

      //เช็คห้ามลาข้าม Period
      const empLeaPerm = await queryRunner.manager.findOne(EmployeeLeavePermission, {
        where: {
          startPeriod: LessThanOrEqual(new Date(dateStart)),
          endPeriod: MoreThanOrEqual(new Date(dateEnd)),
          employee: {
            id: employeeId,
          },
          leaveType: {
            id: leaveTypeId,
          }
        }
      })

      if (!empLeaPerm) {
        throw new BadRequestException("Can't create leave")        
      }

      //get last id
      const lastLeave = await Leave.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = lastLeave.length;

      //add leave
      const leaveCode = Helper.generateNo('L', lastId).toString();
      const newLeave = Leave.create({
        code: leaveCode,
        date: createLeaveDto.date,
        type: createLeaveDto.type,
        dateStart: dateStart,
        dateEnd: dateEnd,
        year: employee.totalWorkYear,
        reason: createLeaveDto.reason,
        file: createLeaveDto.file,
        status: LeaveStatusEnum.OPEN,
        leaveType: { id: leaveTypeId },
        employee: { id: employeeId },
        head: { id: headId },
      });

      // บันทึก leave
      await queryRunner.manager.save(Leave, newLeave)

      // คำนวณวันที่ลา
      const dateInPeriod = Helper.dateInPeriod(dateStart, dateEnd);

      //เชคค่อมวันหยุดกรณีลาป่วย
      const checkLeaveOnHoliday = await this.checkLeaveOnHoliday(leaveTypeId, dateInPeriod)
      if (checkLeaveOnHoliday == true && !createLeaveDto.file) {
        throw new NotFoundException("Please attach the medical certificate file due to continuous leave exceeding the limit.");
      }

      const leaveDates: LeaveDate[] = [];
      let qtyDay = 0;

      // การตรวจสอบแต่ละวันลา
      for (let i = 0; i < dateInPeriod.length; i++) {
        const indexDate = Helper.getDayOfWeek(dateInPeriod[i]).toString();

        // ตรวจสอบเวลาการทำงานและวันหยุด
        const [workShift, holiday] = await Promise.all([
          this.workShiftTimeRepository.findOne({
            relations: ['workShift'],
            where: {
              day: indexDate,
              workShift: { id: employee.workShift?.id },
            },
          }),
          this.holidayRepository.findOne({
            where: {
              date: new Date(dateInPeriod[i]),
              employeeType: {
                id: employee.employeeType?.id,
              }
            },
          }),
        ]);

        if (checkLeave.ignoreDayOff || (!holiday && workShift && workShift.active)) {
          const leaveDate = new LeaveDate();
          leaveDate.leave = newLeave;
          leaveDate.date = new Date(dateInPeriod[i]);

          if (createLeaveDto.type == LeaveTypeEnum.HALF_DAY_MOR) {
            const COUNT = 0.5

            leaveDate.time_start = workShift.time_in;
            leaveDate.time_end = workShift.break_in;

            leaveDate.qty = COUNT
            qtyDay += COUNT;

          } else if (createLeaveDto.type == LeaveTypeEnum.HALF_DAY_AFT) {
            const COUNT = 0.5

            leaveDate.time_start = workShift.break_out;
            leaveDate.time_end = workShift.time_out;

            leaveDate.qty = COUNT
            qtyDay += COUNT;
          } else if (createLeaveDto.type == LeaveTypeEnum.FULL_DAY) {
            const COUNT = 1

            leaveDate.time_start = workShift.time_in;
            leaveDate.time_end = workShift.time_out;

            leaveDate.qty = COUNT
            qtyDay += COUNT;
          } else if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_MOR) {

            if (i != dateInPeriod.length - 1) {
              const COUNT = 1

              leaveDate.time_start = workShift.time_in;
              leaveDate.time_end = workShift.time_out;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            } else {
              const COUNT = 0.5

              leaveDate.time_start = workShift.time_in;
              leaveDate.time_end = workShift.break_in;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            }
          } else if (createLeaveDto.type == LeaveTypeEnum.HALF_AFT_CONS) {
            if (i == 0) {
              const COUNT = 0.5
              leaveDate.time_start = workShift.break_out;
              leaveDate.time_end = workShift.time_out;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            } else {
              const COUNT = 1

              leaveDate.time_start = workShift.time_in;
              leaveDate.time_end = workShift.time_out;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            }
          } else if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_BOTH_HALF) {
            if (i == 0) {
              const COUNT = 0.5

              leaveDate.time_start = workShift.time_in;
              leaveDate.time_end = workShift.break_in;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            } else if (i != dateInPeriod.length - 1) {
              const COUNT = 0.5

              leaveDate.time_start = workShift.break_out;
              leaveDate.time_end = workShift.time_out;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            } else {
              const COUNT = 1

              leaveDate.time_start = workShift.time_in;
              leaveDate.time_end = workShift.time_out;

              leaveDate.qty = COUNT
              qtyDay += COUNT;
            }
          } else {
            throw new BadRequestException('Choice Invalid')
          }

          leaveDates.push(leaveDate);
        }
      }

      // บันทึก leave dates ทั้งหมด
      if (leaveDates.length > 0) {
        await queryRunner.manager.save(LeaveDate, leaveDates)
      }

      await queryRunner.manager.increment(EmployeeLeavePermission, { id: empLeaPerm.id }, 'usedDay', qtyDay);

      const remain = empLeaPerm.qtyDay - (empLeaPerm.usedDay + qtyDay) + empLeaPerm.excessDay

      // อัปเดต qtyDay ในเอกสาร leave
      await queryRunner.manager.update(Leave, newLeave.id, {
        qtyDay: qtyDay,//จำนวนที่ลา
        takenDay: empLeaPerm.usedDay + qtyDay,//จำนวนที่ใช้ไป
        entitlementDay: empLeaPerm.qtyDay,//สิทธิการลา
        remainDay: remain,//คงเหลือ
        excessDay: empLeaPerm.excessDay,//excess
      });


      await queryRunner.commitTransaction();

      //sent email to Approver
      this.mailerService.sendMail({
        to: head.email,
        subject: 'Leave Application',
        template: 'leave-request',
        context: {
          fullname: employee.fullname,
          leaveType: checkLeave.name,
          start: DateTime.fromISO(dateStart).toFormat('dd/MM/yyyy'),
          end: DateTime.fromISO(dateEnd).toFormat('dd/MM/yyyy'),
          total: qtyDay,
          reason: newLeave?.reason,
          // web: process.env.WEB_URL + '/leave/leave-approval',
          web: process.env.WEB_URL + '/sign-in',
        }
      })

      //senf eamil to Requester
      this.mailerService.sendMail({
        to: employee.email,
        subject: 'Leave Application',
        template: 'leave-approve',
        context: {}
      }).catch((emailErr) => {
        console.error('Failed to send email:', employee.email, emailErr);
      });

      return newLeave;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async qtyDay(employeeId: number, createLeaveDto: CreateLeaveDto) {
    const { leaveTypeId, headId, dateStart, dateEnd, type } = createLeaveDto;

    const [checkLeave, employee, head] = await Promise.all([
      this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } }),
      this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true, workShift: true } }),
      this.employeeRepository.findOne({ where: { id: headId } })
    ]);

    if (!checkLeave) throw new NotFoundException("leave type not found");
    if (!employee) throw new NotFoundException("employee not found");
    if (!head) throw new NotFoundException("head not found");

    //เช็ควันลาทับซ้อน
    const valid = await isLeaveRequestValid(employeeId, new Date(dateStart), new Date(dateEnd),type)
    if (!valid) {
      throw new BadRequestException("There is already a leave request for the overlapping period");
    }

    // คำนวณวันที่ลา
    const dateInPeriod = Helper.dateInPeriod(dateStart, dateEnd);

    //เชคค่อมวันหยุดกรณีลาป่วย
    const checkLeaveOnHoliday = await this.checkLeaveOnHoliday(leaveTypeId, dateInPeriod)

    if (checkLeaveOnHoliday == true && !createLeaveDto.file) {
      throw new NotFoundException("Please attach the medical certificate file due to continuous leave exceeding the limit.");
    }

    let qtyDay = 0;

    // การตรวจสอบแต่ละวันลา
    for (let i = 0; i < dateInPeriod.length; i++) {
      const indexDate = Helper.getDayOfWeek(dateInPeriod[i]).toString();

      if (checkLeave.ignoreDayOff) {

        qtyDay = this.calculateQtyDay(createLeaveDto, qtyDay, i, dateInPeriod);

      } else {
        // ตรวจสอบเวลาการทำงานและวันหยุด
        const [workShift, holiday] = await Promise.all([
          this.workShiftTimeRepository.findOne({
            relations: ['workShift'],
            where: {
              day: indexDate,
              workShift: { id: employee.workShift.id },
            },
          }),
          this.holidayRepository.findOne({
            where: {
              date: new Date(dateInPeriod[i]),
              employeeType: {
                id: employee.employeeType.id,
              }
            },
          }),
        ]);

        if (!holiday && workShift && workShift.active) {
          qtyDay = this.calculateQtyDay(createLeaveDto, qtyDay, i, dateInPeriod);
        }
      }
    }

    // CONS_FULL_DAY_AND_MOR = 'consecutive_full_day_and_morning',//1.5
    // HALF_AFT_CONS = 'half_afternoon_consecutive',//1.5
    // CONS_FULL_DAY_AND_BOTH_HALF = 'consecutive_full_day_and_both_half'//2
    if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_MOR && qtyDay != 1.5) {
      throw new BadRequestException('This option requires at 2 consecutive days.');
    } else if (createLeaveDto.type == LeaveTypeEnum.HALF_AFT_CONS && qtyDay != 1.5) {
      throw new BadRequestException('This option requires at 2 consecutive days.');
    } else if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_BOTH_HALF && qtyDay != 2) {
      throw new BadRequestException('This option requires at 3 consecutive days.');
    }

    return qtyDay;
  }

  private calculateQtyDay(createLeaveDto: CreateLeaveDto, qtyDay: number, i: number, dateInPeriod: string[]) {
    if (createLeaveDto.type == LeaveTypeEnum.HALF_DAY_MOR) {
      qtyDay += 0.5;
    } else if (createLeaveDto.type == LeaveTypeEnum.HALF_DAY_AFT) {
      qtyDay += 0.5;
    } else if (createLeaveDto.type == LeaveTypeEnum.FULL_DAY) {
      qtyDay += 1;
    } else if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_MOR) {

      if (i != dateInPeriod.length - 1) {
        qtyDay += 1;
      } else {
        qtyDay += 0.5;
      }
    } else if (createLeaveDto.type == LeaveTypeEnum.HALF_AFT_CONS) {
      if (i == 0) {
        qtyDay += 0.5;
      } else {
        qtyDay += 1;
      }
    } else if (createLeaveDto.type == LeaveTypeEnum.CONS_FULL_DAY_AND_BOTH_HALF) {
      if (i == 0) {
        qtyDay += 0.5;
      } else if (i != dateInPeriod.length - 1) {
        qtyDay += 0.5;
      } else {
        qtyDay += 1;
      }
    } else {
      throw new BadRequestException('Choice Invalid');
    }
    return qtyDay;
  }

  findAll(query) {
    const leaveTypeId = query.leaveTypeId
    const employeeId = query.employeeId
    const headId = query.headId

    return this.leaveRepository.find({
      relations: ['employee', 'leaveType', 'head', 'leaveDates'],
      where: {
        leaveType: {
          id: leaveTypeId ? leaveTypeId : null,
        },
        employee: {
          id: employeeId ? employeeId : null,
        },
        head: {
          id: headId ? headId : null,
        },
      }
    });
  }



  async findOne(id: number) {
    const item = await this.leaveRepository.findOne({
      relations: ['employee', 'leaveType', 'head', 'leaveDates'],
      where: { id }
    });

    if (!item) throw new NotFoundException("leave request not found");

    return item;
  }

  // async update(id: number, updateLeaveDto: UpdateLeaveDto) {

  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {

  //     const item = await this.findOneById(id);
  //     if (!item) throw new NotFoundException("employee not found");

  //     const { leaveTypeId, employeeId, headId } = updateLeaveDto;

  //     // เช็คข้อมูล leaveType, employee, head พร้อมกัน
  //     const [checkLeave, employee, head] = await Promise.all([
  //       this.leaveTypeRepository.findOne({ where: { id: leaveTypeId } }),
  //       this.employeeRepository.findOne({ where: { id: employeeId } }),
  //       this.employeeRepository.findOne({ where: { id: headId } })
  //     ]);

  //     // ตรวจสอบว่าเจอข้อมูลหรือไม่
  //     if (!checkLeave) throw new NotFoundException("leave type not found");
  //     if (!employee) throw new NotFoundException("employee not found");
  //     if (!head) throw new NotFoundException("head not found");

  //     // อัปเดตข้อมูล leave
  //     await this.leaveRepository.update(id, {
  //       date: updateLeaveDto.date,
  //       type: updateLeaveDto.type,
  //       dateStart: updateLeaveDto.dateStart,
  //       dateEnd: updateLeaveDto.dateEnd,
  //       reason: updateLeaveDto.reason,
  //       file: updateLeaveDto.file,
  //       leaveType: { id: leaveTypeId },
  //       employee: { id: employeeId },
  //       head: { id: headId },
  //     });

  //     // ลบ leave dates ทั้งหมดในครั้งเดียว
  //     const deletePromises = item.leaveDates.map((leaveDate) =>
  //       this.leaveDateRepository.softDelete(leaveDate.id)
  //     );
  //     await Promise.all(deletePromises);

  //     // คำนวณวันที่ลา
  //     const dateInPeriod = Helper.dateInPeriod(updateLeaveDto.dateStart, updateLeaveDto.dateEnd);
  //     const leaveDates: LeaveDate[] = [];
  //     let qtyDay = 0;

  //     // การตรวจสอบและสร้าง leaveDate สำหรับแต่ละวันลา
  //     const leaveDatePromises = dateInPeriod.map(async (date) => {
  //       const indexDate = Helper.getDayOfWeek(date).toString();

  //       // ตรวจสอบวันหยุดและเวลาทำงาน
  //       const [workShift, holiday] = await Promise.all([
  //         this.workShiftTimeRepository.findOne({
  //           relations: ['workShift'],
  //           where: {
  //             day: indexDate,
  //             workShift: { id: employee.workShift?.id },
  //           },
  //         }),
  //         this.holidayRepository.findOne({ where: { date: new Date(date) } }),
  //       ]);

  //       if (!holiday && workShift && workShift.active) {
  //         const leaveDate = new LeaveDate();
  //         leaveDate.leave = item;
  //         leaveDate.date = new Date(date);
  //         leaveDate.time_start = workShift.time_in;
  //         leaveDate.time_end = workShift.time_out;

  //         leaveDates.push(leaveDate);
  //         qtyDay++;
  //       }
  //     });

  //     // รอให้สร้าง leaveDate เสร็จทั้งหมด
  //     await Promise.all(leaveDatePromises);

  //     // บันทึก leave dates ทั้งหมดในครั้งเดียว
  //     if (leaveDates.length > 0) {
  //       await queryRunner.manager.save(leaveDates)
  //     }

  //     // อัปเดต qtyDay ในเอกสาร leave
  //     await this.leaveRepository.update(id, { qtyDay });

  //     await queryRunner.commitTransaction();

  //     return item;

  //   } catch (error) {
  //     await queryRunner.rollbackTransaction();
  //     return error.response
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }


  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("employee not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.leaveRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.leaveRepository.findOne({
      relations: ['employee', 'leaveType', 'head', 'leaveDates'],
      where: { id }
    });
  }


  async updateStatus(id: number, updateStatusLeaveDto: UpdateStatusLeaveDto, headId: number, role: AuthRole) {

    const { status, statusRemark, hrRemark } = updateStatusLeaveDto;

    const leave = await this.leaveRepository.findOne({
      where: { id },
      relations: {
        leaveDates: true,
        leaveType: true,
        employee: true,
        head: true
      }
    });

    if (!leave) {
      throw new NotFoundException('Leave not found');
    }

    if (leave.status == LeaveStatusEnum.CANCEL || leave.status == LeaveStatusEnum.REJECT) {
      throw new BadRequestException('Don\'t change leave status');
    }

    //find employee leave permission
    const employeeLeavePermission = await EmployeeLeavePermission.findOne({
      where: {
        startPeriod: LessThanOrEqual(leave.dateStart),
        endPeriod: MoreThanOrEqual(leave.dateEnd),
        employee: {
          id: leave.employee.id,
        },
        leaveType: {
          id: leave.leaveType.id,
        }
      }
    });

    if (!employeeLeavePermission) {
      throw new NotFoundException('Leave quota not found')
    }

    // //check if remaining
    // const remain = employeeLeavePermission.qtyDay + employeeLeavePermission.excessDay - employeeLeavePermission.usedDay;
    // if (remain < leave.qtyDay) {
    //   throw new BadRequestException("Insufficient leave remaining")
    // }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      if (status === LeaveStatusEnum.APPROVED || status === LeaveStatusEnum.REJECT) {
        if (leave.head.id != headId) {
          throw new BadRequestException("You are not an approver.")
        }

        if (status === LeaveStatusEnum.REJECT) {
          await queryRunner.manager.decrement(EmployeeLeavePermission,
            { id: employeeLeavePermission.id },
            'usedDay', leave.qtyDay
          );
        }

        await queryRunner.manager.update(Leave, leave.id, {
          status,
          statusRemark,
          statusDate: new Date(),
        });

        // Send email in the background
        const emailTemplate = status === LeaveStatusEnum.APPROVED ? 'confirm' : 'reject';
        this.mailerService.sendMail({
          to: leave.employee?.email,
          subject: 'Leave Application',
          template: emailTemplate,
          context: {},
        }).catch((emailErr) => {
          console.error('Failed to send email:', emailErr);
        });

      } else if (status == LeaveStatusEnum.CANCEL) {
        await queryRunner.manager.decrement(EmployeeLeavePermission,
          { id: employeeLeavePermission.id },
          'usedDay', leave.qtyDay
        );

        await queryRunner.manager.update(Leave, leave.id, { status, hrRemark, statusRemark });

      } else {
        throw new BadRequestException('Status not supported')
      }

      await queryRunner.commitTransaction();

      return { message: 'ok' }
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }


  async getLeavePermissionEmployee(employeeId: number, leaveTypeId: number) {
    let qtyLeave = 0
    let qtyPermissionLeaveDay = 0
    const employee = await this.employeeRepository.findOne({
      relations: ['level.levelType'],
      where: { id: employeeId }
    })

    if (employee && employee.level && employee.level.levelType) {
      const dateRegister = new Date(employee.registerDate)
      const currentDate = new Date();

      const workYear = employee.totalWorkYear;

      if (workYear < 1) {
        //age work less than 1 year
        const workMonth = Helper.calculateMonthsDifference(dateRegister, currentDate);
        qtyPermissionLeaveDay = workMonth

      } else {
        //1 year ++

        //get leave permission
        const leavePermission = await this.leavePermissionRepository.findOne({
          relations: ['levelType', 'leaveType'],
          where: {
            leaveType: { id: leaveTypeId },
            levelType: { id: employee.level.levelType.id },
            ageWork: LessThanOrEqual(workYear),
          },
          order: { ageWork: 'DESC' },
        });

        if (leavePermission) {
          qtyPermissionLeaveDay = leavePermission.qtyDay
        }
      }

      //get leave
      const leaves = await this.leaveRepository.find({
        where: {
          status: LeaveStatusEnum.APPROVED,
          employee: {
            id: employeeId,
          },
        },
      });

      const totalQtyDay = leaves.reduce((sum, leave) => sum + (leave.qtyDay || 0), 0);

      qtyLeave = qtyPermissionLeaveDay - totalQtyDay

    }

    return qtyLeave
  }

  async testEmail() {
    // Send email in the background
    await this.mailerService.sendMail({
      to: '<EMAIL>',
      subject: 'Leave Application',
      template: 'resend',
      context: {
        fullname: 'Anon Test'
      },
    }).catch((emailErr) => {
      console.error('Failed to send email:', emailErr);
    });
  }

  async reportOfApprover(headId: number) {
    const employees = await Employee.find({
      where: {
        head: {
          id: headId,
        },
        active: Not(EmployeeActiveEnum.RES)
      },
      relations: {
        employeeLeavePermissions: {
          leaveType: true
        },
        department: true
      },
      order: {
        firstname: 'ASC',
        lastname: 'ASC',
        employeeLeavePermissions: {
          leaveType: {
            code: 'ASC'
          },
          year: 'DESC'
        }
      }
    });

    const allLeaveTypes = await LeaveType.find({ where: { active: true }, select: ['code', 'name'], order: { code: 'ASC' } })
    const flatAllLeaveTypes = allLeaveTypes.map((e) => ({ code: e.code, name: e.name, }));

    const result = [];
    for (const employee of employees) {
      const emp = {
        id: employee.id,
        fullName: employee.fullname,
        code: employee.code,
        department: employee?.department?.name,
        start: '0000-00-00',
        end: '0000-00-00',
        leaveType: []
      }

      const leaveType = employee.employeeLeavePermissions.filter(e => e.year == employee.totalWorkYear)

      if (leaveType.length > 0) {
        emp.start = leaveType[0].startPeriod.toString()
        emp.end = leaveType[0].endPeriod.toString()
      }

      emp.leaveType = leaveType.map(e => ({
        code: e?.leaveType?.code,
        name: e?.leaveType?.name,
        entitlement: e.qtyDay,
        taken: e.usedDay,
        remain: e.qtyDay - e.usedDay,
        excess: e.excessDay
      }))

      const leaveNotInEmployees = flatAllLeaveTypes.filter(leaveName => !emp.leaveType.some((l) => l.name === leaveName.name))

      for (const leaveNotInEmployee of leaveNotInEmployees) {
        const noDataLeave = {
          code: leaveNotInEmployee.code,
          name: leaveNotInEmployee.name,
          entitlement: 0,
          taken: 0,
          remain: 0,
          excess: 0
        }

        emp.leaveType.push(noDataLeave)
      }

      emp.leaveType = sortBy(emp.leaveType, 'code')

      result.push(emp)
    }

    const groupByDept = chain(result)
      .groupBy((emp) => emp.department)
      .map((v, k) => {
        return {
          department: k,
          staff: v
        }
      });

    return groupByDept
  }

  async leaveCancel(id: number, hrRemark: string) {
    const leave = await Leave.findOne({
      where: { id },
      relations: {
        employee: true,
      }
    });
  }


  async getLeaveUseQty(employeeId: number, dateStart: Date, dateEnd: Date) {

    const leaves = await this.leaveRepository.find({
      where: [
        {
          employee: {
            id: employeeId,
          },
          // กรองกรณีที่วันที่เริ่มต้นและสิ้นสุดอยู่ในช่วงที่ระบุ
          dateStart: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
          dateEnd: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
          status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
        },
        {
          employee: {
            id: employeeId,
          },
          // กรองกรณีที่วันที่เริ่มต้นก่อนและสิ้นสุดหลังช่วงเวลาที่ระบุ
          dateStart: Raw(alias => `${alias} <= :dateStart`, { dateStart }),
          dateEnd: Raw(alias => `${alias} >= :dateEnd`, { dateEnd }),
          status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
        },
      ],
    });

    let qty = 0
    let excess = 0
    for (const leave of leaves) {
      qty += leave.qtyDay
      excess += leave.excessDay
    }

    let data = {
      qty: qty,
      excess: excess,
    }

    return data
  }

  async getLeavePermissionRemain(employeeId: number, leaveTypeId: number, year: number, qtyCurrentLeave: number = 0, filterDateStart: Date = null, filterDateEnd: Date = null) {

    let taken = 0
    let excessCurrentLeave = 0

    let accumulateLeave = 0
    let entitlementDay = 0
    let remainDay = 0
    let excessDay = 0

    //qty
    let whereConditions: any = {
      employee: {
        id: employeeId
      },
    };
    if (leaveTypeId) {
      whereConditions = {
        ...whereConditions,
        leaveType: {
          id: leaveTypeId
        },
      };
    }

    if (year) {
      whereConditions = {
        ...whereConditions,
        // startPeriod: Raw(alias => `to_char(${alias}, 'YYYY') LIKE :year`, { year: `${year}%` }),
      };
    }

    const employeeLeavePermission = await this.employeeLeavePermissionRepository.findOne({
      relations: ['leaveType'],
      where: whereConditions,
      order: {
        id: 'DESC'
      }
    });

    if (employeeLeavePermission) {

      let startPeriod = employeeLeavePermission.startPeriod
      let endPeriod = employeeLeavePermission.endPeriod

      if (filterDateStart && filterDateEnd) {
        startPeriod = filterDateStart
        endPeriod = filterDateEnd
      }

      //qty_leaved
      const getLeaveUseQty = await this.getLeaveUseQty(employeeId, startPeriod, endPeriod)
      taken = getLeaveUseQty.qty
      excessCurrentLeave = getLeaveUseQty.excess

      entitlementDay = employeeLeavePermission.qtyDay + employeeLeavePermission.excessDay //permissionLeave + excess
      remainDay = entitlementDay - (taken + qtyCurrentLeave)
      excessDay = employeeLeavePermission.excessDay
    }

    let data = {
      entitlementDay: entitlementDay || 0, //สิทธิการลา
      excessDay: excessDay || 0,//excess
      qtyDay: qtyCurrentLeave || 0,//จำนวนที่ลาปัจจุบัน
      takenDay: taken || 0,//จำนวนที่ลาที่ใช้ไปแล้ว
      accumulateLeaveDay: (taken + qtyCurrentLeave) || 0,//จำนวนที่ลาสะสม
      remainDay: remainDay || 0,//คงเหลือ

      excessCurrentLeaveDay: excessCurrentLeave //จำนวนวันที่ได้ excess ปัจจุบัน
    };

    return data
  }


}


