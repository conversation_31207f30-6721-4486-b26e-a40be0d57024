import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateHolidayDto } from './dto/create-holiday.dto';
import { UpdateHolidayDto } from './dto/update-holiday.dto';
import { Holiday } from './entities/holiday.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const HOLIDAY_PAGINATION_CONFIG: PaginateConfig<Holiday> = {
  sortableColumns: ['id', 'date', 'name', 'active'],
  select: ['id', 'date', 'name', 'active', 'employeeType.id', 'employeeType.code', 'employeeType.name', 'createdAt'],
  searchableColumns: ['name'],
  relations: {
    employeeType: true
  },
  filterableColumns: {
    'employeeType.id': [FilterOperator.EQ]
  }
};
@Injectable()
export class HolidayService {
  constructor(
    @InjectRepository(Holiday)
    private holidayRepository: Repository<Holiday>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Holiday>> {
    return paginate(query, this.holidayRepository, HOLIDAY_PAGINATION_CONFIG);
  }

  async create(createHolidayDto: CreateHolidayDto) {
    const dateJs = new Date(createHolidayDto.date)

    //check holiday date exist
    const check = await Holiday.existsBy({
      date: dateJs
    })
    if (check) {
      throw new BadRequestException('holiday date already.')
    }

    const item = this.holidayRepository.create(
      {
        ...createHolidayDto,
        date: dateJs,
        employeeType: {
          id: createHolidayDto.employeeTypeId,
        }
      });

    return this.holidayRepository.save(item);
  }

  findAll() {
    return this.holidayRepository.find();
  }

  async findOne(id: number) {
    const item = await this.holidayRepository.findOne({ where: { id }, relations: { employeeType: true } });

    if (!item) throw new NotFoundException("holiday not found");

    return item;
  }

  async update(id: number, updateHolidayDto: UpdateHolidayDto) {
    const dateJs = new Date(updateHolidayDto.date)

    //check holiday date exist
    const check = await Holiday.existsBy({
      date: dateJs,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Holiday date already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("holiday not found");

    const { employeeTypeId, ...data } = updateHolidayDto;

    return this.holidayRepository.update(id, {
      ...data,
      employeeType: {
        id: employeeTypeId,
      }
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("holiday not found");

    await this.holidayRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.holidayRepository.findOne({ where: { id } });
  }
}

