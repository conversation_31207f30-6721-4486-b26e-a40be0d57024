import { Column, Entity, Index, ManyToOne, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { EmployeeType } from "../../employee-type/entities/employee-type.entity";

@Entity()
export class Holiday extends CustomBaseEntity {
    @Column("date", { nullable: true })
    date: Date;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    @ManyToOne(() => EmployeeType, (_) => _.holidays)
    employeeType: EmployeeType;
}

