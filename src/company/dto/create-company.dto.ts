import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { CompanyCategory } from "src/company_category/entities/company_category.entity";
export class CreateCompanyDto {
    // @IsNotEmpty()
    // @ApiProperty()
    // readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;


    @ApiProperty()
    readonly building: string;

    @ApiProperty()
    readonly street: string;

    @ApiProperty()
    readonly district: string;

    @ApiProperty()
    readonly city: string;

    @ApiProperty()
    readonly postCode: string;

    @ApiProperty()
    readonly country: string;

    @ApiProperty()
    readonly phoneNumber: string;

    @ApiProperty()
    readonly faxNumber: string;

    @ApiProperty()
    readonly email: string;

    @ApiProperty()
    readonly webSite: string;

    @ApiProperty()
    readonly companyCategory: CompanyCategory[];
}





