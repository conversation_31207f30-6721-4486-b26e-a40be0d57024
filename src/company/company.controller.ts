
import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus, Query } from '@nestjs/common';

import { COMPANY_PAGINATION_CONFIG } from './company.service';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AuthRole } from 'src/auth/auth.interface';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { CheckNameCompanyDto } from './dto/check-name-company.dto';


@Controller('company')
@ApiTags('บริษัท')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(COMPANY_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.companyService.datatables(query);
  }

  @Post('/check_duplicate_company')
  @Roles(AuthRole.Admin)
  checkDuplicate(@Body() checkNameCompanyDto: CheckNameCompanyDto) {
    return this.companyService.checkDuplicate(checkNameCompanyDto);
  }

  @Post()
  @Roles(AuthRole.Admin)
  create(@Body() createCompanyDto: CreateCompanyDto) {
    return this.companyService.create(createCompanyDto);
  }

  @Get()
  @ApiQuery({ name: 'categoryId', required: false })
  findAll(@Query() query) {
    return this.companyService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.companyService.findOne(+id);
  }

  @Put(':id')
  @Roles(AuthRole.Admin)
  update(@Param('id', ParseIntPipe) id: string, @Body() updateCompanyDto: UpdateCompanyDto) {
    return this.companyService.update(+id, updateCompanyDto);
  }

  @Delete(':id')
  @Roles(AuthRole.Admin)
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.companyService.remove(+id);
  }
}



