import { Module } from '@nestjs/common';
import { CompanyService } from './company.service';
import { CompanyController } from './company.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from './entities/company.entity';
import { Category } from 'src/category/entities/category.entity';
import { CompanyCategory } from 'src/company_category/entities/company_category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Company, Category, CompanyCategory])],
  controllers: [CompanyController],
  providers: [CompanyService],
})
export class CompanyModule { }
