import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { CompanyCategory } from "src/company_category/entities/company_category.entity";
import { Contact } from "src/contact/entities/contact.entity";

@Entity()
@Unique(['code'])
export class Company extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column("text", { nullable: true })
    building: string;

    @Column("text", { nullable: true })
    street: string;

    @Column("text", { nullable: true })
    district: string;

    @Column("text", { nullable: true })
    city: string;

    @Column("text", { nullable: true })
    postCode: string;

    @Column("text", { nullable: true })
    country: string;

    @Column("text", { nullable: true })
    phoneNumber: string;

    @Column("text", { nullable: true })
    faxNumber: string;

    @Column("text", { nullable: true })
    email: string;

    @Column("text", { nullable: true })
    webSite: string;


    @Column({ default: true })
    active: boolean

    //CompanyCategory
    @OneToMany(() => CompanyCategory, (_) => _.company)
    companyCategories: Array<CompanyCategory>;


    //Contact
    @OneToMany(() => Contact, (_) => _.company)
    contacts: Array<Contact>;


}






