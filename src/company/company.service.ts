import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { Company } from './entities/company.entity';
import { DataSource, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Category } from 'src/category/entities/category.entity';
import { CompanyCategory } from 'src/company_category/entities/company_category.entity';
import { Helper } from 'src/common/utils/helper';
import { CheckNameCompanyDto } from './dto/check-name-company.dto';

export const COMPANY_PAGINATION_CONFIG: PaginateConfig<Company> = {
  relations: ['companyCategories.category'],
  sortableColumns: ['id', 'code', 'name', 'building', 'street', 'district', 'city', 'postCode', 'country', 'phoneNumber', 'faxNumber', 'email', 'webSite', 'active'],
  select: ['id', 'code', 'name', 'building', 'street', 'district', 'city', 'postCode', 'country', 'phoneNumber', 'faxNumber', 'email', 'webSite', 'active', 'createdAt'],
  searchableColumns: ['id', 'code', 'name', 'building', 'street', 'district', 'city', 'postCode', 'country', 'phoneNumber', 'faxNumber', 'email', 'webSite'],
  filterableColumns: {
    status: [FilterOperator.EQ],
    'companyCategories.category.id': [FilterOperator.EQ]
  },
};

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,

    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,

    @InjectRepository(CompanyCategory)
    private companyCategoryRepository: Repository<CompanyCategory>,

    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Company>> {
    return paginate(query, this.companyRepository, COMPANY_PAGINATION_CONFIG);
  }

  async checkDuplicate(checkNameCompanyDto: CheckNameCompanyDto) {

    //check project code exist
    const check = await Company.existsBy({
      name: checkNameCompanyDto?.name
    })
    if (check) {
      throw new BadRequestException('company is already.')
    }

    return ''

  }

  async create(createCompanyDto: CreateCompanyDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      //check project code exist
      const check = await Company.existsBy({
        name: createCompanyDto?.name
      })
      if (check) {
        throw new BadRequestException('company is already.')
      }

      const { ...data } = createCompanyDto;

      //get last id
      const last = await Company.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = last.length;
      const Code = Helper.generateNo('CO', lastId).toString();
      //

      const item = this.companyRepository.create(
        {
          ...data,
          code: Code
        });

      await queryRunner.manager.save(item)

      //add companyCategory
      for (let i = 0; i < createCompanyDto.companyCategory.length; i++) {

        const category = await this.categoryRepository.findOne({
          where: {
            id: createCompanyDto.companyCategory[i].id,
          }
        });
        if (!category) {
          throw new BadRequestException('category is not found')
        }

        const companyCategory = new CompanyCategory();

        companyCategory.company = item //company
        companyCategory.category = category

        await queryRunner.manager.save(companyCategory)
      }


      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll(query) {

    const categoryId = query.categoryId;

    return this.companyRepository.find({
      relations: ['companyCategories.category'],
      where: {
        active: true,
        companyCategories: {
          category: {
            id: categoryId ? categoryId : null,
          }
        }
      },
    });
  }

  async findOne(id: number) {
    const item = await this.companyRepository.findOne({
      relations: ['companyCategories.category', 'contacts'],
      where: {
        id: id
      }
    });

    if (!item) throw new NotFoundException("company not found");

    return item;
  }

  async update(id: number, updateCompanyDto: UpdateCompanyDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      //check project code exist
      const check = await Company.existsBy({
        name: updateCompanyDto?.name,
        id: Not(id)
      })
      if (check) {
        throw new BadRequestException('company is already.')
      }

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("company not found");

      const { ...data } = updateCompanyDto;

      await queryRunner.manager.update(Company, { id: item.id }, { ...data });


      //update  companyCategory

      //dell
      const getCompanyCategory = await this.companyCategoryRepository.find({
        where: {
          company: {
            id: id
          }
        }
      });
      if (getCompanyCategory) {
        getCompanyCategory.forEach(async (item) => {
          const deleteResponse = await this.companyCategoryRepository.softDelete(item.id);
        });
      }

      //add
      for (let i = 0; i < updateCompanyDto.companyCategory.length; i++) {

        const category = await this.categoryRepository.findOne({
          where: {
            id: updateCompanyDto.companyCategory[i].id,
          }
        });
        if (!category) {
          throw new BadRequestException('category is not found')
        }

        const companyCategory = new CompanyCategory();

        companyCategory.company = item //company
        companyCategory.category = category

        await queryRunner.manager.save(companyCategory)
      }
      //

      await queryRunner.commitTransaction();
      return item;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("company not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.companyRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.companyRepository.findOne({
      relations: ['companyCategories.category'],
      where: { id }
    });
  }
}



