import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { Job } from './entities/job.entity';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';

export const JOB_PAGINATION_CONFIG: PaginateConfig<Job> = {
  sortableColumns: ['id', 'title', 'education', 'location', 'experience', 'jobType', 'salary'],
  // select: ['id', 'code', 'name','active', 'createdAt'],
  searchableColumns: ['title', 'location'],
  relations: {
    candidates: {
      applicant: true
    },
    personalForm: true
  }
};

@Injectable()
export class JobService {
  async create(createJobDto: CreateJobDto) {
    const job = Job.create({
      ...createJobDto
    })

    await job.save();

    return job;
  }

  findAll() {
    return Job.find();
  }

  async findOne(id: number) {
    const job = await Job.findOne({
      where: {
        id: id
      }
    })

    if (!job) {
      throw new NotFoundException('Job not found.')
    }

    return job
  }

  async update(id: number, updateJobDto: UpdateJobDto) {
    const job = await Job.findOne({
      where: {
        id: id
      }
    })

    if (!job) {
      throw new NotFoundException('Job not found.')
    }

    const update = Job.create({
      ...updateJobDto
    })

    await Job.update(id, update)

    return this.findOne(id);
  }

  async remove(id: number) {
    const job = await Job.findOne({
      where: {
        id: id
      }
    })

    if (!job) {
      throw new NotFoundException('Job not found.')
    }

    await job.softRemove();
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Job>> {
    return paginate(query, Job.getRepository(), JOB_PAGINATION_CONFIG);
  }

  async open() {
    const job = await Job.find({
      select: ['id', 'title', 'education', 'salary', 'experience', 'location', 'jobType', 'jobOpen'],
      where: {
        active: true
      }
    });

    return job
  }
}
