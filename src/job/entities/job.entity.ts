import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToOne, OneToMany } from "typeorm";
import { Candidate } from "src/candidate/entities/candidate.entity";
import { PersonalForm } from "src/personal-form/entities/personal-form.entity";

@Entity()
export class Job extends CustomBaseEntity {
  @Column()
  title: string;

  @Column({ nullable: true })
  education: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('text', { nullable: true })
  responsibility: string;

  @Column('text', { nullable: true })
  qualification: string;

  @Column('text', { nullable: true })
  benefits: string;

  @Column({ nullable: true })
  experience: string;

  @Column({ nullable: true })
  salary: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  jobType: string;

  @Column('date', { nullable: true })
  jobOpen: string;

  @Column({ default: true })
  active: boolean

  @OneToMany(() => Candidate, (_) => _.job)
  candidates: Candidate[];

  @ManyToOne(() => PersonalForm, (_) => _.jobs, { onDelete: 'CASCADE' })
  personalForm: PersonalForm;
}
