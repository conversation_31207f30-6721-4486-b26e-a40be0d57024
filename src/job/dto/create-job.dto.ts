import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateJobDto {
  @IsNotEmpty()
  readonly title: string;

  // @IsNotEmpty()
  // readonly shotDescription: string;

  // @IsNotEmpty()

  // readonly education: string;

  // readonly description: string;

  readonly responsibility: string;

  readonly qualification: string;

  readonly benefits: string;

  // readonly experience: string;

  // readonly salary: string;

  // readonly location: string;

  readonly jobType: string;

  readonly active: boolean;

  @ApiProperty({ type: Date, format: 'date' })
  readonly jobOpen: string;
}
