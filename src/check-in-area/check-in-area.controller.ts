import { Controller, Get, Post, Body, Patch, Param, Delete, Put, HttpCode, HttpStatus } from '@nestjs/common';
import { CHECK_IN_AREA_PAGINATION_CONFIG, CheckInAreaService } from './check-in-area.service';
import { CreateCheckInAreaDto } from './dto/create-check-in-area.dto';
import { UpdateCheckInAreaDto } from './dto/update-check-in-area.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiTags } from '@nestjs/swagger';

@Controller('check-in-area')
@Auth()
@ApiTags('พื้น Check In')
export class CheckInAreaController {
  constructor(private readonly checkInAreaService: CheckInAreaService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CHECK_IN_AREA_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.checkInAreaService.datatables(query);
  }

  @Post()
  create(@Body() createCheckInAreaDto: CreateCheckInAreaDto) {
    return this.checkInAreaService.create(createCheckInAreaDto);
  }

  @Get()
  findAll() {
    return this.checkInAreaService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.checkInAreaService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCheckInAreaDto: UpdateCheckInAreaDto) {
    return this.checkInAreaService.update(+id, updateCheckInAreaDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.checkInAreaService.remove(+id);
  }
}
