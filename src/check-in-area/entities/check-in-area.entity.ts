import { CustomBaseEntity } from "src/common/entities";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";
import { Column, Entity } from "typeorm";

@Entity()
export class CheckInArea extends CustomBaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column("numeric", { transformer: new DecimalColumnTransformer() })
  lat: number;

  @Column("numeric", { transformer: new DecimalColumnTransformer() })
  lon: number;

  @Column({ default: 1000 })
  area: number;

  @Column({ default: true })
  active: boolean
}
