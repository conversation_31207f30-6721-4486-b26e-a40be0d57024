import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCheckInAreaDto } from './dto/create-check-in-area.dto';
import { UpdateCheckInAreaDto } from './dto/update-check-in-area.dto';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { CheckInArea } from './entities/check-in-area.entity';
import { Not } from 'typeorm';

export const CHECK_IN_AREA_PAGINATION_CONFIG: PaginateConfig<CheckInArea> = {
  sortableColumns: ['id', 'code', 'name', 'lat', 'lon', 'area'],
  // select: ['id', 'code', 'name', 'active', 'createdAt'],
  searchableColumns: ['name']
};

@Injectable()
export class CheckInAreaService {
  constructor(
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<CheckInArea>> {
    return paginate(query, CheckInArea.getRepository(), CHECK_IN_AREA_PAGINATION_CONFIG);
  }

  async create(createLevelDto: CreateCheckInAreaDto) {

    //check level code exist
    const check = await CheckInArea.existsBy({
      code: createLevelDto?.code,
    })
    if (check) {
      throw new BadRequestException('Name already.')
    }

    const item = CheckInArea.create({ ...createLevelDto });

    return item.save();
  }


  findAll() {
    return CheckInArea.find({
      order: {
        code: 'ASC'
      }
    });
  }

  async findOne(id: number) {
    const item = await CheckInArea.findOne({
      where: { id }
    });

    if (!item) throw new NotFoundException("Data not found.");

    return item;
  }

  async update(id: number, updateLevelDto: UpdateCheckInAreaDto) {
    //check level code exist
    const check = await CheckInArea.existsBy({
      code: updateLevelDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Name already.')
    }

    const item = await this.findOneById(id);

    if (!item) throw new NotFoundException("Data not found");

    return CheckInArea.update(id, {
      ...updateLevelDto,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("Data not found");

    await item.remove();
  }

  findOneById(id: number) {
    return CheckInArea.findOne({ where: { id } });
  }
}
