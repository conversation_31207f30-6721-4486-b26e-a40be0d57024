import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Department } from "../../department/entities/department.entity";

@Entity()
@Unique(['code'])
export class Group extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ default: true })
    active: boolean

    //department
    @OneToMany(() => Department, (_) => _.group)
    departments: Array<Department>;

}


