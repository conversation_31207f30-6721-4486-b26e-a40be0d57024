import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateGroupDto } from './dto/create-group.dto';
import { UpdateGroupDto } from './dto/update-group.dto';
import { Group } from './entities/group.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const GROUP_PAGINATION_CONFIG: PaginateConfig<Group> = {
  sortableColumns: ['id', 'code', 'name','active'],
  select: ['id', 'code', 'name','active', 'createdAt'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class GroupService {
  constructor(
    @InjectRepository(Group)
    private groupRepository: Repository<Group>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Group>> {
    return paginate(query, this.groupRepository, GROUP_PAGINATION_CONFIG);
  }

  async create(createGroupDto: CreateGroupDto) {

    const { ...data } = createGroupDto;

    //check group code exist
    const check = await Group.existsBy({
      code: createGroupDto?.code
    })
    if (check) {
      throw new BadRequestException('group code already.')
    }

    const item = this.groupRepository.create(
      {
        ...data,
      });

    return this.groupRepository.save(item);
  }

  findAll() {
    return this.groupRepository.find();
  }

  async findOne(id: number) {
    const item = await this.groupRepository.findOne({ where: { id } });

    if (!item) throw new NotFoundException("group not found");

    return item;
  }

  async update(id: number, updateGroupDto: UpdateGroupDto) {
    //check group code exist
    const check = await Group.existsBy({
      code: updateGroupDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Group code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("group not found");

    const { ...data } = updateGroupDto;

    return this.groupRepository.update(id, {
      ...data,
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("group not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.groupRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.groupRepository.findOne({ where: { id } });
  }
}

