import { Modu<PERSON> } from '@nestjs/common';
import { ApproveListController } from './approve-list.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApproveList } from './entities/approve-list.entity';
import { Employee } from 'src/employee/entities/employee.entity';
import { ApproveListService } from './approve-list.service';

@Module({
  imports: [TypeOrmModule.forFeature([ApproveList, Employee])],
  controllers: [ApproveListController],
  providers: [ApproveListService],
})
export class ApproveListModule { }
