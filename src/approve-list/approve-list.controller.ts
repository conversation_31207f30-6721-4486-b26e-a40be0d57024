import { Controller, Get, Query } from '@nestjs/common';
import { ApproveListService } from './approve-list.service';

import { ApiTags } from '@nestjs/swagger';

// @Auth()
@Controller('appove-list')
@ApiTags('รายชื่อผู้อนุมัติ')
export class ApproveListController {
  constructor(private readonly approveListService: ApproveListService) { }

  // @Get('datatables')
  // @HttpCode(HttpStatus.OK)
  // @ApiPaginationQuery(APPROVE_LIST_PAGINATION_CONFIG)
  // datatables(@Paginate() query: PaginateQuery) {
  //   return this.approveListService.datatables(query);
  // }

  // @Post()
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       employee: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number', example: 1 },
  //             active: { type: 'boolean', example: true },
  //           },
  //           required: ['id', 'active'],
  //         },
  //         example: [
  //           { id: 1, active: true },
  //           { id: 2, active: true },
  //           { id: 3, active: false },
  //         ],
  //         description: 'List of employees with their ID and active status to add to the approve list',
  //       },
  //     },
  //   },
  // })
  // @Roles(AuthRole.Admin)
  // create(@Body('employee') employees: { id: number; active: boolean }[], createAppoveListDto: CreateApproveListDto) {
  //   return this.approveListService.create(employees, createAppoveListDto);
  // }

  @Get()
  // @ApiQuery({ name: 'employeeId', required: false })
  findAll(@Query() query) {
    return this.approveListService.findAll(query);
  }

  // @Get(':id')
  // findOne(@Param('id', ParseIntPipe) id: string) {
  //   return this.approveListService.findOne(+id);
  // }

  // @Put(':id')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       employee: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             id: { type: 'number', example: 1 },
  //             active: { type: 'boolean', example: true },
  //           },
  //           required: ['id', 'active'],
  //         },
  //         example: [
  //           { id: 1, active: true },
  //           { id: 2, active: true },
  //           { id: 3, active: false },
  //         ],
  //         description: 'List of employees with their ID and active status to add to the approve list',
  //       },
  //     },
  //   },
  // })
  // @Roles(AuthRole.Admin)
  // update(@Param('id', ParseIntPipe) id: string, @Body('employee') employees: { id: number; active: boolean }[], updateApproveListDto: UpdateApproveListDto) {
  //   return this.approveListService.update(+id, employees, updateApproveListDto);
  // }

  // @Delete(':id')
  // @Roles(AuthRole.Admin)
  // remove(@Param('id', ParseIntPipe) id: string) {
  //   return this.approveListService.remove(+id);
  // }
}



