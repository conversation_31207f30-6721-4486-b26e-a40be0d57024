import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { LeaveType } from "../../leave-type/entities/leave-type.entity";
import { Employee } from "../../employee/entities/employee.entity";
import { LeaveDate } from "../../leave/entities/leave-date.entity";

@Entity()

export class ApproveList extends CustomBaseEntity {

    @Column({ default: true })
    active: boolean

    //employee
    @ManyToOne(() => Employee, (_) => _.leaves)
    @JoinColumn({ name: 'employee_id' })
    employee: Employee;
}




