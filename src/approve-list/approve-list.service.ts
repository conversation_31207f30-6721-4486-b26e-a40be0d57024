import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateApproveListDto } from './dto/create-approve-list.dto';
import { UpdateApproveListDto } from './dto/update-approve-list.dto';
import { ApproveList } from './entities/approve-list.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { WorkShiftTime } from 'src/work-shift/entities/work-shift-time.entity';
import { log } from 'console';
import { Employee } from 'src/employee/entities/employee.entity';

export const APPROVE_LIST_PAGINATION_CONFIG: PaginateConfig<ApproveList> = {
  sortableColumns: ['id', 'active'],
  select: ['id', 'active', 'createdAt', 'employee'],
};
@Injectable()
export class ApproveListService {
  constructor(
    @InjectRepository(ApproveList)
    private approveListRepository: Repository<ApproveList>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<ApproveList>> {
    return paginate(query, this.approveListRepository, APPROVE_LIST_PAGINATION_CONFIG);
  }

  async create(employees: { id: number; active: boolean }[], createApproveListDto: CreateApproveListDto) {

    const item = await this.approveListRepository.find(
      {
        relations: ['employee'],
      });

    if (!item) throw new NotFoundException("approved list not found");

    //del approved list
    item.forEach(async (item) => {
      const deleteResponse = await this.approveListRepository.softDelete(item.id);
    });

    //add approved list time
    for (let i = 0; i < employees.length; i++) {

      const employee = await this.employeeRepository.findOne({ where: { id: employees[i].id } })

      const approveList = new ApproveList();

      approveList.employee = employee
      approveList.active = true;

      this.approveListRepository.save(approveList);
    }

    return null
  }

  async findAll(query) {
    return Employee.find({
      where: {
        isApprover: true
      },
      order: {
        firstname: 'ASC',
        lastname: 'ASC'
      },
      relations: {
        head: true
      }
    })
  }

  async findOne(id: number) {
    const item = await this.approveListRepository.findOne(
      {
        relations: ['employee'],
        where: { id }
      });

    if (!item) throw new NotFoundException("approved list not found");

    return item;
  }

  // async update(id: number, employees: { id: number; active: boolean }[], updateApproveListDto: UpdateApproveListDto) {

  //   const item = await this.approveListRepository.find(
  //     {
  //       relations: ['employee'],
  //     });

  //   if (!item) throw new NotFoundException("approved list not found");

  //   //del approved list
  //   item.forEach(async (item) => {
  //     const deleteResponse = await this.approveListRepository.softDelete(item.id);
  //   });

  //   //add approved list time
  //   for (let i = 0; i < employees.length; i++) {

  //     const employee = await this.employeeRepository.findOne({ where: { id: employees[i].id } })

  //     const approveList = new ApproveList();

  //     approveList.employee = employee
  //     approveList.active = true;

  //     this.approveListRepository.save(approveList);
  //   }

  //   return null
  // }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("approved list not found");

    await this.approveListRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.approveListRepository.findOne({ where: { id } });
  }
}

