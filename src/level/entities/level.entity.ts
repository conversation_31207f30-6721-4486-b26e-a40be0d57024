import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Employee } from "../../employee/entities/employee.entity";
import { LevelType } from "../../level-type/entities/level-type.entity";
import { DecimalColumnTransformer } from "src/common/utils/decimal-column-transformer";

@Entity()
@Unique(['code'])
export class Level extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    description: string;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyDayOtWorkMin: number;

    @Column("numeric", { precision: 10, scale: 2, default: 0.00, nullable: true, transformer: new DecimalColumnTransformer() })
    qtyDayOtOffMin: number;


    @Column({ default: true })
    active: boolean

    //LevelType
    @ManyToOne(() => LevelType, (_) => _.levels)
    @JoinColumn({ name: 'level_type_id' })
    levelType: LevelType;

    //employee
    @OneToMany(() => Employee, (_) => _.level)
    employees: Array<Employee>;




}



