import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
export class CreateLevelDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly description: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly levelTypeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly qtyDayOtWorkMin: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly qtyDayOtWorkMax: number;
}




