import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { Level } from './entities/level.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const LEVEL_PAGINATION_CONFIG: PaginateConfig<Level> = {
  relations: ['levelType'],
  sortableColumns: ['id', 'code', 'name', 'description', 'active', 'levelType.id', 'levelType.name', 'levelType.code'],
  select: ['id', 'code', 'name', 'description', 'active', 'createdAt', 'levelType.id', 'levelType.name', 'levelType.code'],
  searchableColumns: ['code', 'name']
};
@Injectable()
export class LevelService {
  constructor(
    @InjectRepository(Level)
    private levelRepository: Repository<Level>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Level>> {
    return paginate(query, this.levelRepository, LEVEL_PAGINATION_CONFIG);
  }

  async create(createLevelDto: CreateLevelDto) {

    const { levelTypeId, ...data } = createLevelDto;

    //check level code exist
    const check = await Level.existsBy({
      code: createLevelDto?.code
    })
    if (check) {
      throw new BadRequestException('level code already.')
    }

    const item = this.levelRepository.create(
      {
        ...data,
        levelType: { id: levelTypeId },
      });

    return this.levelRepository.save(item);
  }


  findAll(query) {
    const levelTypeId = query.levelTypeId

    return this.levelRepository.find({
      relations: ['levelType'],
      where: {
        levelType: {
          id: levelTypeId ? levelTypeId : null,
        }
      }
    });
  }

  async findOne(id: number) {
    const item = await this.levelRepository.findOne({
      relations: ['levelType'],
      where: { id }
    });

    if (!item) throw new NotFoundException("level not found");

    return item;
  }

  async update(id: number, updateLevelDto: UpdateLevelDto) {
    //check level code exist
    const check = await Level.existsBy({
      code: updateLevelDto?.code,
      id: Not(id)
    })
    if (check) {
      throw new BadRequestException('Level code already.')
    }

    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("level not found");

    const { levelTypeId, ...data } = updateLevelDto;

    return this.levelRepository.update(id, {
      ...data,
      levelType: { id: levelTypeId },
    });
  }

  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("level not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.levelRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.levelRepository.findOne({ where: { id } });
  }
}

