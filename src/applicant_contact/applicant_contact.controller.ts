import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApplicantContactService } from './applicant_contact.service';
import { CreateApplicantContactDto } from './dto/create-applicant_contact.dto';
import { UpdateApplicantContactDto } from './dto/update-applicant_contact.dto';

@Controller('applicant-contact')
export class ApplicantContactController {
  constructor(private readonly applicantContactService: ApplicantContactService) {}

  @Post()
  create(@Body() createApplicantContactDto: CreateApplicantContactDto) {
    return this.applicantContactService.create(createApplicantContactDto);
  }

  @Get()
  findAll() {
    return this.applicantContactService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.applicantContactService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateApplicantContactDto: UpdateApplicantContactDto) {
    return this.applicantContactService.update(+id, updateApplicantContactDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.applicantContactService.remove(+id);
  }
}
