import { Injectable } from '@nestjs/common';
import { CreateApplicantContactDto } from './dto/create-applicant_contact.dto';
import { UpdateApplicantContactDto } from './dto/update-applicant_contact.dto';

@Injectable()
export class ApplicantContactService {
  create(createApplicantContactDto: CreateApplicantContactDto) {
    return 'This action adds a new applicantContact';
  }

  findAll() {
    return `This action returns all applicantContact`;
  }

  findOne(id: number) {
    return `This action returns a #${id} applicantContact`;
  }

  update(id: number, updateApplicantContactDto: UpdateApplicantContactDto) {
    return `This action updates a #${id} applicantContact`;
  }

  remove(id: number) {
    return `This action removes a #${id} applicantContact`;
  }
}
