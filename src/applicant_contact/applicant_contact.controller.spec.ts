import { Test, TestingModule } from '@nestjs/testing';
import { ApplicantContactController } from './applicant_contact.controller';
import { ApplicantContactService } from './applicant_contact.service';

describe('ApplicantContactController', () => {
  let controller: ApplicantContactController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicantContactController],
      providers: [ApplicantContactService],
    }).compile();

    controller = module.get<ApplicantContactController>(ApplicantContactController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
