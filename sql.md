```sql
INSERT INTO permission_group (id, name, created_at, updated_at)
VALUES 
  (1, 'Admin', NOW(), NOW()),
  (2, 'Human Resources', NOW(), NOW()),
  (3, 'Leave', NOW(), NOW()),
  (4, 'Overtime', NOW(), NOW()),
  (5, 'Project', NOW(), NOW()),
  (6, 'Report', NOW(), NOW()),
  (7, 'Client List', NOW(), NOW()),
  (8, 'Time Attendance', NOW(), NOW()),
  (9, 'Recruitment', NOW(), NOW());
```

```sql
-- Admin
INSERT INTO permission (name, description, permission_group_id, created_at, updated_at) VALUES
('ADMIN_USER_READ', 'Read user', 1, NOW(), NOW()),
('ADMIN_USER_CREATE', 'Create user', 1, NOW(), NOW()),
('ADMIN_USER_UPDATE', 'Update user', 1, NOW(), NOW()),
('ADMIN_USER_DELETE', 'Delete user', 1, NOW(), NOW()),
('ADMIN_SITE_READ', 'Read site', 1, NOW(), NOW()),
('ADMIN_SITE_CREATE', 'Create site', 1, NOW(), NOW()),
('ADMIN_SITE_UPDATE', 'Update site', 1, NOW(), NOW()),
('ADMIN_SITE_DELETE', 'Delete site', 1, NOW(), NOW());

-- Human Resources - Employee
INSERT INTO permission (name, description, permission_group_id, created_at, updated_at) VALUES
('HR_EMPLOYEE_READ', 'Read employee', 2, NOW(), NOW()),
('HR_EMPLOYEE_CREATE', 'Create employee', 2, NOW(), NOW()),
('HR_EMPLOYEE_UPDATE', 'Update employee', 2, NOW(), NOW()),
('HR_EMPLOYEE_DELETE', 'Delete employee', 2, NOW(), NOW()),
('HR_EMPLOYEE_SETTING_READ', 'Read employee setting', 2, NOW(), NOW()),
('HR_EMPLOYEE_SETTING_CREATE', 'Create employee setting', 2, NOW(), NOW()),
('HR_EMPLOYEE_SETTING_UPDATE', 'Update employee setting', 2, NOW(), NOW()),
('HR_EMPLOYEE_SETTING_DELETE', 'Delete employee setting', 2, NOW(), NOW()),
('HR_EMPLOYEE_TYPE_READ', 'Read employee type', 2, NOW(), NOW()),
('HR_EMPLOYEE_TYPE_CREATE', 'Create employee type', 2, NOW(), NOW()),
('HR_EMPLOYEE_TYPE_UPDATE', 'Update employee type', 2, NOW(), NOW()),
('HR_EMPLOYEE_TYPE_DELETE', 'Delete employee type', 2, NOW(), NOW()),
('HR_EMPLOYEE_TITLE_READ', 'Read employee title', 2, NOW(), NOW()),
('HR_EMPLOYEE_TITLE_CREATE', 'Create employee title', 2, NOW(), NOW()),
('HR_EMPLOYEE_TITLE_UPDATE', 'Update employee title', 2, NOW(), NOW()),
('HR_EMPLOYEE_TITLE_DELETE', 'Delete employee title', 2, NOW(), NOW()),
('HR_EMPLOYEE_GROUP_READ', 'Read employee group', 2, NOW(), NOW()),
('HR_EMPLOYEE_GROUP_CREATE', 'Create employee group', 2, NOW(), NOW()),
('HR_EMPLOYEE_GROUP_UPDATE', 'Update employee group', 2, NOW(), NOW()),
('HR_EMPLOYEE_GROUP_DELETE', 'Delete employee group', 2, NOW(), NOW()),
('HR_EMPLOYEE_DEPARTMENT_READ', 'Read department', 2, NOW(), NOW()),
('HR_EMPLOYEE_DEPARTMENT_CREATE', 'Create department', 2, NOW(), NOW()),
('HR_EMPLOYEE_DEPARTMENT_UPDATE', 'Update department', 2, NOW(), NOW()),
('HR_EMPLOYEE_DEPARTMENT_DELETE', 'Delete department', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_READ', 'Read employee level', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_CREATE', 'Create employee level', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_UPDATE', 'Update employee level', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_DELETE', 'Delete employee level', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_TYPE_READ', 'Read level type', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_TYPE_CREATE', 'Create level type', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_TYPE_UPDATE', 'Update level type', 2, NOW(), NOW()),
('HR_EMPLOYEE_LEVEL_TYPE_DELETE', 'Delete level type', 2, NOW(), NOW()),
('HR_EMPLOYEE_APPROVE_LIST_READ', 'Read approve list', 2, NOW(), NOW()),
('HR_EMPLOYEE_APPROVE_LIST_CREATE', 'Create approve list', 2, NOW(), NOW()),
('HR_EMPLOYEE_APPROVE_LIST_UPDATE', 'Update approve list', 2, NOW(), NOW()),
('HR_EMPLOYEE_APPROVE_LIST_DELETE', 'Delete approve list', 2, NOW(), NOW()),
('HR_EMPLOYEE_COMPANY_READ', 'Read employee company', 2, NOW(), NOW()),
('HR_EMPLOYEE_COMPANY_CREATE', 'Create employee company', 2, NOW(), NOW()),
('HR_EMPLOYEE_COMPANY_UPDATE', 'Update employee company', 2, NOW(), NOW()),
('HR_EMPLOYEE_COMPANY_DELETE', 'Delete employee company', 2, NOW(), NOW());

-- Leave
INSERT INTO permission (name, description, permission_group_id, created_at, updated_at) VALUES
('HR_LEAVE_READ', 'Read leave', 3, NOW(), NOW()),
('HR_LEAVE_CREATE', 'Create leave', 3, NOW(), NOW()),
('HR_LEAVE_UPDATE', 'Update leave', 3, NOW(), NOW()),
('HR_LEAVE_DELETE', 'Delete leave', 3, NOW(), NOW()),
('HR_LEAVE_SETTING_READ', 'Read leave setting', 3, NOW(), NOW()),
('HR_LEAVE_SETTING_CREATE', 'Create leave setting', 3, NOW(), NOW()),
('HR_LEAVE_SETTING_UPDATE', 'Update leave setting', 3, NOW(), NOW()),
('HR_LEAVE_SETTING_DELETE', 'Delete leave setting', 3, NOW(), NOW()),
('HR_LEAVE_TYPE_READ', 'Read leave type', 3, NOW(), NOW()),
('HR_LEAVE_TYPE_CREATE', 'Create leave type', 3, NOW(), NOW()),
('HR_LEAVE_TYPE_UPDATE', 'Update leave type', 3, NOW(), NOW()),
('HR_LEAVE_TYPE_DELETE', 'Delete leave type', 3, NOW(), NOW()),
('HR_WORK_SHIFT_READ', 'Read work shift', 3, NOW(), NOW()),
('HR_WORK_SHIFT_CREATE', 'Create work shift', 3, NOW(), NOW()),
('HR_WORK_SHIFT_UPDATE', 'Update work shift', 3, NOW(), NOW()),
('HR_WORK_SHIFT_DELETE', 'Delete work shift', 3, NOW(), NOW()),
('HR_COMPANY_HOLIDAY_READ', 'Read company holiday', 3, NOW(), NOW()),
('HR_COMPANY_HOLIDAY_CREATE', 'Create company holiday', 3, NOW(), NOW()),
('HR_COMPANY_HOLIDAY_UPDATE', 'Update company holiday', 3, NOW(), NOW()),
('HR_COMPANY_HOLIDAY_DELETE', 'Delete company holiday', 3, NOW(), NOW());
```