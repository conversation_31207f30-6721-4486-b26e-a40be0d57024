services:
  db:
    image: postgres:17
    restart: always
    environment:
      TZ: ${TZ}
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_PASSWORD: ${DB_PASS}
    ports:
      - "5432:5432"
    volumes:
      - postgresql_data:/var/lib/postgresql/data

  api:
    build: .
    restart: always
    volumes:
      - ./uploads:/usr/src/app/uploads
      - ./logs:/usr/src/app/logs
    env_file:
      - .env
    ports:
      - "${APP_PORT}:3000"
    depends_on:
      - db

volumes:
  postgresql_data:
