# การตรวจสอบเงื่อนไข OT (OT Condition Validation)

## ภาพรวม

ระบบได้เพิ่มการตรวจสอบเงื่อนไข OT เมื่อมีการขอ OT และแก้ไข OT โดยจะตรวจสอบตามเงื่อนไขที่กำหนดไว้ในตาราง `ot_condition`

## การทำงานของระบบ

### 1. การตรวจสอบประเภทของวัน
ระบบจะตรวจสอบประเภทของวันที่ขอ OT:
- **WORK**: วันทำงานปกติ
- **OFF**: วันหยุดประจำสัปดาห์
- **HOLIDAY**: วันหยุดนักขัตฤกษ์

### 2. การค้นหาเงื่อนไข OT
ระบบจะค้นหาเงื่อนไข OT ที่เหมาะสมตาม:
- แผนกของพนักงาน (Department)
- ประเภทพนักงาน (Employee Type)
- ประเภทของวัน (Day Type)

### 3. การคำนวณชั่วโมง OT
ระบบจะคำนวณชั่วโมง OT ตามเงื่อนไข:
- หาก `deduct = true`: ใช้ `Helper.calculateHours()` (หักเวลาพัก)
- หาก `deduct = false`: ใช้ `Helper.calculateHoursWithoutBreak()` (ไม่หักเวลาพัก)

### 4. การตรวจสอบเวลาขั้นต่ำ
ระบบจะตรวจสอบว่าเวลา OT ที่ขอมีความยาวไม่น้อยกว่า `qtyOtMin` (นาที)

## ตัวอย่างการใช้งาน

### การตั้งค่าเงื่อนไข OT

```json
{
  "departmentId": 1,
  "employeeTypeId": 1,
  "type": "work",
  "qtyOtMin": 30,
  "deduct": false,
  "active": true
}
```

### การขอ OT

เมื่อพนักงานขอ OT ระบบจะ:

1. ตรวจสอบว่ามีเงื่อนไข OT สำหรับแผนกและประเภทพนักงานหรือไม่
2. คำนวณชั่วโมง OT ตามเงื่อนไข
3. ตรวจสอบเวลาขั้นต่ำ
4. บันทึกข้อมูล OT หากผ่านการตรวจสอบทั้งหมด

### ข้อผิดพลาดที่อาจเกิดขึ้น

```json
{
  "statusCode": 400,
  "message": "ไม่พบเงื่อนไข OT สำหรับแผนก IT ประเภทพนักงาน Full-time ในวันwork"
}
```

```json
{
  "statusCode": 400,
  "message": "เวลา OT ต้องไม่น้อยกว่า 30 นาที (0.50 ชั่วโมง)"
}
```

## การปรับปรุงที่ทำ

### 1. OT Service (`src/ot/ot.service.ts`)
- เพิ่มฟังก์ชัน `validateAndCalculateOtHours()`
- ปรับปรุงฟังก์ชัน `create()` และ `update()`
- เพิ่ม dependency `OtConditionService`

### 2. OT Air Service (`src/ot-air/ot-air.service.ts`)
- เพิ่มฟังก์ชัน `validateAndCalculateOtHours()`
- ปรับปรุงฟังก์ชัน `create()`
- เพิ่ม dependency `EmployeeService` และ `OtConditionService`

### 3. Module Updates
- อัปเดต `OtModule` และ `OtAirModule` เพื่อเพิ่ม dependencies ที่จำเป็น

## ข้อดีของการปรับปรุง

1. **ความถูกต้อง**: ตรวจสอบเงื่อนไข OT ตามที่กำหนดไว้
2. **ความยืดหยุ่น**: สามารถกำหนดเงื่อนไขต่างๆ ตามแผนกและประเภทพนักงาน
3. **ความปลอดภัย**: ป้องกันการขอ OT ที่ไม่เป็นไปตามเงื่อนไข
4. **ความสอดคล้อง**: ใช้เงื่อนไขเดียวกันทั้ง OT และ OT Air

## การทดสอบ

### Test Case 1: การขอ OT ในวันทำงาน
```typescript
// ตั้งค่าเงื่อนไข: วันทำงาน, ขั้นต่ำ 30 นาที, ไม่หักเวลาพัก
const condition = {
  departmentId: 1,
  employeeTypeId: 1,
  type: 'work',
  qtyOtMin: 30,
  deduct: false
};

// ขอ OT 1 ชั่วโมง - ควรผ่าน
const otRequest = {
  employeeId: 1,
  date: '2024-01-15',
  timeStart: '18:00',
  timeEnd: '19:00'
};
```

### Test Case 2: การขอ OT น้อยกว่าเวลาขั้นต่ำ
```typescript
// ขอ OT 15 นาที - ควรไม่ผ่าน
const otRequest = {
  employeeId: 1,
  date: '2024-01-15',
  timeStart: '18:00',
  timeEnd: '18:15'
};
// Expected: "เวลา OT ต้องไม่น้อยกว่า 30 นาที"
```

### Test Case 3: ไม่มีเงื่อนไข OT
```typescript
// ขอ OT โดยไม่มีเงื่อนไขที่ตั้งไว้ - ควรไม่ผ่าน
const otRequest = {
  employeeId: 999, // พนักงานที่ไม่มีเงื่อนไข OT
  date: '2024-01-15',
  timeStart: '18:00',
  timeEnd: '19:00'
};
// Expected: "ไม่พบเงื่อนไข OT สำหรับแผนก..."
```
