name: deploy

on:
  push:
    branches: ['develop']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh
            cd /home/<USER>/node_server/asha-hrm-backend
            git pull
            npm install
            npm run build
            npm run pm2:restart
