{"name": "asha-hrm-backend", "version": "0.2.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "pm2:start": "pm2 start dist/main.js --name hrm-mh", "pm2:stop": "pm2 stop hrm-mh", "pm2:restart": "pm2 restart hrm-mh", "deploy": "npm run build && npm run pm2:restart", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/common/config/typeorm.ts", "migration:generate": "npm run typeorm -- -d ./src/common/config/typeorm.ts migration:generate ./src/common/database/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./src/common/database/migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./src/common/config/typeorm.ts migration:revert", "seed": "ts-node ./node_modules/typeorm-extension/bin/cli.cjs seed:run -d ./src/common/config/typeorm.ts"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.1", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^10.0.2", "@types/lodash": "^4.17.12", "@types/luxon": "^3.4.2", "argon2": "^0.41.1", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "exceljs": "^4.4.0", "express-basic-auth": "^1.2.1", "handlebars": "^4.7.8", "helmet": "^8.0.0", "ldap-authentication": "^3.2.4", "lodash": "^4.17.21", "luxon": "^3.5.0", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.7", "nestjs-paginate": "^8.6.0", "nodemailer": "^6.9.15", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "puppeteer": "^24.2.0", "randomstring": "^1.3.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-extension": "^3.6.2", "typeorm-naming-strategies": "^4.1.0", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5", "yargs": "^17.7.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "nestjs-command": "^3.1.4", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}